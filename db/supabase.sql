-- 创建真题表
CREATE TABLE IF NOT EXISTS papers (
  id BIGSERIAL PRIMARY KEY,
  year VARCHAR(10) NOT NULL,
  type VARCHAR(50) NOT NULL,
  section_type VARCHAR(50) NOT NULL,
  task_type VARCHAR(50),
  content TEXT,
  reference_translation TEXT,
  ai_reference TEXT,
  writing_analysis TEXT,
  translation_analysis TEXT,
  image_url VARCHAR(255),
  image_description TEXT,
  is_public BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_papers_year_type ON papers(year, type);
CREATE INDEX idx_papers_section_type ON papers(section_type);
CREATE INDEX idx_papers_is_public ON papers(is_public);

-- 创建句子表
CREATE TABLE IF NOT EXISTS sentences (
  id BIGSERIAL PRIMARY KEY,
  article_id BIGINT NOT NULL REFERENCES papers(id) ON DELETE CASCADE,
  explain_md TEXT,
  paragraph_num INTEGER NOT NULL DEFAULT 1,
  sequence INTEGER NOT NULL DEFAULT 1,
  content TEXT NOT NULL,
  is_marked BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_sentences_article_id ON sentences(article_id);
CREATE INDEX idx_sentences_paragraph_sequence ON sentences(paragraph_num, sequence);
CREATE INDEX idx_sentences_is_marked ON sentences(is_marked);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为每个表添加更新时间触发器
CREATE TRIGGER update_papers_updated_at
    BEFORE UPDATE ON papers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sentences_updated_at
    BEFORE UPDATE ON sentences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_profiles_updated_at
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_activation_codes_updated_at
    BEFORE UPDATE ON activation_codes
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 示例数据
INSERT INTO papers (year, type, section_type, content, reference_translation, translation_analysis, is_public, preview_content) VALUES
('2023', '英语一', 'reading_comprehension', NULL, NULL, NULL, TRUE, '这是一篇阅读理解文章...'),
('2023', '英语二', 'translation', 
'Climate change is one of the most pressing challenges of our time, requiring immediate and coordinated global action. Rising temperatures, changing precipitation patterns, and increasingly frequent extreme weather events are already affecting ecosystems, agriculture, and human settlements worldwide. Despite the overwhelming scientific consensus on the human causes of climate change, political and economic barriers continue to impede effective responses. Addressing this crisis will require not only technological innovation and policy reform but also fundamental changes in consumption patterns and economic systems.',
'气候变化是我们这个时代最紧迫的挑战之一，需要立即采取协调一致的全球行动。气温上升、降水模式改变以及日益频繁的极端天气事件已经在全球范围内影响着生态系统、农业和人类聚居地。尽管在气候变化的人为原因上存在压倒性的科学共识，但政治和经济障碍仍然阻碍着有效的应对措施。应对这一危机不仅需要技术创新和政策改革，还需要消费模式和经济系统的根本性变革。',
'翻译难点分析：\n1. 专业术语：如"climate change"、"precipitation patterns"、"extreme weather events"等环境领域专业术语的准确翻译\n2. 长句结构：第二句包含多个并列成分，需要在汉语中保持流畅\n3. 抽象概念：如"political and economic barriers"、"consumption patterns"等抽象概念的表达\n\n翻译技巧：\n1. 术语对应：使用领域内公认的术语翻译\n2. 分句处理：适当将英语长句在汉语中分成较短的句子\n3. 调整语序：按照汉语的表达习惯调整句子成分顺序',
TRUE,
'这是一篇关于气候变化的翻译题目...');

-- 添加示例激活码
INSERT INTO activation_codes (code, is_used, expires_at) VALUES
('XYZ123', FALSE, CURRENT_TIMESTAMP + INTERVAL '1 year'),
('ABC456', FALSE, CURRENT_TIMESTAMP + INTERVAL '1 year'); 