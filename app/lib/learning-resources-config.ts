// 学习资源配置文件
export interface ResourceConfig {
  [sectionName: string]: string; // section名称 -> 存储路径
}

// 嵌套的section配置接口
export interface NestedSectionConfig {
  [key: string]: string | NestedSectionConfig;
}

// 资源类型完整配置接口
export interface ResourceTypeConfig {
  id: string;
  label: string;
  icon: string;
  needsApi: boolean; // 是否需要调用API获取资源
  defaultSection?: string; // 默认选择的section
  description?: string; // 描述信息
  specialComponent?: "print" | "custom"; // 特殊组件类型
  sections?: string[]; // 可用的sections（简单模式）
  nestedSections?: NestedSectionConfig; // 嵌套sections（复杂模式）
}

// 分类图标配置
export const CATEGORY_ICONS: Record<string, string> = {
  考研英语一: "🎯",
  考研英语二: "📚",
  考研政治: "📜",
  考研数学一: "📐",
  考研数学二: "📏",
  考研数学三: "🔢",
  考研专业课: "📊",
  考研计算机408: "💻",
  考研管综199: "📈",
  考研经济396: "📋",
  考研西综306: "🩺",
  考研中综307: "🏥",
  考研教育学311: "🎓",
  考研教育综合333: "📖",
  考研心理学312: "🧠",
  考研答题卡: "📝",
  学习计划表: "📅",
  四级: "🔍",
  六级: "📝",
  专四: "📘",
  专八: "📗",
  大学生英语竞赛: "🏆",
  考研历史学313: "📜",
  // 教资相关
  小学教资: "👨‍🏫",
  中学教资: "👩‍🏫",
  幼儿园教资: "🧸",
};

// Section图标配置
export const SECTION_ICONS: Record<string, string> = {
  真题: "📄",
  答案解析: "✏️",
  资料: "📑",
  核心词汇: "📚",
  公式: "📊",
  打印模板: "🖨️",
  手译本: "✍️",
  计划模板: "📅",
  音频: "🎵",
  "25年06月真题及解析": "🎯",
  四级分类作文模板: "📝",
  六级分类作文模板: "📝",
  四级作文预测: "🔮",
  六级作文预测: "🔮",
  翻译句式及模板: "🔄",
  经典长难句50句: "📖",
  高频词汇与短语搭配: "🔗",
  核心词汇大纲: "📋",
  写作模板与句型表达: "📝",
  专项训练资料: "🎯",
  真题及解析: "📄",
  视频: "🎬",
  // 教资相关section图标
  资料包: "📦",
  面试资料: "🎤",
  试讲模板: "📋",
  结构化问答: "💬",
  笔试资料包: "📚",
  科目一真题及解析: "📝",
  科目二真题及解析: "📖",
  科目三真题及解析: "📊",
  科目一真题: "📄",
  科目一答案解析: "✏️",
  科目二真题: "📄",
  科目二答案解析: "✏️",
  科目三真题: "📄",
  科目三答案解析: "✏️",
  历年真题: "📄",
  综合素质: "📋",
  保教知识与能力: "🧸",
  教育教学知识与能力: "🎓",
  教育知识与能力: "📚",
  "科目一（综合素质）": "📋",
  "科目二（保教知识与能力）": "🧸",
  "科目二（教育教学知识与能力）": "🎓",
  "科目二（教育知识与能力）": "📚",
  // 中学科目三各学科图标
  政治: "⚖️",
  美术: "🎨",
  历史: "📜",
  生物: "🧬",
  化学: "🧪",
  英语: "🔤",
  地理: "🌍",
  音乐: "🎵",
  体育: "⚽",
  物理: "⚛️",
  信息技术: "💻",
  语文: "📚",
  数学: "🔢",
};

// 分类样式配置
export const CATEGORY_STYLES: Record<string, string> = {
  考研英语一:
    "bg-blue-50 text-blue-600 border border-blue-100 hover:bg-blue-100",
  考研英语二:
    "bg-indigo-50 text-indigo-600 border border-indigo-100 hover:bg-indigo-100",
  考研政治: "bg-red-50 text-red-600 border border-red-100 hover:bg-red-100",
  考研数学一:
    "bg-purple-50 text-purple-600 border border-purple-100 hover:bg-purple-100",
  考研数学二:
    "bg-fuchsia-50 text-fuchsia-600 border border-fuchsia-100 hover:bg-fuchsia-100",
  考研数学三:
    "bg-violet-50 text-violet-600 border border-violet-100 hover:bg-violet-100",
  考研专业课:
    "bg-orange-50 text-orange-600 border border-orange-100 hover:bg-orange-100",
  考研计算机408:
    "bg-sky-50 text-sky-600 border border-sky-100 hover:bg-sky-100",
  考研管综199:
    "bg-pink-50 text-pink-600 border border-pink-100 hover:bg-pink-100",
  考研经济396:
    "bg-rose-50 text-rose-600 border border-rose-100 hover:bg-rose-100",
  考研西综306:
    "bg-emerald-50 text-emerald-600 border border-emerald-100 hover:bg-emerald-100",
  考研中综307:
    "bg-teal-50 text-teal-600 border border-teal-100 hover:bg-teal-100",
  考研教育学311:
    "bg-slate-50 text-slate-600 border border-slate-100 hover:bg-slate-100",
  考研教育综合333:
    "bg-teal-50 text-teal-600 border border-teal-100 hover:bg-teal-100",
  考研心理学312:
    "bg-pink-50 text-pink-600 border border-pink-100 hover:bg-pink-100",
  考研历史学313:
    "bg-stone-50 text-stone-600 border border-stone-100 hover:bg-stone-100",
  考研答题卡:
    "bg-yellow-50 text-yellow-600 border border-yellow-100 hover:bg-yellow-100",
  学习计划表:
    "bg-green-50 text-green-600 border border-green-100 hover:bg-green-100",
  四级: "bg-cyan-50 text-cyan-600 border border-cyan-100 hover:bg-cyan-100",
  六级: "bg-teal-50 text-teal-600 border border-teal-100 hover:bg-teal-100",
  专四: "bg-emerald-50 text-emerald-600 border border-emerald-100 hover:bg-emerald-100",
  专八: "bg-lime-50 text-lime-600 border border-lime-100 hover:bg-lime-100",
  大学生英语竞赛:
    "bg-amber-50 text-amber-600 border border-amber-100 hover:bg-amber-100",
  // 教资相关样式
  小学教资: "bg-blue-50 text-blue-600 border border-blue-100 hover:bg-blue-100",
  中学教资:
    "bg-purple-50 text-purple-600 border border-purple-100 hover:bg-purple-100",
  幼儿园教资:
    "bg-pink-50 text-pink-600 border border-pink-100 hover:bg-pink-100",
};

// Section样式配置
export const SECTION_STYLES: Record<string, string> = {
  真题: "bg-green-50 text-green-600 border border-green-100 hover:bg-green-100",
  答案解析:
    "bg-yellow-50 text-yellow-700 border border-yellow-100 hover:bg-yellow-100",
  资料: "bg-pink-50 text-pink-600 border border-pink-100 hover:bg-pink-100",
  核心词汇:
    "bg-purple-50 text-purple-600 border border-purple-100 hover:bg-purple-100",
  公式: "bg-blue-50 text-blue-600 border border-blue-100 hover:bg-blue-100",
  打印模板:
    "bg-amber-50 text-amber-700 border border-amber-100 hover:bg-amber-100",
  手译本: "bg-teal-50 text-teal-600 border border-teal-100 hover:bg-teal-100",
  计划模板:
    "bg-emerald-50 text-emerald-600 border border-emerald-100 hover:bg-emerald-100",
  音频: "bg-violet-50 text-violet-600 border border-violet-100 hover:bg-violet-100",
  "25年06月真题及解析":
    "bg-red-50 text-red-600 border border-red-100 hover:bg-red-100",
  四级分类作文模板:
    "bg-indigo-50 text-indigo-600 border border-indigo-100 hover:bg-indigo-100",
  六级分类作文模板:
    "bg-indigo-50 text-indigo-600 border border-indigo-100 hover:bg-indigo-100",
  四级作文预测:
    "bg-purple-50 text-purple-600 border border-purple-100 hover:bg-purple-100",
  六级作文预测:
    "bg-purple-50 text-purple-600 border border-purple-100 hover:bg-purple-100",
  翻译句式及模板:
    "bg-cyan-50 text-cyan-600 border border-cyan-100 hover:bg-cyan-100",
  经典长难句50句:
    "bg-rose-50 text-rose-600 border border-rose-100 hover:bg-rose-100",
  高频词汇与短语搭配:
    "bg-orange-50 text-orange-600 border border-orange-100 hover:bg-orange-100",
  核心词汇大纲:
    "bg-slate-50 text-slate-600 border border-slate-100 hover:bg-slate-100",
  写作模板与句型表达:
    "bg-fuchsia-50 text-fuchsia-600 border border-fuchsia-100 hover:bg-fuchsia-100",
  专项训练资料:
    "bg-lime-50 text-lime-600 border border-lime-100 hover:bg-lime-100",
  真题及解析:
    "bg-green-50 text-green-600 border border-green-100 hover:bg-green-100",
  视频: "bg-purple-50 text-purple-600 border border-purple-100 hover:bg-purple-100",
  // 教资相关section样式
  资料包: "bg-blue-50 text-blue-600 border border-blue-100 hover:bg-blue-100",
  面试资料:
    "bg-orange-50 text-orange-600 border border-orange-100 hover:bg-orange-100",
  试讲模板:
    "bg-green-50 text-green-600 border border-green-100 hover:bg-green-100",
  结构化问答:
    "bg-purple-50 text-purple-600 border border-purple-100 hover:bg-purple-100",
  笔试资料包:
    "bg-indigo-50 text-indigo-600 border border-indigo-100 hover:bg-indigo-100",
  科目一真题及解析:
    "bg-emerald-50 text-emerald-600 border border-emerald-100 hover:bg-emerald-100",
  科目二真题及解析:
    "bg-teal-50 text-teal-600 border border-teal-100 hover:bg-teal-100",
  科目三真题及解析:
    "bg-cyan-50 text-cyan-600 border border-cyan-100 hover:bg-cyan-100",
  科目一真题:
    "bg-green-50 text-green-600 border border-green-100 hover:bg-green-100",
  科目一答案解析:
    "bg-yellow-50 text-yellow-700 border border-yellow-100 hover:bg-yellow-100",
  科目二真题:
    "bg-green-50 text-green-600 border border-green-100 hover:bg-green-100",
  科目二答案解析:
    "bg-yellow-50 text-yellow-700 border border-yellow-100 hover:bg-yellow-100",
  科目三真题:
    "bg-green-50 text-green-600 border border-green-100 hover:bg-green-100",
  科目三答案解析:
    "bg-yellow-50 text-yellow-700 border border-yellow-100 hover:bg-yellow-100",
  历年真题:
    "bg-green-50 text-green-600 border border-green-100 hover:bg-green-100",
  综合素质: "bg-blue-50 text-blue-600 border border-blue-100 hover:bg-blue-100",
  保教知识与能力:
    "bg-pink-50 text-pink-600 border border-pink-100 hover:bg-pink-100",
  教育教学知识与能力:
    "bg-purple-50 text-purple-600 border border-purple-100 hover:bg-purple-100",
  教育知识与能力:
    "bg-indigo-50 text-indigo-600 border border-indigo-100 hover:bg-indigo-100",
  // 中学科目三各学科样式
  政治: "bg-red-50 text-red-600 border border-red-100 hover:bg-red-100",
  美术: "bg-pink-50 text-pink-600 border border-pink-100 hover:bg-pink-100",
  历史: "bg-amber-50 text-amber-600 border border-amber-100 hover:bg-amber-100",
  生物: "bg-green-50 text-green-600 border border-green-100 hover:bg-green-100",
  化学: "bg-orange-50 text-orange-600 border border-orange-100 hover:bg-orange-100",
  英语: "bg-blue-50 text-blue-600 border border-blue-100 hover:bg-blue-100",
  地理: "bg-emerald-50 text-emerald-600 border border-emerald-100 hover:bg-emerald-100",
  音乐: "bg-violet-50 text-violet-600 border border-violet-100 hover:bg-violet-100",
  体育: "bg-lime-50 text-lime-600 border border-lime-100 hover:bg-lime-100",
  物理: "bg-indigo-50 text-indigo-600 border border-indigo-100 hover:bg-indigo-100",
  信息技术: "bg-sky-50 text-sky-600 border border-sky-100 hover:bg-sky-100",
  语文: "bg-slate-50 text-slate-600 border border-slate-100 hover:bg-slate-100",
  数学: "bg-cyan-50 text-cyan-600 border border-cyan-100 hover:bg-cyan-100",
};

// 资源路径映射配置
export const RESOURCE_PATHS: Record<string, ResourceConfig> = {
  // 考研英语一
  kaoyan1: {
    真题: "kaoyan/English1/pastExamPapers",
    答案解析: "kaoyan/English1/Explanation",
    手译本: "kaoyan/English1/handTranslation",
    高频词汇与短语搭配: "kaoyan/English/highFrequencyVocabAndPhrases",
    核心词汇大纲: "kaoyan/English/coreVocabularyOutline",
    写作模板与句型表达: "kaoyan/English/writingTemplatesAndExpressions",
    专项训练资料: "kaoyan/English/specialTrainingMaterials",
  },
  // 考研英语二
  kaoyan2: {
    真题: "kaoyan/English2/pastExamPapers",
    答案解析: "kaoyan/English2/Explanation",
    手译本: "kaoyan/English2/handTranslation",
    高频词汇与短语搭配: "kaoyan/English/highFrequencyVocabAndPhrases",
    核心词汇大纲: "kaoyan/English/coreVocabularyOutline",
    写作模板与句型表达: "kaoyan/English/writingTemplatesAndExpressions",
    专项训练资料: "kaoyan/English/specialTrainingMaterials",
  },
  // 四级
  cet4: {
    真题: "cet46/cet4/pastExamPapers",
    答案解析: "cet46/cet4/Explanation",
    核心词汇: "cet46/cet4/core1500words",
    音频: "cet46/cet4/audio",
    "25年06月真题及解析": "cet46/cet4/june2025pastExamPapersAndExplanations",
    四级分类作文模板: "cet46/cet4/writingTemplates",
    四级作文预测: "cet46/cet4/writingPredictions",
    翻译句式及模板: "cet46/cet4/translationTemplates",
  },
  // 六级
  cet6: {
    真题: "cet46/cet6/pastExamPapers",
    答案解析: "cet46/cet6/Explanation",
    核心词汇: "cet46/cet6/core1500words",
    音频: "cet46/cet6/audio",
    "25年06月真题及解析": "cet46/cet6/june2025pastExamPapersAndExplanations",
    六级分类作文模板: "cet46/cet6/writingTemplates",
    六级作文预测: "cet46/cet6/writingPredictions",
    翻译句式及模板: "cet46/cet6/translationTemplates",
  },
  // 考研数学一
  kaoyan_math1: {
    真题: "kaoyan/Math1/pastExamPapers",
    答案解析: "kaoyan/Math1/Explanation",
    公式: "kaoyan/Math1/formula",
  },
  // 考研数学二
  kaoyan_math2: {
    真题: "kaoyan/Math2/pastExamPapers",
    答案解析: "kaoyan/Math2/Explanation",
    公式: "kaoyan/Math1/formula",
  },
  // 考研数学三
  kaoyan_math3: {
    真题: "kaoyan/Math3/pastExamPapers",
    答案解析: "kaoyan/Math3/Explanation",
    公式: "kaoyan/Math1/formula",
  },
  // 考研政治
  kaoyan_politics: {
    真题: "kaoyan/Politics/pastExamPapers",
    答案解析: "kaoyan/Politics/Explanation",
  },
  // 考研计算机408
  kaoyan_408: {
    真题: "kaoyan/CS408/pastExamPapers",
    答案解析: "kaoyan/CS408/Explanation",
  },
  // 考研管综199
  kaoyan_199: {
    真题: "kaoyan/Management199/pastExamPapers",
    答案解析: "kaoyan/Management199/Explanation",
  },
  // 考研经济396
  kaoyan_396: {
    真题: "kaoyan/Economics396/pastExamPapers",
    答案解析: "kaoyan/Economics396/Explanation",
  },
  // 考研西综306
  kaoyan_306: {
    真题: "kaoyan/Western306/pastExamPapers",
    答案解析: "kaoyan/Western306/Explanation",
  },
  // 考研中综307
  kaoyan_307: {
    真题及解析: "kaoyan/Chinese307/pastExamPapersAndExplanations",
  },
  // 考研教育学311
  kaoyan_311: {
    真题: "kaoyan/Education311/pastExamPapers",
    答案解析: "kaoyan/Education311/Explanation",
  },
  // 考研教育综合333
  kaoyan_333: {
    真题: "kaoyan/Education333/pastExamPapers",
  },
  // 考研心理学312
  kaoyan_312: {
    真题: "kaoyan/Psychology312/pastExamPapers",
    答案解析: "kaoyan/Psychology312/Explanation",
  },
  // 考研历史学313
  kaoyan_313: {
    真题及解析: "kaoyan/History313/pastExamPapersAndExplanations",
  },
  // 专四
  tem4: {
    真题: "tem/tem4/pastExamPapers",
    答案解析: "tem/tem4/Explanation",
    音频: "tem/tem4/audio",
  },
  // 专八
  tem8: {
    真题: "tem/tem8/pastExamPapers",
    答案解析: "tem/tem8/Explanation",
    音频: "tem/tem8/audio",
  },
  // 教资面试资料（保留独立配置）
  teacher_interview: {
    面试资料: "teacher/interview/materials",
    试讲模板: "teacher/interview/templates",
    结构化问答: "teacher/interview/structuredQA",
  },
  putonghua: {
    真题: "putonghua/pastExamPapers",
    音频: "putonghua/audio",
    普通话资料包: "putonghua/materials",
  },
  // 考研答题卡
  kaoyan_answer_sheet: {
    打印模板: "kaoyan/answerSheet",
  },
  // 学习计划表
  study_plan: {
    计划模板: "studyPlan",
  },
};

// 完整的资源类型配置
export const RESOURCE_TYPE_CONFIGS: ResourceTypeConfig[] = [
  {
    id: "kaoyan1",
    label: "考研英语一",
    icon: "🎯",
    needsApi: true,
    defaultSection: "真题",
    sections: [
      "真题",
      "答案解析",
      "手译本",
      "高频词汇与短语搭配",
      "核心词汇大纲",
      "写作模板与句型表达",
      "专项训练资料",
    ],
  },
  {
    id: "kaoyan2",
    label: "考研英语二",
    icon: "📚",
    needsApi: true,
    defaultSection: "真题",
    sections: [
      "真题",
      "答案解析",
      "手译本",
      "高频词汇与短语搭配",
      "核心词汇大纲",
      "写作模板与句型表达",
      "专项训练资料",
    ],
  },
  {
    id: "cet4",
    label: "英语四级",
    icon: "🔍",
    needsApi: true,
    defaultSection: "真题",
    sections: [
      "真题",
      "答案解析",
      "核心词汇",
      "音频",
      "25年06月真题及解析",
      "四级分类作文模板",
      "四级作文预测",
      "翻译句式及模板",
    ],
  },
  {
    id: "cet6",
    label: "英语六级",
    icon: "📝",
    needsApi: true,
    defaultSection: "真题",
    sections: [
      "真题",
      "答案解析",
      "核心词汇",
      "音频",
      "25年06月真题及解析",
      "六级分类作文模板",
      "六级作文预测",
      "翻译句式及模板",
    ],
  },
  {
    id: "kaoyan_math1",
    label: "考研数学一",
    icon: "📐",
    needsApi: true,
    defaultSection: "真题",
    sections: ["真题", "答案解析", "公式"],
  },
  {
    id: "kaoyan_math2",
    label: "考研数学二",
    icon: "📏",
    needsApi: true,
    defaultSection: "真题",
    sections: ["真题", "答案解析", "公式"],
  },
  {
    id: "kaoyan_math3",
    label: "考研数学三",
    icon: "🔢",
    needsApi: true,
    defaultSection: "真题",
    sections: ["真题", "答案解析", "公式"],
  },
  {
    id: "kaoyan_politics",
    label: "考研政治",
    icon: "📜",
    needsApi: true,
    defaultSection: "真题",
    sections: ["真题", "答案解析"],
  },
  {
    id: "kaoyan_408",
    label: "考研计算机408",
    icon: "💻",
    needsApi: true,
    defaultSection: "真题",
    sections: ["真题", "答案解析"],
  },
  {
    id: "kaoyan_199",
    label: "考研管综199",
    icon: "📈",
    needsApi: true,
    defaultSection: "真题",
    sections: ["真题", "答案解析"],
  },
  {
    id: "kaoyan_396",
    label: "考研经济396",
    icon: "📋",
    needsApi: true,
    defaultSection: "真题",
    sections: ["真题", "答案解析"],
  },
  {
    id: "kaoyan_306",
    label: "考研西综306",
    icon: "🩺",
    needsApi: true,
    defaultSection: "真题",
    sections: ["真题", "答案解析"],
  },
  {
    id: "kaoyan_307",
    label: "考研中综307",
    icon: "🏥",
    needsApi: true,
    defaultSection: "真题及解析",
    sections: ["真题及解析"],
  },
  {
    id: "kaoyan_311",
    label: "考研教育学311",
    icon: "🎓",
    needsApi: true,
    defaultSection: "真题",
    sections: ["真题", "答案解析"],
  },
  {
    id: "kaoyan_333",
    label: "考研教育综合333",
    icon: "📖",
    needsApi: true,
    defaultSection: "真题",
    sections: ["真题"],
  },
  {
    id: "kaoyan_312",
    label: "考研心理学312",
    icon: "🧠",
    needsApi: true,
    defaultSection: "真题",
    sections: ["真题", "答案解析"],
  },
  {
    id: "kaoyan_313",
    label: "考研历史学313",
    icon: "📜",
    needsApi: true,
    defaultSection: "真题及解析",
    sections: ["真题及解析"],
  },
  {
    id: "tem4",
    label: "专四",
    icon: "📘",
    needsApi: true,
    defaultSection: "真题",
    sections: ["真题", "答案解析", "音频"],
  },
  {
    id: "tem8",
    label: "专八",
    icon: "📗",
    needsApi: true,
    defaultSection: "真题",
    sections: ["真题", "答案解析", "音频"],
  },
  {
    id: "kaoyan_answer_sheet",
    label: "考研答题卡",
    icon: "📝",
    needsApi: true,
    defaultSection: "打印模板",
    description:
      "以下提供各科目考研机读答题卡PDF打印模板，可用于模拟考试练习。建议使用A3/B5纸打印，不要缩放，以保持与实际答题卡大小一致。",
    sections: ["打印模板"],
  },
  {
    id: "study_plan",
    label: "学习计划表",
    icon: "📅",
    needsApi: true,
    defaultSection: "计划模板",
    description:
      "以下提供多种学习计划表模板，包括：考研时间计划表（PDF/可编辑Word版）、艾宾浩斯遗忘曲线计划表、日/周/月复习计划表、100天早起计划等。支持个性化编辑，帮助制定科学的学习时间安排和记忆复习计划。",
    sections: ["计划模板"],
  },
  {
    id: "print",
    label: "打印五分钱/页",
    icon: "🖨️",
    needsApi: false,
    specialComponent: "print",
    description:
      "给大家强烈推荐这个打印平台，仅需5分钱1页，自助即可下单，同学们，快给我冲鸭 🚀",
  },
];

// 教资专用嵌套配置 - 包含历年真题和答案解析
export const NESTED_TEACHER_RESOURCE_PATHS: Record<
  string,
  NestedSectionConfig
> = {
  // 幼儿园教资
  teacher_kindergarten: {
    "科目一（综合素质）": {
      历年真题: "teacher/kindergarten/s1/papers",
      答案解析: "teacher/kindergarten/s1/answers",
    },
    "科目二（保教知识与能力）": {
      历年真题: "teacher/kindergarten/s2/papers",
      答案解析: "teacher/kindergarten/s2/answers",
    },
  },

  // 小学教资
  teacher_primary: {
    "科目一（综合素质）": {
      历年真题: "teacher/primary/s1/papers",
      答案解析: "teacher/primary/s1/answers",
    },
    "科目二（教育教学知识与能力）": {
      历年真题: "teacher/primary/s2/papers",
      答案解析: "teacher/primary/s2/answers",
    },
  },

  // 中学教资
  teacher_secondary: {
    "科目一（综合素质）": {
      历年真题: "teacher/secondary/s1/papers",
      答案解析: "teacher/secondary/s1/answers",
    },
    "科目二（教育知识与能力）": {
      历年真题: "teacher/secondary/s2/papers",
      答案解析: "teacher/secondary/s2/answers",
    },
    科目三: {
      初中: {
        政治: {
          历年真题: "teacher/secondary/s3/junior/politics/papers",
          答案解析: "teacher/secondary/s3/junior/politics/answers",
        },
        美术: {
          历年真题: "teacher/secondary/s3/junior/art/papers",
          答案解析: "teacher/secondary/s3/junior/art/answers",
        },
        历史: {
          历年真题: "teacher/secondary/s3/junior/history/papers",
          答案解析: "teacher/secondary/s3/junior/history/answers",
        },
        生物: {
          历年真题: "teacher/secondary/s3/junior/biology/papers",
          答案解析: "teacher/secondary/s3/junior/biology/answers",
        },
        化学: {
          历年真题: "teacher/secondary/s3/junior/chemistry/papers",
          答案解析: "teacher/secondary/s3/junior/chemistry/answers",
        },
        英语: {
          历年真题: "teacher/secondary/s3/junior/english/papers",
          答案解析: "teacher/secondary/s3/junior/english/answers",
        },
        地理: {
          历年真题: "teacher/secondary/s3/junior/geography/papers",
          答案解析: "teacher/secondary/s3/junior/geography/answers",
        },
        音乐: {
          历年真题: "teacher/secondary/s3/junior/music/papers",
          答案解析: "teacher/secondary/s3/junior/music/answers",
        },
        体育: {
          历年真题: "teacher/secondary/s3/junior/sports/papers",
          答案解析: "teacher/secondary/s3/junior/sports/answers",
        },
        物理: {
          历年真题: "teacher/secondary/s3/junior/physics/papers",
          答案解析: "teacher/secondary/s3/junior/physics/answers",
        },
        信息技术: {
          历年真题: "teacher/secondary/s3/junior/it/papers",
          答案解析: "teacher/secondary/s3/junior/it/answers",
        },
        语文: {
          历年真题: "teacher/secondary/s3/junior/chinese/papers",
          答案解析: "teacher/secondary/s3/junior/chinese/answers",
        },
        数学: {
          历年真题: "teacher/secondary/s3/junior/math/papers",
          答案解析: "teacher/secondary/s3/junior/math/answers",
        },
      },
      高中: {
        政治: {
          历年真题: "teacher/secondary/s3/senior/politics/papers",
          答案解析: "teacher/secondary/s3/senior/politics/answers",
        },
        美术: {
          历年真题: "teacher/secondary/s3/senior/art/papers",
          答案解析: "teacher/secondary/s3/senior/art/answers",
        },
        历史: {
          历年真题: "teacher/secondary/s3/senior/history/papers",
          答案解析: "teacher/secondary/s3/senior/history/answers",
        },
        生物: {
          历年真题: "teacher/secondary/s3/senior/biology/papers",
          答案解析: "teacher/secondary/s3/senior/biology/answers",
        },
        化学: {
          历年真题: "teacher/secondary/s3/senior/chemistry/papers",
          答案解析: "teacher/secondary/s3/senior/chemistry/answers",
        },
        英语: {
          历年真题: "teacher/secondary/s3/senior/english/papers",
          答案解析: "teacher/secondary/s3/senior/english/answers",
        },
        地理: {
          历年真题: "teacher/secondary/s3/senior/geography/papers",
          答案解析: "teacher/secondary/s3/senior/geography/answers",
        },
        音乐: {
          历年真题: "teacher/secondary/s3/senior/music/papers",
          答案解析: "teacher/secondary/s3/senior/music/answers",
        },
        体育: {
          历年真题: "teacher/secondary/s3/senior/sports/papers",
          答案解析: "teacher/secondary/s3/senior/sports/answers",
        },
        物理: {
          历年真题: "teacher/secondary/s3/senior/physics/papers",
          答案解析: "teacher/secondary/s3/senior/physics/answers",
        },
        信息技术: {
          历年真题: "teacher/secondary/s3/senior/it/papers",
          答案解析: "teacher/secondary/s3/senior/it/answers",
        },
        语文: {
          历年真题: "teacher/secondary/s3/senior/chinese/papers",
          答案解析: "teacher/secondary/s3/senior/chinese/answers",
        },
        数学: {
          历年真题: "teacher/secondary/s3/senior/math/papers",
          答案解析: "teacher/secondary/s3/senior/math/answers",
        },
      },
    },
  },
};

// 更新后的教资资源类型配置
export const TEACHER_RESOURCE_CONFIGS: ResourceTypeConfig[] = [
  {
    id: "teacher_secondary",
    label: "中学教资",
    icon: "👩‍🏫",
    needsApi: true,
    description:
      "中学教资历年真题及答案解析，包含科目一《综合素质》、科目二《教育知识与能力》和科目三各学科的历年真题和详细答案解析",
    nestedSections: NESTED_TEACHER_RESOURCE_PATHS.teacher_secondary,
  },
  {
    id: "teacher_primary",
    label: "小学教资",
    icon: "👨‍🏫",
    needsApi: true,
    description:
      "小学教资历年真题及答案解析，包含科目一《综合素质》和科目二《教育教学知识与能力》的历年真题和详细答案解析",
    nestedSections: NESTED_TEACHER_RESOURCE_PATHS.teacher_primary,
  },
  {
    id: "teacher_kindergarten",
    label: "幼儿园教资",
    icon: "🧸",
    needsApi: true,
    description:
      "幼儿园教资历年真题及答案解析，包含科目一《综合素质》和科目二《保教知识与能力》的历年真题和详细答案解析",
    nestedSections: NESTED_TEACHER_RESOURCE_PATHS.teacher_kindergarten,
  },
];

// 辅助函数：获取嵌套路径
export function getNestedPath(
  nestedConfig: NestedSectionConfig,
  pathArray: string[]
): string | null {
  let current: any = nestedConfig;

  for (const key of pathArray) {
    if (current && typeof current === "object" && key in current) {
      current = current[key];
    } else {
      return null;
    }
  }

  return typeof current === "string" ? current : null;
}

// 辅助函数：获取嵌套结构的所有键
export function getNestedKeys(nestedConfig: NestedSectionConfig): string[] {
  if (!nestedConfig || typeof nestedConfig !== "object") {
    return [];
  }

  return Object.keys(nestedConfig);
}

// 辅助函数：检查是否为叶子节点（最终路径）
export function isLeafNode(
  nestedConfig: NestedSectionConfig,
  pathArray: string[]
): boolean {
  let current: any = nestedConfig;

  for (const key of pathArray) {
    if (current && typeof current === "object" && key in current) {
      current = current[key];
    } else {
      return false;
    }
  }

  return typeof current === "string";
}

// 辅助函数：获取面包屑路径
export function getBreadcrumbPath(pathArray: string[]): string {
  return pathArray.join(" > ");
}

// 辅助函数：获取默认路径（自动选择第一个选项直到叶子节点）
export function getDefaultPath(nestedConfig: NestedSectionConfig): string[] {
  const path: string[] = [];
  let current: any = nestedConfig;

  while (current && typeof current === "object") {
    const keys = Object.keys(current);
    if (keys.length === 0) break;

    const firstKey = keys[0];
    path.push(firstKey);
    current = current[firstKey];

    // 如果到达叶子节点（值是字符串），停止
    if (typeof current === "string") {
      break;
    }
  }

  return path;
}

// 类型映射 - 保持向后兼容
export const TYPE_CATEGORY_MAP: Record<string, string> = {
  kaoyan1: "考研英语一",
  kaoyan2: "考研英语二",
  cet4: "四级",
  cet6: "六级",
  kaoyan_math1: "考研数学一",
  kaoyan_math2: "考研数学二",
  kaoyan_math3: "考研数学三",
  kaoyan_politics: "考研政治",
  kaoyan_408: "考研计算机408",
  kaoyan_199: "考研管综199",
  kaoyan_396: "考研经济396",
  kaoyan_306: "考研西综306",
  kaoyan_307: "考研中综307",
  kaoyan_311: "考研教育学311",
  kaoyan_333: "考研教育综合333",
  kaoyan_312: "考研心理学312",
  kaoyan_313: "考研历史学313",
  tem4: "专四",
  tem8: "专八",
  kaoyan_answer_sheet: "考研答题卡",
  study_plan: "学习计划表",
  // 教资相关
  teacher_primary: "小学教资",
  teacher_secondary: "中学教资",
  teacher_kindergarten: "幼儿园教资",
};

// 辅助函数：根据ID获取资源类型配置
export function getResourceTypeConfig(
  id: string
): ResourceTypeConfig | undefined {
  return RESOURCE_TYPE_CONFIGS.find((config) => config.id === id);
}

// 辅助函数：获取所有需要API的资源类型
export function getApiResourceTypes(): ResourceTypeConfig[] {
  return RESOURCE_TYPE_CONFIGS.filter((config) => config.needsApi);
}

// 辅助函数：获取所有特殊组件类型
export function getSpecialResourceTypes(): ResourceTypeConfig[] {
  return RESOURCE_TYPE_CONFIGS.filter((config) => !config.needsApi);
}

// 辅助函数：获取分类图标
export function getCategoryIcon(category: string): string {
  return CATEGORY_ICONS[category] || "📄";
}

// 辅助函数：获取section图标
export function getSectionIcon(section: string): string {
  return SECTION_ICONS[section] || "📋";
}

// 辅助函数：获取分类样式
export function getCategoryStyles(category: string): string {
  return (
    CATEGORY_STYLES[category] ||
    "bg-blue-50 text-blue-600 border border-blue-100 hover:bg-blue-100"
  );
}

// 辅助函数：获取section样式
export function getSectionStyles(section: string): string {
  return (
    SECTION_STYLES[section] ||
    "bg-gray-50 text-gray-600 border border-gray-100 hover:bg-gray-100"
  );
}

// 资源接口定义
export interface Resource {
  id: string;
  name: string;
  size: number;
  url: string;
  created_at: string;
  type: string;
  category?: string;
  section?: string;
}

// 根据文件名获取文件类型
export function getFileTypeFromName(fileName: string): string {
  const extension = fileName.split(".").pop()?.toLowerCase();
  switch (extension) {
    case "pdf":
      return "application/pdf";
    case "doc":
    case "docx":
      return "application/msword";
    case "xls":
    case "xlsx":
      return "application/vnd.ms-excel";
    case "ppt":
    case "pptx":
      return "application/vnd.ms-powerpoint";
    case "txt":
      return "text/plain";
    case "mp3":
      return "audio/mpeg";
    case "wav":
      return "audio/wav";
    case "mp4":
      return "video/mp4";
    case "jpg":
    case "jpeg":
      return "image/jpeg";
    case "png":
      return "image/png";
    default:
      return "application/octet-stream";
  }
}
