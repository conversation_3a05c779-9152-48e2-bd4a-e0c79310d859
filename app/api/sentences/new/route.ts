import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// POST 方法：添加新句子
export async function POST(request: Request) {
  try {
    // 获取请求数据
    const sentenceData = await request.json();

    // 验证必填字段
    const requiredFields = [
      "year",
      "type",
      "sectionType",
      "title",
      "originalContent",
    ];
    for (const field of requiredFields) {
      if (!sentenceData[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // 处理content字段
    let contentJson = "{}";
    if (sentenceData.content) {
      contentJson =
        typeof sentenceData.content === "string"
          ? sentenceData.content
          : JSON.stringify(sentenceData.content);
    }

    // 确保indexNum是数字类型
    const indexNum =
      sentenceData.indexNum !== undefined && sentenceData.indexNum !== null
        ? Number(sentenceData.indexNum)
        : 0;

    // 准备插入数据
    const insertData = {
      year: sentenceData.year,
      type: sentenceData.type,
      section_type: sentenceData.sectionType,
      title: sentenceData.title,
      original_content: sentenceData.originalContent,
      content: contentJson,
      index_num: indexNum,
    };

    // 执行Supabase插入
    const { data, error } = await supabase
      .from("sentence_analysis")
      .insert(insertData)
      .select("id")
      .single();

    if (error) {
      throw error;
    }

    return NextResponse.json({
      success: true,
      message: "Sentence created successfully",
      data: {
        id: data.id,
      },
    });
  } catch (error) {
    console.error("Error creating sentence:", error);
    return NextResponse.json(
      {
        error: "Failed to create sentence",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
