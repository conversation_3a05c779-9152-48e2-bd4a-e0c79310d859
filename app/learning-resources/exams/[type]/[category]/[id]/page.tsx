"use client";

import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import {
  Download,
  ArrowLeft,
  FileText,
  Calendar,
  Archive,
  Info,
  CheckCircle,
  LogIn,
  Home,
} from "lucide-react";
import { Resource } from "@/app/lib/learning-resources-config";

// 文件类型图标组件 (复用现有组件)
const PdfIcon = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="text-red-500"
  >
    <path
      d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="#FEEFEE"
    />
    <path
      d="M14 2V8H20"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path d="M11.5 12.5H12.5V16.5H11.5V12.5Z" fill="currentColor" />
    <path
      d="M9 13C9 12.7239 9.22386 12.5 9.5 12.5H10.5C10.7761 12.5 11 12.7239 11 13V16C11 16.2761 10.7761 16.5 10.5 16.5H9.5C9.22386 16.5 9 16.2761 9 16V13Z"
      fill="currentColor"
    />
    <path
      d="M13 13C13 12.7239 13.2239 12.5 13.5 12.5H14.5C14.7761 12.5 15 12.7239 15 13V16C15 16.2761 14.7761 16.5 14.5 16.5H13.5C13.2239 16.5 13 16.2761 13 16V13Z"
      fill="currentColor"
    />
    <path
      d="M7 16.5H17"
      stroke="currentColor"
      strokeWidth="1"
      strokeLinecap="round"
    />
  </svg>
);

// 通用文件图标
function getFileIcon(type: string) {
  if (type.includes("pdf")) return <PdfIcon />;
  return <FileText className="w-8 h-8 text-gray-500" />;
}

// 格式化文件大小
function formatFileSize(bytes: number) {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

// 获取用户友好的文件类型名称
function getReadableFileType(mimeType: string): string {
  const type = mimeType.toLowerCase();
  if (type.includes("pdf")) return "PDF文档";
  if (type.includes("word") || type.includes("doc")) return "Word文档";
  if (type.includes("sheet") || type.includes("excel") || type.includes("xls"))
    return "Excel表格";
  if (
    type.includes("presentation") ||
    type.includes("powerpoint") ||
    type.includes("ppt")
  )
    return "PPT演示文稿";
  if (type.includes("image")) return "图片文件";
  if (type.includes("text")) return "文本文件";
  if (
    type.includes("audio") ||
    type.includes("mp3") ||
    type.includes("wav") ||
    type.includes("m4a") ||
    type.includes("aac")
  )
    return "音频文件";
  return "文档文件";
}

// 获取分类样式
function getCategoryStyles(category: string) {
  switch (category) {
    case "考研英语一":
      return "bg-blue-50 text-blue-600 border border-blue-100";
    case "考研英语二":
      return "bg-indigo-50 text-indigo-600 border border-indigo-100";
    case "考研政治":
      return "bg-red-50 text-red-600 border border-red-100";
    case "考研数学一":
      return "bg-purple-50 text-purple-600 border border-purple-100";
    case "考研数学二":
      return "bg-fuchsia-50 text-fuchsia-600 border border-fuchsia-100";
    case "考研数学三":
      return "bg-violet-50 text-violet-600 border border-violet-100";
    case "四级":
      return "bg-cyan-50 text-cyan-600 border border-cyan-100";
    case "六级":
      return "bg-teal-50 text-teal-600 border border-teal-100";
    default:
      return "bg-blue-50 text-blue-600 border border-blue-100";
  }
}

// 获取部分样式
function getSectionStyles(section: string) {
  switch (section) {
    case "真题":
      return "bg-green-50 text-green-600 border border-green-100";
    case "答案解析":
      return "bg-yellow-50 text-yellow-700 border border-yellow-100";
    case "资料":
      return "bg-pink-50 text-pink-600 border border-pink-100";
    case "核心词汇":
      return "bg-purple-50 text-purple-600 border border-purple-100";
    default:
      return "bg-gray-50 text-gray-600 border border-gray-100";
  }
}

// 订阅信息接口定义
interface SubscriptionInfo {
  isActive?: boolean;
  type?: string;
  endDate?: string;
  isSubscribed?: boolean;
  isLifetimeMember?: boolean;
}

export default function ExamResourceDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [resource, setResource] = useState<Resource | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [downloadCount, setDownloadCount] = useState<number>(0);
  const [maxDownloads, setMaxDownloads] = useState<number>(3);
  const [subscriptionInfo, setSubscriptionInfo] =
    useState<SubscriptionInfo | null>(null);
  const [downloadMessage, setDownloadMessage] = useState<string | null>(null);

  const { type, category, id } = params;

  // 获取订阅信息
  useEffect(() => {
    const fetchSubscriptionInfo = async () => {
      try {
        const response = await fetch("/api/subscription");
        if (response.status === 401) {
          setSubscriptionInfo(null);
          return;
        }
        if (!response.ok) return;

        const data = await response.json();
        if (data.subscriptionInfo) {
          setSubscriptionInfo(data.subscriptionInfo);
        } else if (data.success && data.data) {
          setSubscriptionInfo(data.data);
        } else {
          setSubscriptionInfo({ isActive: false });
        }
      } catch (error) {
        console.error("获取订阅信息错误:", error);
        setSubscriptionInfo({ isActive: false });
      }
    };

    fetchSubscriptionInfo();
  }, []);

  // 获取下载次数
  useEffect(() => {
    const fetchDownloadCount = async () => {
      try {
        const response = await fetch("/api/download-count");
        if (!response.ok) return;

        const data = await response.json();
        setDownloadCount(data.currentDownloads || 0);
        setMaxDownloads(data.maxDownloads || 3);
      } catch (error) {
        console.error("获取下载次数出错:", error);
      }
    };

    fetchDownloadCount();
  }, []);

  // 获取资源详情
  useEffect(() => {
    const fetchResource = async () => {
      try {
        setLoading(true);
        // 解码 URL 参数
        const decodedCategory = decodeURIComponent(category as string);

        // 使用优化的单个资源API（不需要section参数，API会自动搜索所有路径）
        const response = await fetch(
          `/api/learning-resources/${id}?type=${type}&category=${encodeURIComponent(decodedCategory)}`
        );

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error("资源不存在");
          }
          throw new Error(`获取资源失败: ${response.status}`);
        }

        const data = await response.json();
        setResource(data.resource);
        setError(null);
      } catch (err) {
        console.error("获取资源详情出错:", err);
        setError(
          err instanceof Error ? err.message : "加载资源失败，请稍后再试"
        );
      } finally {
        setLoading(false);
      }
    };

    if (type && category && id) {
      fetchResource();
    }
  }, [type, category, id]);

  // 处理下载
  const handleDownload = async () => {
    if (!resource) return;

    const isSubscribed =
      subscriptionInfo?.isSubscribed ||
      subscriptionInfo?.isLifetimeMember ||
      subscriptionInfo?.isActive ||
      false;
    const isLoggedIn = subscriptionInfo !== null;

    // 检查下载权限
    if (!isSubscribed && downloadCount >= maxDownloads) {
      if (isLoggedIn) {
        router.push("/pricing");
        return;
      } else {
        router.push("/sign-in?redirectTo=" + encodeURIComponent("/pricing"));
        return;
      }
    }

    setDownloadMessage("正在准备下载...");
    setTimeout(() => setDownloadMessage(null), 3000);

    try {
      const link = document.createElement("a");
      link.href = resource.url;
      link.setAttribute("download", resource.name);
      link.setAttribute("target", "_blank");
      document.body.appendChild(link);
      link.click();
      setTimeout(() => document.body.removeChild(link), 100);

      // 更新下载计数
      if (!isSubscribed) {
        await fetch("/api/download-count", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
        });
        setDownloadCount((prev) => prev + 1);
      }
    } catch (error) {
      console.error("下载文件失败:", error);
      window.location.href = resource.url;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-50 py-10">
        <div className="container mx-auto px-4">
          <div className="text-center py-20">
            <div className="text-7xl mb-4">⏳</div>
            <h3 className="text-xl font-medium text-gray-800 mb-2">
              加载中...
            </h3>
            <p className="text-gray-600">正在获取资源详情，请稍候</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !resource) {
    return (
      <div className="min-h-screen bg-slate-50 py-10">
        <div className="container mx-auto px-4">
          <div className="text-center py-20">
            <div className="text-7xl mb-4">😞</div>
            <h3 className="text-xl font-medium text-gray-800 mb-2">
              资源不存在
            </h3>
            <p className="text-gray-600 mb-6">{error || "请求的资源未找到"}</p>
            <Link
              href="/learning-resources"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回资源库
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* SEO Meta Tags */}
      <head>
        <title>
          {resource.name} - {resource.category}
          {resource.section} - 面包资料屋
        </title>
        <meta
          name="description"
          content={`免费下载${resource.category}${resource.section}资料《${resource.name}》，文件大小${formatFileSize(resource.size)}，${getReadableFileType(resource.type)}格式。面包资料屋提供考研、四六级等优质学习资源。`}
        />
      </head>

      <div className="min-h-screen bg-slate-50 py-10">
        {/* 下载提示信息 */}
        {downloadMessage && (
          <div className="fixed top-5 left-1/2 transform -translate-x-1/2 z-50 bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded shadow-lg">
            <div className="flex items-center">
              <Info className="w-5 h-5 mr-2" />
              <p>{downloadMessage}</p>
            </div>
          </div>
        )}

        <div className="container mx-auto px-4">
          {/* 面包屑导航 */}
          <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-8">
            <Link href="/" className="hover:text-blue-600 transition-colors">
              <Home className="w-4 h-4" />
            </Link>
            <span>/</span>
            <Link
              href="/learning-resources"
              className="hover:text-blue-600 transition-colors"
            >
              资源库
            </Link>
            <span>/</span>
            <Link
              href={`/learning-resources?type=${type}`}
              className="hover:text-blue-600 transition-colors"
            >
              {resource.category}
            </Link>
            <span>/</span>
            <span className="text-gray-700">{resource.section}</span>
            <span>/</span>
            <span className="text-gray-900 font-medium truncate max-w-xs">
              {resource.name}
            </span>
          </nav>

          {/* 返回按钮 */}
          <div className="mb-6">
            <Link
              href="/learning-resources"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              返回资源库
            </Link>
          </div>

          {/* 主要内容 */}
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="p-8">
              {/* 资源标题和基本信息 */}
              <div className="flex flex-col lg:flex-row lg:items-start gap-8">
                {/* 文件图标 */}
                <div className="flex-shrink-0">
                  <div className="w-24 h-24 bg-gray-50 rounded-lg flex items-center justify-center">
                    {getFileIcon(resource.type)}
                  </div>
                </div>

                {/* 资源信息 */}
                <div className="flex-1 min-w-0">
                  <h1 className="text-3xl font-bold text-gray-900 mb-4 break-words">
                    {resource.name}
                  </h1>

                  {/* 标签 */}
                  <div className="flex flex-wrap gap-3 mb-6">
                    <span
                      className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${getCategoryStyles(resource.category || "")}`}
                    >
                      {resource.category}
                    </span>
                    <span
                      className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${getSectionStyles(resource.section || "")}`}
                    >
                      {resource.section}
                    </span>
                  </div>

                  {/* 文件详情 */}
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-8">
                    <div className="flex items-center">
                      <FileText className="w-5 h-5 text-gray-400 mr-3" />
                      <div>
                        <div className="text-sm text-gray-500">文件类型</div>
                        <div className="font-medium">
                          {getReadableFileType(resource.type)}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <Archive className="w-5 h-5 text-gray-400 mr-3" />
                      <div>
                        <div className="text-sm text-gray-500">文件大小</div>
                        <div className="font-medium">
                          {formatFileSize(resource.size)}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <Calendar className="w-5 h-5 text-gray-400 mr-3" />
                      <div>
                        <div className="text-sm text-gray-500">上传时间</div>
                        <div className="font-medium">
                          {new Date(resource.created_at).toLocaleDateString(
                            "zh-CN"
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex flex-col sm:flex-row gap-4">
                    <button
                      onClick={handleDownload}
                      className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors font-medium"
                    >
                      <Download className="w-5 h-5 mr-2" />
                      立即下载
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* 下载权限说明 */}
            <div className="border-t border-gray-200 p-8 bg-gray-50">
              {subscriptionInfo?.isSubscribed ||
              subscriptionInfo?.isLifetimeMember ||
              subscriptionInfo?.isActive ? (
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-6 h-6 text-green-600 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="font-medium text-green-800 mb-1">
                      订阅用户专享
                    </h3>
                    <p className="text-sm text-green-700">
                      您已解锁无限下载权限，可以自由下载所有学习资源。感谢您的支持！
                    </p>
                  </div>
                </div>
              ) : (
                <div className="flex items-start gap-3">
                  <Info className="w-6 h-6 text-blue-600 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="font-medium text-blue-800 mb-1">
                      下载权限说明
                    </h3>
                    <p className="text-sm text-blue-700 mb-3">
                      您当前可以免费下载 {maxDownloads} 个学习资源，已下载{" "}
                      {downloadCount} 个。 订阅后可无限制下载所有资源。
                    </p>
                    <div className="flex gap-3">
                      {subscriptionInfo !== null ? (
                        <Link
                          href="/pricing"
                          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                        >
                          <CheckCircle className="w-4 h-4 mr-1" />
                          立即订阅
                        </Link>
                      ) : (
                        <Link
                          href="/sign-in"
                          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                        >
                          <LogIn className="w-4 h-4 mr-1" />
                          登录账号
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
