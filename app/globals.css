@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 自定义Markdown主体样式 */
.markdown-body {
  @apply text-gray-800 leading-relaxed;
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  @apply font-bold text-blue-700 mt-6 mb-3;
}

.markdown-body h1 {
  @apply text-2xl mb-4 pb-2 border-b border-gray-200;
}

.markdown-body h2 {
  @apply text-xl my-4 pb-1 border-b border-gray-100;
}

.markdown-body h3 {
  @apply text-lg font-semibold;
}

.markdown-body p {
  @apply my-3;
}

.markdown-body ul,
.markdown-body ol {
  @apply pl-6 my-4;
}

.markdown-body ul {
  @apply list-disc;
}

.markdown-body ol {
  @apply list-decimal;
}

.markdown-body li {
  @apply mb-2;
}

.markdown-body blockquote {
  @apply border-l-4 border-blue-300 bg-blue-50 p-3 my-4 rounded-r text-gray-700 italic;
}

/* 代码块的特殊设置 */
.markdown-body pre {
  @apply bg-slate-900 text-white p-4 rounded-lg overflow-x-auto my-4 text-sm;
}

.markdown-body code {
  @apply bg-slate-100 text-slate-800 px-1 py-0.5 rounded text-sm;
}

.markdown-body pre code {
  @apply bg-transparent text-white p-0 text-sm;
}

.markdown-body strong {
  @apply font-bold text-gray-900;
}

.markdown-body em {
  @apply italic text-gray-700;
}

.markdown-body a {
  @apply text-blue-600 hover:underline;
}

.markdown-body table {
  @apply w-full border-collapse my-4 text-sm;
}

.markdown-body table th,
.markdown-body table td {
  @apply border border-gray-300 p-2;
}

.markdown-body table th {
  @apply bg-gray-100 font-semibold;
}

.markdown-body hr {
  @apply my-6 border-gray-200;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}
