import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// 配置最大文件大小 (50MB)
export const maxDuration = 30; // Vercel function timeout
export const dynamic = "force-dynamic";

// POST请求处理函数 - 上传文件
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const path = formData.get("path") as string;

    if (!file) {
      return NextResponse.json({ error: "没有选择文件" }, { status: 400 });
    }

    // 验证文件类型
    const allowedTypes = [
      "audio/",
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/vnd.ms-powerpoint",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    ];

    const isValidType = allowedTypes.some((type) => file.type.startsWith(type));
    if (!isValidType) {
      return NextResponse.json({ error: "不支持的文件类型" }, { status: 400 });
    }

    // 构建文件路径
    const fileName = file.name;
    const filePath = path ? `${path}/${fileName}` : fileName;

    // 将文件转换为ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);

    // 上传文件到Supabase Storage
    const { data, error } = await supabase.storage
      .from("mbdata")
      .upload(filePath, uint8Array, {
        contentType: file.type,
        upsert: true, // 允许覆盖同名文件
      });

    if (error) {
      console.error("文件上传失败:", error);
      return NextResponse.json(
        { error: "文件上传失败", details: error.message },
        { status: 500 }
      );
    }

    // 获取上传文件的公开URL
    const { data: urlData } = supabase.storage
      .from("mbdata")
      .getPublicUrl(data.path);

    return NextResponse.json({
      success: true,
      message: "文件上传成功",
      file: {
        name: fileName,
        path: data.path,
        size: file.size,
        type: file.type,
        url: urlData.publicUrl,
      },
    });
  } catch (error) {
    console.error("上传过程中出现错误:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
