"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";

const slides = [
  {
    id: 1,
    title: "2023年考研英语真题解析",
    description: "最新考研英语真题，包含完整解析和句子分析",
    bgColor: "from-blue-500 ",
    buttonText: "立即学习",
    buttonLink: "/papers?year=2023",
    image: "/images/slide1.jpg", // 替换为实际的图片路径
  },
  {
    id: 2,
    title: "完形填空专项训练",
    description: "历年完形填空真题汇总，掌握解题技巧",
    bgColor: "from-indigo-500 to-purple-600",
    buttonText: "开始练习",
    buttonLink: "/papers?sectionType=use_of_english",
    image: "/images/slide2.jpg", // 替换为实际的图片路径
  },
  {
    id: 3,
    title: "翻译专区",
    description: "考研英语翻译题目分析，掌握翻译技巧",
    bgColor: "from-purple-500 to-pink-600",
    buttonText: "查看翻译题",
    buttonLink: "/papers?sectionType=translation",
    image: "/images/slide3.jpg", // 替换为实际的图片路径
  },
];

const HeroSlider = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  // 自动轮播
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // 手动切换轮播
  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1));
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev === 0 ? slides.length - 1 : prev - 1));
  };

  return (
    <div className="relative overflow-hidden w-full h-[400px] md:h-[500px] bg-gray-900">
      {/* 幻灯片容器 */}
      <div
        className="h-full transition-transform duration-500 ease-out flex"
        style={{ transform: `translateX(-${currentSlide * 100}%)` }}
      >
        {slides.map((slide, index) => (
          <div
            key={slide.id}
            className={`absolute inset-0 w-full h-full flex-shrink-0 bg-gradient-to-r ${slide.bgColor} transition-opacity duration-500`}
            style={{ opacity: currentSlide === index ? 1 : 0 }}
          >
            {/* 背景图片 */}
            <div className="absolute inset-0 opacity-20">
              {slide.image && (
                <Image
                  src={slide.image}
                  alt={slide.title}
                  layout="fill"
                  objectFit="cover"
                  priority={index === 0}
                />
              )}
            </div>

            {/* 内容 */}
            <div className="relative z-10 container mx-auto px-4 h-full flex flex-col justify-center">
              <div className="max-w-xl">
                <h2 className="text-3xl md:text-4xl font-bold text-white mb-4 animate-fadeIn">
                  {slide.title}
                </h2>
                <p className="text-lg md:text-xl text-white/90 mb-8 animate-fadeIn animation-delay-200">
                  {slide.description}
                </p>
                <Link
                  href={slide.buttonLink}
                  className="inline-block px-6 py-3 bg-white text-blue-600 font-medium rounded-lg hover:bg-blue-50 transition-colors animate-fadeIn animation-delay-400"
                >
                  {slide.buttonText}
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 导航按钮 - 移动端隐藏 */}
      <div className="absolute inset-y-0 left-0 hidden md:flex items-center ml-4 z-20">
        <button
          onClick={prevSlide}
          className="w-10 h-10 rounded-full bg-white/20 hover:bg-white/30 flex items-center justify-center text-white transition-colors"
          aria-label="Previous slide"
        >
          <svg
            className="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>
      </div>

      <div className="absolute inset-y-0 right-0 hidden md:flex items-center mr-4 z-20">
        <button
          onClick={nextSlide}
          className="w-10 h-10 rounded-full bg-white/20 hover:bg-white/30 flex items-center justify-center text-white transition-colors"
          aria-label="Next slide"
        >
          <svg
            className="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
        </button>
      </div>

      {/* 指示器 */}
      <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-2 z-20">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-2.5 h-2.5 rounded-full transition-all ${
              currentSlide === index
                ? "bg-white w-8"
                : "bg-white/50 hover:bg-white/80"
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default HeroSlider;
