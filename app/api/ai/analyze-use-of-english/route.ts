import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { Database } from "@/types/supabase";
import { timeStart, timeEnd } from "@/utils/performance";

// 创建Supabase客户端
const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL || "",
  process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || ""
);

// 定义DeepSeek API URL和密钥
const DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions";
const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY || "";

// 解析题目选项文本
function parseOptions(optionsText: string): Array<{
  questionNumber: number;
  options: { A: string; B: string; C: string; D: string };
}> {
  const result = [];
  const lines = optionsText.split("\n").filter((line) => line.trim());

  for (const line of lines) {
    // 修改正则表达式，以支持多词选项
    // 匹配格式：题号. A. 选项A B. 选项B C. 选项C D. 选项D
    const match = line.match(
      /(\d+)\.\s+A\.\s+(.*?)\s+B\.\s+(.*?)\s+C\.\s+(.*?)\s+D\.\s+(.*?)$/
    );

    if (match) {
      const [_, questionNumber, optionA, optionB, optionC, optionD] = match;

      result.push({
        questionNumber: parseInt(questionNumber, 10),
        options: {
          A: optionA.trim(),
          B: optionB.trim(),
          C: optionC.trim(),
          D: optionD.trim(),
        },
      });
    }
  }

  return result;
}

// 请求AI分析
async function requestAIAnalysis(text: string, options: any) {
  try {
    const response = await fetch(DEEPSEEK_API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${DEEPSEEK_API_KEY}`,
      },
      body: JSON.stringify({
        model: "deepseek-chat",
        messages: [
          {
            role: "system",
            content:
              "你是一位专业的考研英语老师，精通完形填空题目的分析。请对以下完形填空进行分析，包括全文翻译、语境分析、词汇解析，并给出每道题的正确答案及详细解析。请使用以下格式输出：\n\n## 全文翻译：\n[完整原文的中文翻译]\n\n## 题目解析：\n1. 答案：[选项]\n解析：[详细解析]\n\n2. 答案：[选项]\n解析：[详细解析]\n\n以此类推...",
          },
          {
            role: "user",
            content: `请分析以下完形填空题目，提供详细解析：\n\n原文:\n${text}\n\n题目选项:\n${options}\n\n请务必先提供完整的全文翻译，然后再进行具体题目分析。`,
          },
        ],
        temperature: 0.7,
        max_tokens: 4000,
      }),
    });

    if (!response.ok) {
      throw new Error(
        `DeepSeek API request failed with status ${response.status}`
      );
    }

    return await response.json();
  } catch (error) {
    console.error("AI分析请求错误:", error);
    throw error;
  }
}

// 解析AI响应并更新数据库
async function processAIResponse(
  aiResponse: any,
  paperId: string,
  parsedOptions: any[]
) {
  try {
    // 提取AI生成的内容
    const aiContent = aiResponse.choices[0].message.content;

    // 调试日志：输出完整的AI响应内容
    console.log("AI返回的完整内容:", aiContent);

    // 提取全文翻译（假设AI回复中包含"全文翻译"部分）
    const textAnalysisMatch = aiContent.match(
      /全文翻译[:：]([\s\S]+?)(?=\n## |$)/i
    );
    const textAnalysis = textAnalysisMatch ? textAnalysisMatch[1].trim() : "";

    // 调试日志：检查全文翻译匹配结果
    console.log("全文翻译匹配结果:", textAnalysisMatch);
    console.log("解析后的全文翻译:", textAnalysis);

    // 更新全文翻译
    if (textAnalysis) {
      const { error: updateError } = await supabase
        .from("use_of_english_texts")
        .update({ text_analysis: textAnalysis })
        .eq("paper_id", paperId);

      if (updateError) {
        throw updateError;
      }
    } else {
      console.log("警告：未找到全文翻译内容");

      // 尝试使用其他可能的匹配模式
      const altTextAnalysisMatch = aiContent.match(
        /文章翻译[:：]([\s\S]+?)(?=\n## |$)/i
      );
      const altTextAnalysis = altTextAnalysisMatch
        ? altTextAnalysisMatch[1].trim()
        : "";

      if (altTextAnalysis) {
        console.log("使用替代匹配模式找到全文翻译:", altTextAnalysis);
        const { error: updateError } = await supabase
          .from("use_of_english_texts")
          .update({ text_analysis: altTextAnalysis })
          .eq("paper_id", paperId);

        if (updateError) {
          throw updateError;
        }
      }
    }

    // 提取题目答案和解析
    for (const option of parsedOptions) {
      const questionNumber = option.questionNumber;

      // 匹配该题目的答案和解析
      const answerMatch = aiContent.match(
        new RegExp(`${questionNumber}[.．]\\s*答案[:：]\\s*([A-D])`, "i")
      );
      const explanationMatch = aiContent.match(
        new RegExp(
          `${questionNumber}[.．](?:[^]*?)解析[:：]([^]*?)(?=\\n\\d+[.．]|$)`,
          "i"
        )
      );

      // 调试日志：检查题目匹配结果
      console.log(`题目${questionNumber}答案匹配结果:`, answerMatch);
      console.log(`题目${questionNumber}解析匹配结果:`, explanationMatch);

      // 准备更新数据
      const updateData: any = {};

      if (answerMatch && answerMatch[1]) {
        updateData.correct_answer = answerMatch[1].trim();
      }

      if (explanationMatch && explanationMatch[1]) {
        updateData.explanation = explanationMatch[1].trim();
      } else {
        // 尝试使用其他可能的匹配模式
        // 转义选项文本中的特殊字符，以便在正则表达式中使用
        const escapeRegExp = (string: string) => {
          return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
        };

        const safeOptionA = escapeRegExp(option.options.A);
        const safeOptionB = escapeRegExp(option.options.B);
        const safeOptionC = escapeRegExp(option.options.C);
        const safeOptionD = escapeRegExp(option.options.D);

        // 更安全的替代匹配模式，避免特殊字符导致正则表达式失效
        const altExplanationMatch = aiContent.match(
          new RegExp(
            `${questionNumber}[.．](?:[^]*?)答案[:：]\\s*([A-D])(?:[^]*?)解析[:：]([^]*?)(?=\\n\\d+[.．]|$)`,
            "i"
          )
        );

        if (altExplanationMatch && altExplanationMatch[2]) {
          console.log(
            `题目${questionNumber}使用替代模式找到解析:`,
            altExplanationMatch[2].trim()
          );
          updateData.explanation = altExplanationMatch[2].trim();

          // 如果之前没有找到答案，也尝试从这个匹配中提取
          if (!updateData.correct_answer && altExplanationMatch[1]) {
            updateData.correct_answer = altExplanationMatch[1].trim();
          }
        }
      }

      // 匹配选项翻译 - 修改正则表达式使其更安全地处理多词选项
      const escapeRegExp = (string: string) => {
        return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
      };

      const safeOptionA = escapeRegExp(option.options.A);
      const safeOptionB = escapeRegExp(option.options.B);
      const safeOptionC = escapeRegExp(option.options.C);
      const safeOptionD = escapeRegExp(option.options.D);

      const optionATransMatch = aiContent.match(
        new RegExp(
          `${questionNumber}[.．](?:[^]*?)A[.．]\\s*${safeOptionA}\\s*[:：]\\s*([^]*?)(?=\\s*B[.．]|$)`,
          "i"
        )
      );
      const optionBTransMatch = aiContent.match(
        new RegExp(
          `${questionNumber}[.．](?:[^]*?)B[.．]\\s*${safeOptionB}\\s*[:：]\\s*([^]*?)(?=\\s*C[.．]|$)`,
          "i"
        )
      );
      const optionCTransMatch = aiContent.match(
        new RegExp(
          `${questionNumber}[.．](?:[^]*?)C[.．]\\s*${safeOptionC}\\s*[:：]\\s*([^]*?)(?=\\s*D[.．]|$)`,
          "i"
        )
      );
      const optionDTransMatch = aiContent.match(
        new RegExp(
          `${questionNumber}[.．](?:[^]*?)D[.．]\\s*${safeOptionD}\\s*[:：]\\s*([^]*?)(?=\\s*\\n|$)`,
          "i"
        )
      );

      // 更新选项翻译
      const options: any = {
        A: { text: option.options.A },
        B: { text: option.options.B },
        C: { text: option.options.C },
        D: { text: option.options.D },
      };

      if (optionATransMatch && optionATransMatch[1]) {
        options.A.translation = optionATransMatch[1].trim();
      }

      if (optionBTransMatch && optionBTransMatch[1]) {
        options.B.translation = optionBTransMatch[1].trim();
      }

      if (optionCTransMatch && optionCTransMatch[1]) {
        options.C.translation = optionCTransMatch[1].trim();
      }

      if (optionDTransMatch && optionDTransMatch[1]) {
        options.D.translation = optionDTransMatch[1].trim();
      }

      updateData.options = options;

      // 更新数据库
      if (Object.keys(updateData).length > 0) {
        console.log(`更新题目${questionNumber}数据:`, updateData);
        const { error: updateError } = await supabase
          .from("use_of_english_questions")
          .update(updateData)
          .eq("paper_id", paperId)
          .eq("question_number", questionNumber);

        if (updateError) {
          throw updateError;
        }
      } else {
        console.log(`警告：题目${questionNumber}没有提取到任何数据`);
      }
    }

    return { success: true };
  } catch (error) {
    console.error("处理AI响应错误:", error);
    throw error;
  }
}

// POST接口：使用AI分析完形填空
export async function POST(request: NextRequest) {
  const totalStartTime = timeStart(
    "POST /api/ai/analyze-use-of-english 总耗时"
  );
  try {
    // 解析请求数据
    const requestData = await request.json();
    const { paper_id, original_text, options } = requestData;

    // 验证必填字段
    if (!paper_id || !original_text || !options) {
      timeEnd("POST /api/ai/analyze-use-of-english 总耗时", totalStartTime);
      return NextResponse.json(
        {
          success: false,
          error: "Missing required fields: paper_id, original_text, options",
        },
        { status: 400 }
      );
    }

    // 解析选项
    const parsedOptions = parseOptions(options);

    if (parsedOptions.length === 0) {
      timeEnd("POST /api/ai/analyze-use-of-english 总耗时", totalStartTime);
      return NextResponse.json(
        {
          success: false,
          error: "Failed to parse options",
        },
        { status: 400 }
      );
    }

    // 请求AI分析
    const aiStartTime = timeStart("AI分析请求");
    const aiResponse = await requestAIAnalysis(original_text, options);
    timeEnd("AI分析请求", aiStartTime);

    // 处理AI响应并更新数据库
    const processStartTime = timeStart("处理AI响应");
    await processAIResponse(aiResponse, paper_id, parsedOptions);
    timeEnd("处理AI响应", processStartTime);

    // 返回成功响应
    timeEnd("POST /api/ai/analyze-use-of-english 总耗时", totalStartTime);
    return NextResponse.json({
      success: true,
      message: "AI analysis completed successfully",
    });
  } catch (err) {
    console.error("AI分析错误:", err);
    timeEnd("POST /api/ai/analyze-use-of-english 总耗时", totalStartTime);
    return NextResponse.json(
      {
        success: false,
        error: err instanceof Error ? err.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
