import { createClient } from "@/lib/supabase/server";
import { NextResponse } from "next/server";
import { AlphabetLetter } from "@/types/vocabulary";

// 所有字母数组
const ALPHABET: AlphabetLetter[] = [
  "a",
  "b",
  "c",
  "d",
  "e",
  "f",
  "g",
  "h",
  "i",
  "j",
  "k",
  "l",
  "m",
  "n",
  "o",
  "p",
  "q",
  "r",
  "s",
  "t",
  "u",
  "v",
  "w",
  "x",
  "y",
  "z",
];

export async function GET() {
  try {
    const supabase = createClient();

    // 初始化计数对象
    const counts: Record<string, number> = {};
    ALPHABET.forEach((letter) => {
      counts[letter] = 0;
    });

    // 对每个字母执行单独查询
    await Promise.all(
      ALPHABET.map(async (letter) => {
        const { count, error } = await supabase
          .from("vocabulary")
          .select("*", { count: "exact", head: true })
          .eq("first_letter", letter);

        if (!error && count !== null) {
          counts[letter] = count;
        }
      })
    );

    // 添加缓存控制头，防止客户端过度缓存
    return NextResponse.json(
      { counts },
      {
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      }
    );
  } catch (error) {
    console.error("获取字母单词数量出错:", error);
    return NextResponse.json(
      { error: "获取字母单词数量出错" },
      { status: 500 }
    );
  }
}
