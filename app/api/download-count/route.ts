import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/db";

// 性能计时函数
function now() {
  return Date.now();
}

// 获取默认的免费下载次数
export async function GET(request: NextRequest) {
  const startTime = now();
  console.log("[GET] 开始获取下载次数", startTime);
  try {
    // 获取客户端IP地址
    const ipHeader =
      request.headers.get("x-forwarded-for") ||
      request.headers.get("x-real-ip");
    const ip = ipHeader ? ipHeader.split(",")[0].trim() : "unknown";

    // 转换 IPv6 localhost 地址为更易读的格式
    const normalizedIp = ip === "::1" ? "localhost" : ip;

    const ipTime = now();
    console.log(
      `[GET] 客户端IP处理完成: ${ip}, 规范化后: ${normalizedIp}, 耗时: ${ipTime - startTime}ms`
    );

    // 从配置表中获取免费下载次数限制
    console.log("[GET] 开始查询系统配置");
    const configStartTime = now();
    const { data: configData, error: configError } = await supabase
      .from("system_config")
      .select("value")
      .eq("key", "free_download_limit")
      .single();
    const configEndTime = now();
    console.log(
      `[GET] 系统配置查询完成, 耗时: ${configEndTime - configStartTime}ms`,
      configData,
      configError
    );

    if (configError) {
      console.error("[GET] 获取系统配置失败:", configError);
      // 如果获取配置失败，默认使用3次
      const defaultFreeDownloads = 3;

      // 查询当前IP的下载记录
      console.log(`[GET] 开始查询IP: ${normalizedIp} 的下载记录`);
      const downloadStartTime = now();
      const { data: downloadData, error: downloadError } = await supabase
        .from("download_counts")
        .select("count")
        .eq("ip_address", normalizedIp)
        .single();
      const downloadEndTime = now();
      console.log(
        `[GET] 下载记录查询完成, 耗时: ${downloadEndTime - downloadStartTime}ms`,
        downloadData,
        downloadError
      );

      if (downloadError && downloadError.code !== "PGRST116") {
        // PGRST116是未找到记录的错误码
        console.error("[GET] 获取下载次数失败:", downloadError);
        return NextResponse.json(
          { error: "获取下载次数失败" },
          { status: 500 }
        );
      }

      // 如果没有记录，创建一条新记录
      if (!downloadData) {
        console.log(`[GET] 未找到IP: ${normalizedIp} 的记录，创建新记录`);
        const insertStartTime = now();
        const { data, error } = await supabase.from("download_counts").insert([
          {
            ip_address: normalizedIp,
            count: 0,
          },
        ]);
        const insertEndTime = now();
        console.log(
          `[GET] 创建新记录完成, 耗时: ${insertEndTime - insertStartTime}ms`,
          data,
          error
        );

        const totalTime = now() - startTime;
        console.log(`[GET] 总响应时间: ${totalTime}ms`);
        return NextResponse.json({
          maxDownloads: defaultFreeDownloads,
          currentDownloads: 0,
        });
      }

      const totalTime = now() - startTime;
      console.log(`[GET] 总响应时间: ${totalTime}ms`);
      return NextResponse.json({
        maxDownloads: defaultFreeDownloads,
        currentDownloads: downloadData.count,
      });
    }

    // 从配置中获取免费下载次数限制
    const maxFreeDownloads = parseInt(configData.value, 10) || 3;
    console.log(`[GET] 免费下载次数限制: ${maxFreeDownloads}`);

    // 查询当前IP的下载记录
    console.log(`[GET] 开始查询IP: ${normalizedIp} 的下载记录`);
    const downloadStartTime = now();
    const { data: downloadData, error: downloadError } = await supabase
      .from("download_counts")
      .select("count")
      .eq("ip_address", normalizedIp)
      .single();
    const downloadEndTime = now();
    console.log(
      `[GET] 下载记录查询完成, 耗时: ${downloadEndTime - downloadStartTime}ms`,
      downloadData,
      downloadError
    );

    if (downloadError && downloadError.code !== "PGRST116") {
      console.error("[GET] 获取下载次数失败:", downloadError);
      return NextResponse.json({ error: "获取下载次数失败" }, { status: 500 });
    }

    // 如果没有记录，创建一条新记录
    if (!downloadData) {
      console.log(`[GET] 未找到IP: ${normalizedIp} 的记录，创建新记录`);
      const insertStartTime = now();
      const { data, error } = await supabase.from("download_counts").insert([
        {
          ip_address: normalizedIp,
          count: 0,
        },
      ]);
      const insertEndTime = now();
      console.log(
        `[GET] 创建新记录完成, 耗时: ${insertEndTime - insertStartTime}ms`,
        data,
        error
      );

      const totalTime = now() - startTime;
      console.log(`[GET] 总响应时间: ${totalTime}ms`);
      return NextResponse.json({
        maxDownloads: maxFreeDownloads,
        currentDownloads: 0,
      });
    }

    const totalTime = now() - startTime;
    console.log(`[GET] 返回下载次数信息, 总响应时间: ${totalTime}ms`);
    return NextResponse.json({
      maxDownloads: maxFreeDownloads,
      currentDownloads: downloadData.count,
    });
  } catch (error) {
    const totalTime = now() - startTime;
    console.error(
      `[GET] 获取下载次数发生错误, 总响应时间: ${totalTime}ms:`,
      error
    );
    return NextResponse.json({ error: "获取下载次数失败" }, { status: 500 });
  }
}

// 更新下载次数
export async function POST(request: NextRequest) {
  const startTime = now();
  console.log("[POST] 开始更新下载次数", startTime);
  try {
    // 获取客户端IP地址
    const ipHeader =
      request.headers.get("x-forwarded-for") ||
      request.headers.get("x-real-ip");
    const ip = ipHeader ? ipHeader.split(",")[0].trim() : "unknown";

    // 转换 IPv6 localhost 地址为更易读的格式
    const normalizedIp = ip === "::1" ? "localhost" : ip;

    const ipTime = now();
    console.log(
      `[POST] 客户端IP处理完成: ${ip}, 规范化后: ${normalizedIp}, 耗时: ${ipTime - startTime}ms`
    );

    // 查询当前IP的下载记录
    console.log(`[POST] 开始查询IP: ${normalizedIp} 的下载记录`);
    const downloadStartTime = now();
    const { data: downloadData, error: downloadError } = await supabase
      .from("download_counts")
      .select("count")
      .eq("ip_address", normalizedIp)
      .single();
    const downloadEndTime = now();
    console.log(
      `[POST] 下载记录查询完成, 耗时: ${downloadEndTime - downloadStartTime}ms`,
      downloadData,
      downloadError
    );

    if (downloadError && downloadError.code !== "PGRST116") {
      console.error("[POST] 获取下载次数失败:", downloadError);
      return NextResponse.json({ error: "获取下载次数失败" }, { status: 500 });
    }

    let currentCount = 0;

    // 更新或创建下载记录
    if (!downloadData) {
      // 创建新记录
      console.log(`[POST] 未找到IP: ${normalizedIp} 的记录，创建新记录`);
      const insertStartTime = now();
      const { data, error } = await supabase.from("download_counts").insert([
        {
          ip_address: normalizedIp,
          count: 1,
        },
      ]);
      const insertEndTime = now();
      console.log(
        `[POST] 创建新记录完成, 耗时: ${insertEndTime - insertStartTime}ms`,
        data,
        error
      );

      if (error) {
        console.error("[POST] 创建下载记录失败:", error);
        return NextResponse.json(
          { error: "更新下载次数失败" },
          { status: 500 }
        );
      }

      currentCount = 1;
    } else {
      // 更新已有记录
      const newCount = downloadData.count + 1;
      console.log(`[POST] 更新IP: ${normalizedIp} 的下载次数为: ${newCount}`);
      const updateStartTime = now();
      const { data, error } = await supabase
        .from("download_counts")
        .update({ count: newCount })
        .eq("ip_address", normalizedIp);
      const updateEndTime = now();
      console.log(
        `[POST] 更新记录完成, 耗时: ${updateEndTime - updateStartTime}ms`,
        data,
        error
      );

      if (error) {
        console.error("[POST] 更新下载记录失败:", error);
        return NextResponse.json(
          { error: "更新下载次数失败" },
          { status: 500 }
        );
      }

      currentCount = newCount;
    }

    // 从配置表中获取免费下载次数限制
    console.log("[POST] 开始查询系统配置");
    const configStartTime = now();
    const { data: configData, error: configError } = await supabase
      .from("system_config")
      .select("value")
      .eq("key", "free_download_limit")
      .single();
    const configEndTime = now();
    console.log(
      `[POST] 系统配置查询完成, 耗时: ${configEndTime - configStartTime}ms`,
      configData,
      configError
    );

    let maxFreeDownloads = 3; // 默认值
    if (!configError && configData) {
      maxFreeDownloads = parseInt(configData.value, 10) || 3;
    }
    console.log(`[POST] 免费下载次数限制: ${maxFreeDownloads}`);

    const totalTime = now() - startTime;
    console.log(`[POST] 返回更新后的下载次数信息, 总响应时间: ${totalTime}ms`);
    return NextResponse.json({
      success: true,
      maxDownloads: maxFreeDownloads,
      currentDownloads: currentCount,
    });
  } catch (error) {
    const totalTime = now() - startTime;
    console.error(
      `[POST] 更新下载次数发生错误, 总响应时间: ${totalTime}ms:`,
      error
    );
    return NextResponse.json({ error: "更新下载次数失败" }, { status: 500 });
  }
}
