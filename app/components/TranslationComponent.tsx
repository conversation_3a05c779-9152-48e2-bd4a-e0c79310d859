"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Highlight } from "../components/ui/highlight";
import {
  ChevronDown,
  ChevronUp,
  BookOpen,
  Lightbulb,
  AlertCircle,
  CheckCircle,
  Edit,
} from "lucide-react";

// 定义单个划线句子的翻译信息
interface TranslationLine {
  id: number;
  sentence_id: number;
  content: string; // 原句内容
  reference_translation: string;
  difficulty_analysis?: string;
  translation_skills?: string;
}

// 定义整段翻译信息
interface FullTranslation {
  id: number;
  paper_id: number;
  content: string; // 整段原文
  reference_translation: string;
  analysis?: string;
}

// 组件输入参数
interface TranslationComponentProps {
  examType: "英语一" | "英语二"; // 考试类型
  translation?: FullTranslation; // 英语二的整段翻译
  translationLines?: TranslationLine[]; // 英语一的划线句子翻译
  originalContent?: string; // 文章原文（用于英语一中高亮划线句子）
  onUserTranslate?: (text: string) => void; // 用户提交翻译的回调
}

const TranslationComponent: React.FC<TranslationComponentProps> = ({
  examType,
  translation,
  translationLines,
  originalContent,
  onUserTranslate,
}) => {
  const [userTranslation, setUserTranslation] = useState<string>("");
  const [showReference, setShowReference] = useState<boolean>(false);
  const [showAnalysis, setShowAnalysis] = useState<boolean>(false);
  const [activeLineId, setActiveLineId] = useState<number | null>(null);
  const [expandedSections, setExpandedSections] = useState<{
    [key: string]: boolean;
  }>({});

  // 用于切换展开/折叠状态
  const toggleSection = (section: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  // 处理用户翻译提交
  const handleSubmitTranslation = () => {
    if (onUserTranslate && userTranslation.trim()) {
      onUserTranslate(userTranslation);
    }
  };

  // 为英语一渲染所有句子，并高亮显示划线句子
  const renderEnglishOneContent = () => {
    if (
      !originalContent ||
      !translationLines ||
      translationLines.length === 0
    ) {
      return <p className="text-gray-500">暂无翻译内容</p>;
    }

    // 获取所有划线句子的内容，用于在原文中高亮显示
    const highlightedTexts = translationLines.map((line) => line.content);

    return (
      <div className="space-y-6">
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h3 className="text-lg font-semibold mb-2">原文</h3>
          <div className="text-gray-700 leading-relaxed">
            <Highlight
              content={originalContent}
              highlights={highlightedTexts}
              highlightClassName="bg-yellow-200 px-0.5 rounded"
            />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">划线句子翻译</h3>

          {translationLines.map((line) => (
            <Card
              key={line.id}
              className={activeLineId === line.id ? "border-blue-500" : ""}
            >
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-md font-medium">
                    划线句子
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      setActiveLineId(activeLineId === line.id ? null : line.id)
                    }
                  >
                    {activeLineId === line.id ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 mb-4 bg-yellow-50 p-2 rounded border-l-4 border-yellow-400">
                  {line.content}
                </p>

                {activeLineId === line.id && (
                  <div className="space-y-4">
                    <div className="mt-4">
                      <h4 className="text-sm font-medium mb-2 flex items-center">
                        <Edit className="h-4 w-4 mr-1" />
                        你的翻译
                      </h4>
                      <textarea
                        className="w-full p-2 border rounded-md h-24"
                        placeholder="输入你的翻译..."
                        value={userTranslation}
                        onChange={(e) => setUserTranslation(e.target.value)}
                      />
                      <div className="flex justify-end mt-2">
                        <Button size="sm" onClick={handleSubmitTranslation}>
                          提交翻译
                        </Button>
                      </div>
                    </div>

                    <div>
                      <div
                        className="flex justify-between items-center cursor-pointer"
                        onClick={() => setShowReference(!showReference)}
                      >
                        <h4 className="text-sm font-medium mb-2 flex items-center">
                          <BookOpen className="h-4 w-4 mr-1" />
                          参考翻译
                        </h4>
                        <Button variant="ghost" size="sm">
                          {showReference ? (
                            <ChevronUp className="h-4 w-4" />
                          ) : (
                            <ChevronDown className="h-4 w-4" />
                          )}
                        </Button>
                      </div>

                      {showReference && (
                        <div className="bg-gray-50 p-3 rounded-md">
                          <p className="text-gray-700">
                            {line.reference_translation}
                          </p>
                        </div>
                      )}
                    </div>

                    <div>
                      <div
                        className="flex justify-between items-center cursor-pointer"
                        onClick={() => setShowAnalysis(!showAnalysis)}
                      >
                        <h4 className="text-sm font-medium mb-2 flex items-center">
                          <Lightbulb className="h-4 w-4 mr-1" />
                          翻译分析
                        </h4>
                        <Button variant="ghost" size="sm">
                          {showAnalysis ? (
                            <ChevronUp className="h-4 w-4" />
                          ) : (
                            <ChevronDown className="h-4 w-4" />
                          )}
                        </Button>
                      </div>

                      {showAnalysis && (
                        <div className="space-y-4">
                          {line.difficulty_analysis && (
                            <div className="bg-gray-50 p-3 rounded-md">
                              <h5 className="text-sm font-medium mb-1">
                                翻译难点
                              </h5>
                              <div className="text-gray-700 text-sm whitespace-pre-line">
                                {line.difficulty_analysis}
                              </div>
                            </div>
                          )}

                          {line.translation_skills && (
                            <div className="bg-gray-50 p-3 rounded-md">
                              <h5 className="text-sm font-medium mb-1">
                                翻译技巧
                              </h5>
                              <div className="text-gray-700 text-sm whitespace-pre-line">
                                {line.translation_skills}
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  // 为英语二渲染整段翻译
  const renderEnglishTwoContent = () => {
    if (!translation) {
      return <p className="text-gray-500">暂无翻译内容</p>;
    }

    return (
      <div className="space-y-6">
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <h3 className="text-lg font-semibold mb-2">原文</h3>
          <p className="text-gray-700 leading-relaxed">{translation.content}</p>
        </div>

        <div className="mt-6">
          <h4 className="text-md font-medium mb-2">你的翻译</h4>
          <textarea
            className="w-full p-3 border rounded-md h-40"
            placeholder="输入你的翻译..."
            value={userTranslation}
            onChange={(e) => setUserTranslation(e.target.value)}
          />
          <div className="flex justify-end mt-2">
            <Button onClick={handleSubmitTranslation}>提交翻译</Button>
          </div>
        </div>

        <Card>
          <CardHeader className="pb-2">
            <div
              className="flex justify-between items-center cursor-pointer"
              onClick={() => toggleSection("reference")}
            >
              <CardTitle className="text-md font-medium flex items-center">
                <BookOpen className="h-5 w-5 mr-2" />
                参考翻译
              </CardTitle>
              <Button variant="ghost" size="sm">
                {expandedSections["reference"] ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </div>
          </CardHeader>
          {expandedSections["reference"] && (
            <CardContent>
              <div className="bg-gray-50 p-4 rounded-md">
                <p className="text-gray-700 whitespace-pre-line">
                  {translation.reference_translation}
                </p>
              </div>
            </CardContent>
          )}
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <div
              className="flex justify-between items-center cursor-pointer"
              onClick={() => toggleSection("analysis")}
            >
              <CardTitle className="text-md font-medium flex items-center">
                <Lightbulb className="h-5 w-5 mr-2" />
                翻译分析
              </CardTitle>
              <Button variant="ghost" size="sm">
                {expandedSections["analysis"] ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </div>
          </CardHeader>
          {expandedSections["analysis"] && (
            <CardContent>
              <div className="bg-gray-50 p-4 rounded-md">
                <div className="text-gray-700 whitespace-pre-line">
                  {translation.analysis}
                </div>
              </div>
            </CardContent>
          )}
        </Card>
      </div>
    );
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">
        {examType === "英语一"
          ? "英语一翻译（划线句子翻译）"
          : "英语二翻译（段落翻译）"}
      </h2>

      {examType === "英语一"
        ? renderEnglishOneContent()
        : renderEnglishTwoContent()}
    </div>
  );
};

export default TranslationComponent;
