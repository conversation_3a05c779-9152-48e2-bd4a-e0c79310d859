import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// 性能计时辅助函数
const timeStart = (label: string) => {
  console.time(`⏱️ ${label}`);
  return Date.now();
};

const timeEnd = (label: string, startTime: number) => {
  const duration = Date.now() - startTime;
  console.timeEnd(`⏱️ ${label}`);
  console.log(`🕒 ${label} 耗时: ${duration}ms`);
  return duration;
};

// 简单内存缓存实现
type CacheEntry = {
  data: any;
  expiry: number;
};

const cache: Record<string, CacheEntry> = {};

const cacheGet = (key: string) => {
  const entry = cache[key];
  if (entry && entry.expiry > Date.now()) {
    console.log(`🔄 使用缓存：${key}`);
    return entry.data;
  }
  return null;
};

const cacheSet = (key: string, data: any, ttlSeconds: number = 60) => {
  cache[key] = {
    data,
    expiry: Date.now() + ttlSeconds * 1000,
  };
};

// GET方法：获取真题列表
export async function GET(request: Request) {
  const totalStartTime = timeStart("GET /api/papers 总耗时");
  try {
    const parseParamsStartTime = timeStart("解析请求参数");
    const { searchParams } = new URL(request.url);

    // 检查是否为简单列表模式（用于下拉选择）
    const isListMode = searchParams.get("listMode") === "true";

    const page = parseInt(searchParams.get("page") || "1");
    // 检查是否有limit参数，有则使用limit参数，否则使用pageSize参数
    const limit = searchParams.get("limit");
    const pageSize = limit
      ? parseInt(limit)
      : parseInt(searchParams.get("pageSize") || "10");
    const year = searchParams.get("year");
    const type = searchParams.get("type");
    // 同时支持 sectionType 和 section_type 两种参数名
    const sectionType =
      searchParams.get("sectionType") || searchParams.get("section_type");

    // 计算偏移量
    const offset = (page - 1) * pageSize;
    timeEnd("解析请求参数", parseParamsStartTime);

    // 尝试从缓存获取数据
    if (isListMode) {
      // 列表模式缓存键
      const cacheKey = `papers_list_${year || "all"}_${type || "all"}_${sectionType || "all"}`;
      const cachedData = cacheGet(cacheKey);

      if (cachedData) {
        timeEnd("GET /api/papers 总耗时", totalStartTime);
        return NextResponse.json({
          success: true,
          papers: cachedData,
          from_cache: true,
        });
      }

      // 构建查询
      const buildQueryStartTime = timeStart("构建查询");
      let query = supabase.from("papers").select("id, year, type");

      // 添加过滤条件
      if (year) {
        query = query.eq("year", year);
      }

      if (type) {
        query = query.eq("type", type);
      }

      if (sectionType) {
        query = query.eq("section_type", sectionType);
      }
      timeEnd("构建查询", buildQueryStartTime);

      const listModeQueryStartTime = timeStart("列表模式查询");
      const { data, error } = await query
        .order("year", { ascending: false })
        .order("type");
      timeEnd("列表模式查询", listModeQueryStartTime);

      if (error) {
        throw error;
      }

      const formatDataStartTime = timeStart("格式化列表数据");
      // 添加title字段
      const papersWithTitle = data.map((paper) => ({
        ...paper,
        title: `${paper.year}年全国硕士研究生招生考试${paper.type}`,
      }));
      timeEnd("格式化列表数据", formatDataStartTime);

      // 缓存结果，列表数据缓存10分钟
      cacheSet(cacheKey, papersWithTitle, 600);

      timeEnd("GET /api/papers 总耗时", totalStartTime);
      return NextResponse.json({
        success: true,
        papers: papersWithTitle,
      });
    }

    // 分页数据查询缓存键
    const cacheKey = `papers_page_${page}_${pageSize}_${year || "all"}_${type || "all"}_${sectionType || "all"}`;
    const cachedData = cacheGet(cacheKey);

    if (cachedData) {
      timeEnd("GET /api/papers 总耗时", totalStartTime);
      return NextResponse.json({
        success: true,
        ...cachedData,
        from_cache: true,
      });
    }

    // 合并查询 - 同时获取数据和总数
    const combinedQueryStartTime = timeStart("合并查询");

    // 构建基础过滤条件
    let filters: Record<string, any> = {};

    if (year) {
      filters.year = year;
    }

    if (type) {
      filters.type = type;
    }

    if (sectionType) {
      filters.section_type = sectionType;
    }

    // 计数查询
    const countQuery = supabase
      .from("papers")
      .select("id", { count: "exact", head: true });

    // 应用过滤条件到计数查询
    Object.entries(filters).forEach(([key, value]) => {
      countQuery.eq(key, value);
    });

    // 数据查询
    const dataQuery = supabase
      .from("papers")
      .select(
        "id, year, type, section_type, content, reference_translation, translation_analysis, created_at, updated_at"
      );

    // 应用过滤条件到数据查询
    Object.entries(filters).forEach(([key, value]) => {
      dataQuery.eq(key, value);
    });

    // 添加排序和分页到数据查询
    dataQuery
      .order("year", { ascending: false })
      .order("type")
      .range(offset, offset + pageSize - 1);

    // 并行执行两个查询
    const [countResult, dataResult] = await Promise.all([
      countQuery,
      dataQuery,
    ]);

    timeEnd("合并查询", combinedQueryStartTime);

    // 处理错误
    if (countResult.error) {
      throw countResult.error;
    }

    if (dataResult.error) {
      throw dataResult.error;
    }

    const count = countResult.count || 0;
    const total = count;
    const totalPages = Math.ceil(total / pageSize);

    // 转换数据格式以保持与原有API兼容
    const formatDataStartTime = timeStart("格式化数据");
    const formattedData = dataResult.data.map((paper) => ({
      id: paper.id,
      year: paper.year,
      type: paper.type,
      sectionType: paper.section_type,
      content: paper.content,
      reference_translation: paper.reference_translation,
      translation_analysis: paper.translation_analysis,
      title: `${paper.year}年全国硕士研究生招生考试${paper.type}试题`,
      createdAt: paper.created_at,
      updatedAt: paper.updated_at,
    }));
    timeEnd("格式化数据", formatDataStartTime);

    // 准备返回数据
    const responseData = {
      data: formattedData,
      pagination: {
        total: count,
        page,
        pageSize,
        totalPages,
      },
    };

    // 缓存结果 - 分页数据缓存2分钟
    cacheSet(cacheKey, responseData, 120);

    timeEnd("GET /api/papers 总耗时", totalStartTime);
    return NextResponse.json({
      success: true,
      ...responseData,
    });
  } catch (error) {
    timeEnd("GET /api/papers 总耗时", totalStartTime);
    console.error("Error fetching papers:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch papers",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// POST方法：创建新试题
export async function POST(request: Request) {
  const totalStartTime = timeStart("POST /api/papers 总耗时");
  try {
    // 获取请求数据
    const parseBodyStartTime = timeStart("解析请求体");
    const paperData = await request.json();
    timeEnd("解析请求体", parseBodyStartTime);

    // 验证必填字段
    if (!paperData.year || !paperData.type || !paperData.section_type) {
      timeEnd("POST /api/papers 总耗时", totalStartTime);
      return NextResponse.json(
        {
          error: "Missing required fields",
          details: "year, type, and section_type are required",
        },
        { status: 400 }
      );
    }

    // 准备插入数据
    const prepareDataStartTime = timeStart("准备插入数据");
    const insertData = {
      year: paperData.year,
      type: paperData.type,
      section_type: paperData.section_type || paperData.sectionType,
    };
    timeEnd("准备插入数据", prepareDataStartTime);

    // 执行Supabase插入
    const insertStartTime = timeStart("执行数据插入");
    const { data, error } = await supabase
      .from("papers")
      .insert(insertData)
      .select("id")
      .single();
    timeEnd("执行数据插入", insertStartTime);

    if (error) {
      throw error;
    }

    // 清除受影响的缓存
    Object.keys(cache).forEach((key) => {
      if (key.startsWith("papers_")) {
        delete cache[key];
      }
    });

    // 返回成功响应
    timeEnd("POST /api/papers 总耗时", totalStartTime);
    return NextResponse.json({
      success: true,
      message: "Paper saved successfully",
      data: {
        id: data.id,
      },
    });
  } catch (error) {
    timeEnd("POST /api/papers 总耗时", totalStartTime);
    console.error("Error saving paper:", error);
    return NextResponse.json(
      {
        error: "Failed to save paper",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
