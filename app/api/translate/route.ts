import { NextResponse } from "next/server";
import { DeepSeek } from "@/app/services/deepseek";
import { supabase } from "@/lib/db";

export async function POST(request: Request) {
  try {
    const { type, content, sentenceId, paperId } = await request.json();

    if (!type || !content) {
      return NextResponse.json(
        { error: "Type and content are required" },
        { status: 400 }
      );
    }

    // 根据类型处理不同的翻译请求
    if (type === "sentence" && sentenceId) {
      // 句子翻译（用于英语一划线句子翻译）
      return await handleSentenceTranslation(sentenceId, content);
    } else if (type === "paragraph" && paperId) {
      // 段落翻译（用于英语二段落翻译）
      return await handleParagraphTranslation(paperId, content);
    } else {
      return NextResponse.json(
        { error: "Invalid type or missing required parameters" },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Translation error:", error);
    return NextResponse.json(
      {
        error: "Failed to translate",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// 处理英语一划线句子翻译
async function handleSentenceTranslation(sentenceId: number, content: string) {
  // 调用AI进行翻译分析
  const prompt = `
你是一位精通英汉翻译的考研英语专家。请对以下英语句子进行翻译分析。

原句：
${content}

请提供以下内容：
1. 准确、地道的中文翻译
2. 翻译难点分析（分点列出）
3. 翻译技巧（分点列出）

请以JSON格式返回分析结果，格式如下：
{
  "reference_translation": "中文翻译",
  "difficulty_analysis": "翻译难点分析，包括语法结构、词汇难点等",
  "translation_skills": "翻译技巧，包括句式转换、词语选择等具体建议"
}

确保返回的是有效的JSON格式，不包含其他说明文字。
`;

  const deepseek = new DeepSeek();
  const response = await deepseek.chat({
    messages: [
      {
        role: "user",
        content: prompt,
      },
    ],
  });

  try {
    // 解析AI返回的JSON
    const analysis = JSON.parse(response.choices[0].message.content.trim());

    // 检查是否已存在记录
    const { data: existingRows, error: checkError } = await supabase
      .from("translation_lines")
      .select("id")
      .eq("sentence_id", sentenceId);

    if (checkError) {
      throw checkError;
    }

    if (existingRows && existingRows.length > 0) {
      // 更新现有记录
      const { error: updateError } = await supabase
        .from("translation_lines")
        .update({
          reference_translation: analysis.reference_translation,
          difficulty_analysis: analysis.difficulty_analysis,
          translation_skills: analysis.translation_skills,
        })
        .eq("sentence_id", sentenceId);

      if (updateError) {
        throw updateError;
      }
    } else {
      // 创建新记录
      const { error: insertError } = await supabase
        .from("translation_lines")
        .insert({
          sentence_id: sentenceId,
          reference_translation: analysis.reference_translation,
          difficulty_analysis: analysis.difficulty_analysis,
          translation_skills: analysis.translation_skills,
        });

      if (insertError) {
        console.error("插入错误:", insertError);
        return NextResponse.json(
          {
            error: "Failed to save translation",
            details: insertError.message,
          },
          { status: 500 }
        );
      }
    }

    // 确保句子标记为划线句子
    const { error: updateSentenceError } = await supabase
      .from("sentences")
      .update({ is_marked: true })
      .eq("id", sentenceId);

    if (updateSentenceError) {
      console.error("更新句子标记错误:", updateSentenceError);
    }

    return NextResponse.json({
      success: true,
      data: analysis,
    });
  } catch (parseError) {
    console.error("Failed to parse AI response:", parseError);
    return NextResponse.json(
      {
        error: "Invalid translation analysis format",
        details:
          parseError instanceof Error
            ? parseError.message
            : "Unknown parsing error",
      },
      { status: 422 }
    );
  }
}

// 处理英语二段落翻译
async function handleParagraphTranslation(paperId: number, content: string) {
  // 调用AI进行翻译分析
  const prompt = `
你是一位精通英汉翻译的考研英语专家。请对以下英语段落进行翻译分析。

原文：
${content}

请提供以下内容：
1. 准确、地道的中文翻译
2. 翻译分析（包括难点、关键词汇和表达方式等）

请以JSON格式返回分析结果，格式如下：
{
  "reference_translation": "完整的中文翻译",
  "analysis": "详细的翻译分析，包括翻译难点、应用的翻译技巧等"
}

JSON格式注意事项：
1. 必须使用英文双引号(")作为JSON的引号，不能使用中文引号("")
2. 在JSON内容中，不要使用撇号(')表示缩写，如don't、we're等，请写成完整形式：do not、we are
3. 如需在内容中使用引号，请用转义形式(\\")
4. 返回的JSON必须是严格有效的，只包含reference_translation和analysis两个字段
5. 不要添加任何前缀或后缀，直接返回JSON对象

非常重要：确保你的回复是能被JSON.parse()直接解析的有效JSON格式。
`;

  try {
    const deepseek = new DeepSeek();
    const response = await deepseek.chat({
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
    });

    // 获取并处理响应内容
    const responseContent = response.choices[0].message.content.trim();
    console.log("AI返回的原始内容:", responseContent);

    // 尝试提取有效的JSON
    let jsonMatch = responseContent.match(/\{[\s\S]*\}/);
    let jsonStr = jsonMatch ? jsonMatch[0] : responseContent;

    // 替换可能存在的单引号问题
    jsonStr = jsonStr.replace(/(\w)"(\w)/g, "$1'$2");
    // 替换引号问题
    jsonStr = jsonStr.replace(/[""]|['']|[「」]/g, '"');

    // 尝试解析JSON
    let analysis;
    try {
      const result = JSON.parse(jsonStr);

      // 验证返回的数据格式是否正确
      if (!result.reference_translation || !result.analysis) {
        throw new Error("AI返回的数据缺少必要字段");
      }

      // 检查是否已存在翻译
      const { data: existingTranslation, error: checkError } = await supabase
        .from("translations")
        .select("id")
        .eq("paper_id", paperId);

      if (checkError) {
        throw checkError;
      }

      if (existingTranslation && existingTranslation.length > 0) {
        // 更新现有记录
        const { error: updateError } = await supabase
          .from("translations")
          .update({
            content,
            reference_translation: result.reference_translation,
            analysis: result.analysis,
          })
          .eq("paper_id", paperId);

        if (updateError) {
          throw updateError;
        }
      } else {
        // 插入新记录
        const { error: insertError } = await supabase
          .from("translations")
          .insert({
            paper_id: paperId,
            content,
            reference_translation: result.reference_translation,
            analysis: result.analysis,
          });

        if (insertError) {
          throw insertError;
        }
      }

      // 同时更新paper表
      const { error: updatePaperError } = await supabase
        .from("papers")
        .update({
          reference_translation: result.reference_translation,
          translation_analysis: result.analysis,
        })
        .eq("id", paperId);

      if (updatePaperError) {
        console.error("更新paper表错误:", updatePaperError);
      }

      return NextResponse.json({
        success: true,
        data: {
          reference_translation: result.reference_translation,
          analysis: result.analysis,
        },
      });
    } catch (parseError) {
      console.error("JSON解析错误:", parseError);
      console.error("尝试解析的JSON字符串:", jsonStr);

      // 构建一个替代响应
      return NextResponse.json(
        {
          error: "JSON解析错误",
          details:
            parseError instanceof Error
              ? parseError.message
              : String(parseError),
        },
        { status: 422 }
      );
    }
  } catch (error) {
    console.error("调用AI接口错误:", error);
    return NextResponse.json(
      {
        error: "调用AI接口错误",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const sentenceId = searchParams.get("sentenceId");
    const paperId = searchParams.get("paperId");
    const type = searchParams.get("type") || "sentence";

    if (!sentenceId && !paperId) {
      return NextResponse.json(
        { error: "sentenceId or paperId is required" },
        { status: 400 }
      );
    }

    if (type === "sentence" && sentenceId) {
      // 获取句子翻译
      const { data, error } = await supabase
        .from("translation_lines")
        .select("*")
        .eq("sentence_id", sentenceId)
        .single();

      if (error && error.code !== "PGRST116") {
        // PGRST116是没有找到记录的错误码
        throw error;
      }

      return NextResponse.json({
        success: true,
        data: data || null,
      });
    } else if (type === "paragraph" && paperId) {
      // 获取段落翻译
      const { data, error } = await supabase
        .from("translations")
        .select("*")
        .eq("paper_id", paperId)
        .single();

      if (error && error.code !== "PGRST116") {
        throw error;
      }

      return NextResponse.json({
        success: true,
        data: data || null,
      });
    } else {
      return NextResponse.json(
        { error: "Invalid type or missing required parameters" },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Error fetching translation:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch translation",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
