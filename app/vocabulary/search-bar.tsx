"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useState, useEffect, useRef, KeyboardEvent } from "react";
import { Search } from "lucide-react";
import { useDebounce } from "@/app/hooks/use-debounce";

interface WordSuggestion {
  word: string;
  simple_translation: string;
}

export default function SearchBar() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchTerm, setSearchTerm] = useState(
    searchParams.get("search") || ""
  );
  const [suggestions, setSuggestions] = useState<WordSuggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setSearchTerm(searchParams.get("search") || "");
  }, [searchParams]);

  useEffect(() => {
    fetchSuggestions();
  }, [debouncedSearchTerm]);

  const fetchSuggestions = async () => {
    setLoading(true);
    try {
      const url =
        debouncedSearchTerm.length > 0
          ? `/api/vocabulary/suggest?q=${encodeURIComponent(debouncedSearchTerm)}`
          : "/api/vocabulary/suggest";

      const response = await fetch(url);
      const data = await response.json();
      setSuggestions(data.data || []);
      setSelectedIndex(-1);
    } catch (error) {
      console.error("获取单词建议失败:", error);
      setSuggestions([]);
    } finally {
      setLoading(false);
    }
  };

  // 点击外部关闭建议列表
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        inputRef.current !== event.target
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (!showSuggestions || suggestions.length === 0) return;

    // 上箭头
    if (e.key === "ArrowUp") {
      e.preventDefault();
      const newIndex =
        selectedIndex <= 0 ? suggestions.length - 1 : selectedIndex - 1;
      setSelectedIndex(newIndex);
      scrollToItem(newIndex);
    }
    // 下箭头
    else if (e.key === "ArrowDown") {
      e.preventDefault();
      const newIndex =
        selectedIndex >= suggestions.length - 1 ? 0 : selectedIndex + 1;
      setSelectedIndex(newIndex);
      scrollToItem(newIndex);
    }
    // 回车
    else if (e.key === "Enter" && selectedIndex >= 0) {
      e.preventDefault();
      handleSuggestionClick(suggestions[selectedIndex].word);
    }
  };

  // 滚动到选中的项目
  const scrollToItem = (index: number) => {
    if (!suggestionsRef.current) return;

    const suggestionItems = suggestionsRef.current.querySelectorAll("li");
    if (suggestionItems.length > index) {
      const selectedItem = suggestionItems[index];
      selectedItem.scrollIntoView({
        block: "nearest",
        behavior: "smooth",
      });
    }
  };

  // 获取单词首字母（返回小写）
  const getFirstLetter = (word: string): string => {
    if (!word || word.trim() === "") return "a";
    return word.trim().charAt(0).toLowerCase();
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setShowSuggestions(false);

    if (!searchTerm || searchTerm.trim() === "") {
      // 如果搜索词为空，保持在当前字母页
      const currentLetter = searchParams.get("letter") || "a";
      router.push(`/vocabulary?letter=${currentLetter}`);
      return;
    }

    // 获取搜索词的首字母
    const firstLetter = getFirstLetter(searchTerm);

    const params = new URLSearchParams();
    params.set("letter", firstLetter);
    params.set("search", searchTerm.trim());

    router.push(`/vocabulary?${params.toString()}`);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setSearchTerm(suggestion);
    setShowSuggestions(false);

    if (!suggestion || suggestion.trim() === "") {
      // 如果建议词为空，保持在当前字母页
      const currentLetter = searchParams.get("letter") || "a";
      router.push(`/vocabulary?letter=${currentLetter}`);
      return;
    }

    // 获取建议词的首字母
    const firstLetter = getFirstLetter(suggestion);

    const params = new URLSearchParams();
    params.set("letter", firstLetter);
    params.set("search", suggestion.trim());

    router.push(`/vocabulary?${params.toString()}`);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchTerm(newValue);
    setShowSuggestions(true);

    // 当用户清空搜索框时，自动跳转到显示当前字母的单词
    if (newValue.trim() === "" && searchParams.get("search")) {
      const params = new URLSearchParams();

      // 保留当前字母筛选器的设置，默认为a
      const currentLetter = searchParams.get("letter") || "a";
      params.set("letter", currentLetter);

      router.push(`/vocabulary?${params.toString()}`);
    }
  };

  return (
    <form onSubmit={handleSearch} className="w-full relative">
      <div className="relative">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <Search className="w-5 h-5 text-blue-500" />
        </div>
        <input
          ref={inputRef}
          type="search"
          value={searchTerm}
          onChange={handleInputChange}
          onFocus={() => {
            setShowSuggestions(true);
            // 当输入框聚焦且无内容时，主动获取建议
            if (searchTerm.length === 0 && suggestions.length === 0) {
              fetchSuggestions();
            }
          }}
          onKeyDown={handleKeyDown}
          className="block w-full p-3 pl-10 text-gray-900 bg-gray-50 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
          placeholder="输入单词进行搜索..."
          autoComplete="off"
        />
        <button
          type="submit"
          className="absolute right-2.5 bottom-2 top-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-300 focus:ring-offset-2"
        >
          搜索
        </button>
      </div>

      {/* 单词建议列表 */}
      {showSuggestions && (
        <div
          ref={suggestionsRef}
          className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-auto"
        >
          {loading ? (
            <div className="p-3 text-center">
              <div className="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2"></div>
              <span className="text-gray-500">加载中...</span>
            </div>
          ) : suggestions.length > 0 ? (
            <ul className="py-1">
              {suggestions.map((suggestion, index) => (
                <li
                  key={index}
                  onClick={() => handleSuggestionClick(suggestion.word)}
                  className={`px-4 py-2.5 cursor-pointer transition-colors ${
                    index === selectedIndex ? "bg-blue-50" : "hover:bg-gray-50"
                  }`}
                >
                  <div className="font-medium text-gray-800">
                    {suggestion.word}
                  </div>
                  <div className="text-sm text-gray-500">
                    {suggestion.simple_translation}
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <div className="p-4 text-center text-gray-500">
              未找到匹配的单词
            </div>
          )}
        </div>
      )}
    </form>
  );
}
