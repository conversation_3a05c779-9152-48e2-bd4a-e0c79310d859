-- 创建下载计数表
CREATE TABLE IF NOT EXISTS download_counts (
  id SERIAL PRIMARY KEY,
  ip_address TEXT NOT NULL,
  count INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(ip_address)
);

-- 创建系统配置表
CREATE TABLE IF NOT EXISTS system_config (
  id SERIAL PRIMARY KEY,
  key TEXT NOT NULL,
  value TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(key)
);

-- 插入默认配置：免费下载次数限制
INSERT INTO system_config (key, value, description)
VALUES ('free_download_limit', '3', '非订阅用户的免费下载次数限制')
ON CONFLICT (key) DO NOTHING;

-- 创建触发器函数，用于自动更新updated_at字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为download_counts表添加更新时间的触发器
CREATE TRIGGER update_download_counts_updated_at
BEFORE UPDATE ON download_counts
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 为system_config表添加更新时间的触发器
CREATE TRIGGER update_system_config_updated_at
BEFORE UPDATE ON system_config
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column(); 