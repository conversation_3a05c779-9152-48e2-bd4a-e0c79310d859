"use client";

import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { Lock } from "lucide-react";

// 验证组件
const AdminAuthCheck = ({ children }: { children: React.ReactNode }) => {
  const [key, setKey] = useState("");
  const [error, setError] = useState("");
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // 组件挂载后自动聚焦到输入框
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (key === "zhangjingjie") {
      setIsAuthenticated(true);
      setError("");
    } else {
      setError("密钥错误，请重新输入");
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
        <div className="max-w-md w-full p-8 bg-white rounded-lg shadow-md">
          <div className="text-center mb-6">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-100 text-blue-600 mb-4">
              <Lock className="w-8 h-8" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900">管理员验证</h1>
            <p className="text-gray-600 mt-2">请输入管理员密钥以继续访问</p>
          </div>

          {error && (
            <div className="bg-red-50 text-red-600 p-3 rounded-md mb-4">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label
                htmlFor="key"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                管理员密钥
              </label>
              <input
                type="password"
                id="key"
                ref={inputRef}
                value={key}
                onChange={(e) => setKey(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入密钥"
                required
              />
            </div>
            <button
              type="submit"
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
            >
              验证并进入
            </button>
          </form>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const pathname = usePathname();
  const router = useRouter();

  return (
    <AdminAuthCheck>
      <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100">
        {/* 侧边栏 */}
        <div
          className={`fixed inset-y-0 left-0 z-30 bg-white shadow-xl transform transition-transform duration-300 ease-in-out ${
            isSidebarOpen ? "translate-x-0" : "-translate-x-full"
          }`}
          style={{ width: "280px" }}
        >
          <div className="p-8">
            <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
              考研英语学习
            </h1>
          </div>
          <nav className="mt-8 px-4">
            <Link
              href="/admin/analytics"
              className={`flex items-center px-6 py-4 rounded-xl mb-2 transition-all duration-200 hover:scale-105 hover:shadow-md ${
                pathname && pathname.startsWith("/admin/analytics")
                  ? "bg-blue-50 text-blue-700"
                  : "text-gray-700 hover:bg-blue-50"
              }`}
            >
              <span className="mr-4 text-xl">📊</span>
              <span className="text-lg">下载统计分析</span>
            </Link>
            <Link
              href="/admin/papers"
              className={`flex items-center px-6 py-4 rounded-xl mb-2 transition-all duration-200 hover:scale-105 hover:shadow-md ${
                pathname && pathname.startsWith("/admin/papers")
                  ? "bg-blue-50 text-blue-700"
                  : "text-gray-700 hover:bg-blue-50"
              }`}
            >
              <span className="mr-4 text-xl">📝</span>
              <span className="text-lg">阅读真题管理</span>
            </Link>
            <Link
              href="/admin/sentences"
              className={`flex items-center px-6 py-4 rounded-xl mb-2 transition-all duration-200 hover:scale-105 hover:shadow-md ${
                pathname && pathname.startsWith("/admin/sentences")
                  ? "bg-blue-50 text-blue-700"
                  : "text-gray-700 hover:bg-blue-50"
              }`}
            >
              <span className="mr-4 text-xl">📚</span>
              <span className="text-lg">阅读句子管理</span>
            </Link>
            <Link
              href="/admin/use-of-english"
              className={`flex items-center px-6 py-4 rounded-xl mb-2 transition-all duration-200 hover:scale-105 hover:shadow-md ${
                pathname && pathname.startsWith("/admin/use-of-english")
                  ? "bg-blue-50 text-blue-700"
                  : "text-gray-700 hover:bg-blue-50"
              }`}
            >
              <span className="mr-4 text-xl">📋</span>
              <span className="text-lg">完形填空管理</span>
            </Link>
            <Link
              href="/admin/translations"
              className={`flex items-center px-6 py-4 rounded-xl mb-2 transition-all duration-200 hover:scale-105 hover:shadow-md ${
                pathname && pathname.startsWith("/admin/translations")
                  ? "bg-blue-50 text-blue-700"
                  : "text-gray-700 hover:bg-blue-50"
              }`}
            >
              <span className="mr-4 text-xl">🔄</span>
              <span className="text-lg">翻译管理</span>
            </Link>
            <Link
              href="/admin/writing-tasks"
              className={`flex items-center px-6 py-4 rounded-xl mb-2 transition-all duration-200 hover:scale-105 hover:shadow-md ${
                pathname && pathname.startsWith("/admin/writing-tasks")
                  ? "bg-blue-50 text-blue-700"
                  : "text-gray-700 hover:bg-blue-50"
              }`}
            >
              <span className="mr-4 text-xl">✍️</span>
              <span className="text-lg">写作管理</span>
            </Link>
            <Link
              href="/admin/resources"
              className={`flex items-center px-6 py-4 rounded-xl mb-2 transition-all duration-200 hover:scale-105 hover:shadow-md ${
                pathname && pathname.startsWith("/admin/resources")
                  ? "bg-blue-50 text-blue-700"
                  : "text-gray-700 hover:bg-blue-50"
              }`}
            >
              <span className="mr-4 text-xl">📁</span>
              <span className="text-lg">资源管理</span>
            </Link>
          </nav>
        </div>

        {/* 主内容区 */}
        <div
          className={`min-h-screen transition-all duration-300 ease-in-out ${
            isSidebarOpen ? "ml-[280px]" : "ml-0"
          }`}
        >
          {/* 顶部导航栏 */}
          <header className="bg-white shadow-md sticky top-0 z-20">
            <div className="flex justify-between items-center px-8 py-5">
              <div className="flex items-center">
                <button
                  onClick={() => setIsSidebarOpen(!isSidebarOpen)}
                  className="p-2 rounded-lg text-gray-600 hover:bg-gray-100 focus:outline-none transition-all duration-200 hover:scale-105"
                >
                  <svg
                    className="w-7 h-7"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d={
                        isSidebarOpen
                          ? "M4 6h16M4 12h16M4 18h16"
                          : "M4 6h16M4 12h16M4 18h16"
                      }
                    />
                  </svg>
                </button>
                <h2 className="ml-6 text-2xl font-semibold text-gray-800">
                  管理后台
                </h2>
              </div>
              <div className="flex items-center space-x-4">
                <span className="text-gray-600 text-lg">管理员</span>
              </div>
            </div>
          </header>

          {/* 主要内容 */}
          <main className="p-8 w-full min-h-[calc(100vh-4rem)]">
            {children}
          </main>
        </div>
      </div>
    </AdminAuthCheck>
  );
}
