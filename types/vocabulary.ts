export interface Vocabulary {
  id: number;
  word: string;
  first_letter: string;
  simple_translation: string;
  pronunciation: string;
  analysis: string;
  draw_explain: string;
  draw_prompt: string;
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

export type AlphabetLetter =
  | "a"
  | "b"
  | "c"
  | "d"
  | "e"
  | "f"
  | "g"
  | "h"
  | "i"
  | "j"
  | "k"
  | "l"
  | "m"
  | "n"
  | "o"
  | "p"
  | "q"
  | "r"
  | "s"
  | "t"
  | "u"
  | "v"
  | "w"
  | "x"
  | "y"
  | "z";

export interface VocabularyFilters {
  letter?: AlphabetLetter | "all";
  search?: string;
}
