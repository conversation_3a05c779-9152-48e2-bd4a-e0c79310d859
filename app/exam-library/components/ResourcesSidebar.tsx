"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { createClient } from "@supabase/supabase-js";

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || "";
const supabase = createClient(supabaseUrl, supabaseKey);

interface Resource {
  id: string;
  name: string;
  url: string;
}

export default function ResourcesSidebar() {
  const [recentResources, setRecentResources] = useState<Resource[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchRecentResources() {
      try {
        setLoading(true);
        const { data, error } = await supabase.storage
          .from("mbdata")
          .list("resources", {
            sortBy: { column: "created_at", order: "desc" },
            limit: 5,
          });

        if (error) {
          throw error;
        }

        if (data) {
          const resources = await Promise.all(
            data.map(async (file) => {
              const { data: urlData } = supabase.storage
                .from("mbdata")
                .getPublicUrl(`resources/${file.name}`);

              return {
                id: file.id,
                name: file.name,
                url: urlData.publicUrl,
              };
            })
          );

          setRecentResources(resources);
        }
      } catch (error) {
        console.error("获取最近资源失败:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchRecentResources();
  }, []);

  function getFileIcon(fileName: string) {
    const extension = fileName.split(".").pop()?.toLowerCase();
    if (extension === "pdf") return "📄";
    if (extension === "doc" || extension === "docx") return "📝";
    if (extension === "xls" || extension === "xlsx") return "📊";
    if (extension === "ppt" || extension === "pptx") return "📑";
    return "📁";
  }

  return (
    <div className="bg-white rounded-xl shadow-md p-5">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">学习资源</h3>
        <Link
          href="/learning-resources"
          className="text-sm text-blue-600 hover:text-blue-800 transition-colors"
        >
          查看全部
        </Link>
      </div>

      {loading ? (
        <div className="animate-pulse space-y-3">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-full"></div>
            </div>
          ))}
        </div>
      ) : recentResources.length === 0 ? (
        <div className="text-center py-6 text-gray-500">
          <div className="text-4xl mb-2">📚</div>
          <p className="text-sm">资源准备中</p>
        </div>
      ) : (
        <div className="space-y-3">
          {recentResources.map((resource) => (
            <Link
              key={resource.id}
              href={resource.url}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center p-2 rounded-lg hover:bg-gray-50 transition-colors group"
            >
              <span className="text-2xl mr-3">
                {getFileIcon(resource.name)}
              </span>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-800 truncate group-hover:text-blue-600">
                  {resource.name}
                </p>
              </div>
              <svg
                className="w-5 h-5 text-gray-400 group-hover:text-blue-500"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </Link>
          ))}
        </div>
      )}

      <div className="mt-5 pt-4 border-t border-gray-100">
        <Link
          href="/learning-resources"
          className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
        >
          <svg
            className="w-4 h-4 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          浏览更多学习资源
        </Link>
      </div>
    </div>
  );
}
