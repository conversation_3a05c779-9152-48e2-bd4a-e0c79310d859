import { createClient } from "@/lib/supabase/server";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get("q") || "";

    const supabase = createClient();
    let dbQuery = supabase
      .from("vocabulary")
      .select("word, simple_translation")
      .limit(10);

    // 如果有搜索词，则根据搜索词进行筛选
    if (query && query.trim() !== "") {
      dbQuery = dbQuery.ilike("word", `%${query}%`).order("word");
    } else {
      // 如果没有搜索词，则返回随机单词
      dbQuery = dbQuery.order("word", { ascending: true }).limit(10);
    }

    const { data, error } = await dbQuery;

    if (error) {
      console.error("获取单词建议失败:", error);
      return NextResponse.json({ error: "获取单词建议失败" }, { status: 500 });
    }

    // 添加缓存控制头，防止客户端过度缓存
    return NextResponse.json(
      { data: data || [] },
      {
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      }
    );
  } catch (error) {
    console.error("获取单词建议出错:", error);
    return NextResponse.json({ error: "获取单词建议出错" }, { status: 500 });
  }
}
