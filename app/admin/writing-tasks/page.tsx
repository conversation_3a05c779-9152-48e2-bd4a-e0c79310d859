"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../components/ui/table";
import {
  PlusCircle,
  Search,
  RefreshCw,
  Image as ImageIcon,
  Edit,
  Calendar,
  Info,
} from "lucide-react";

interface WritingTask {
  id: number;
  year: string;
  type: string;
  section_type: string;
  task_type: "small_composition" | "large_composition";
  content: string;
  reference_translation: string | null;
  ai_reference: string | null;
  writing_analysis: string | null;
  translation_analysis: string | null;
  image_url: string | null;
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

const truncateText = (text: string | null, maxLength: number = 80) => {
  if (!text) return "无";
  return text.length > maxLength ? text.substring(0, maxLength) + "..." : text;
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  }).format(date);
};

const WritingTasksPage = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("all");
  const [isLoading, setIsLoading] = useState(false);
  const [tasks, setTasks] = useState<WritingTask[]>([]);
  const [searchQuery, setSearchQuery] = useState("");

  // 加载写作任务数据
  const loadTasks = async () => {
    setIsLoading(true);
    try {
      const res = await fetch("/api/writing-tasks");
      const data = await res.json();
      if (data.success) {
        setTasks(data.data);
      }
    } catch (error) {
      console.error("加载写作任务失败:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // 初始加载数据
  useEffect(() => {
    loadTasks();
  }, []);

  // 按类型过滤数据
  const filteredTasks = tasks
    .filter((task) => {
      const matchesSearch =
        task.content?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.year?.includes(searchQuery) ||
        task.type?.includes(searchQuery);

      if (activeTab === "all") return matchesSearch;
      if (activeTab === "small")
        return task.task_type === "small_composition" && matchesSearch;
      if (activeTab === "large")
        return task.task_type === "large_composition" && matchesSearch;
      if (activeTab === "english1-small")
        return (
          task.task_type === "small_composition" &&
          task.type?.includes("英语一") &&
          matchesSearch
        );
      if (activeTab === "english1-large")
        return (
          task.task_type === "large_composition" &&
          task.type?.includes("英语一") &&
          matchesSearch
        );
      if (activeTab === "english2-small")
        return (
          task.task_type === "small_composition" &&
          task.type?.includes("英语二") &&
          matchesSearch
        );
      if (activeTab === "english2-large")
        return (
          task.task_type === "large_composition" &&
          task.type?.includes("英语二") &&
          matchesSearch
        );
      return matchesSearch;
    })
    .sort((a, b) => {
      // 按年份降序排序（从新到旧）
      return parseInt(b.year) - parseInt(a.year);
    });

  // 添加新写作任务
  const handleAddTask = () => {
    router.push("/admin/writing-tasks/new");
  };

  // 刷新数据
  const handleRefresh = () => {
    loadTasks();
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold tracking-tight">写作管理</h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isLoading}
            className="transition-all duration-200 hover:bg-gray-100"
          >
            <RefreshCw
              className={`mr-2 h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
            />
            {isLoading ? "加载中..." : "刷新"}
          </Button>
        </div>
      </div>

      <div className="mb-8 flex justify-between items-center">
        <div className="flex gap-2 w-1/3">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
            <Input
              type="search"
              placeholder="搜索题目内容、年份或类型..."
              className="pl-10 pr-4 py-2 border-gray-300 focus:ring-2 focus:ring-blue-500 rounded-md"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        <Button
          onClick={handleAddTask}
          className="bg-blue-600 hover:bg-blue-700 transition-colors"
          title="添加新的写作任务"
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          添加写作任务
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="flex flex-col gap-3">
          <TabsList className="w-full max-w-md grid grid-cols-3 rounded-lg bg-gray-100">
            <TabsTrigger
              value="all"
              className="rounded-md data-[state=active]:bg-white"
            >
              全部任务
            </TabsTrigger>
            <TabsTrigger
              value="small"
              className="rounded-md data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700"
            >
              全部小作文
            </TabsTrigger>
            <TabsTrigger
              value="large"
              className="rounded-md data-[state=active]:bg-purple-50 data-[state=active]:text-purple-700"
            >
              全部大作文
            </TabsTrigger>
          </TabsList>

          <div className="flex flex-col gap-1.5">
            <div className="px-1 text-sm font-medium text-gray-600">
              按考试类型细分：
            </div>
            <TabsList className="mb-6 w-full max-w-xl grid grid-cols-4 rounded-lg bg-gray-50">
              <TabsTrigger
                value="english1-small"
                className="rounded-md data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700"
              >
                英语一小作文
              </TabsTrigger>
              <TabsTrigger
                value="english1-large"
                className="rounded-md data-[state=active]:bg-purple-50 data-[state=active]:text-purple-700"
              >
                英语一大作文
              </TabsTrigger>
              <TabsTrigger
                value="english2-small"
                className="rounded-md data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700"
              >
                英语二小作文
              </TabsTrigger>
              <TabsTrigger
                value="english2-large"
                className="rounded-md data-[state=active]:bg-purple-50 data-[state=active]:text-purple-700"
              >
                英语二大作文
              </TabsTrigger>
            </TabsList>
          </div>
        </div>

        <Card className="border rounded-lg shadow-sm">
          <CardContent className="pt-6">
            <Table>
              <TableHeader>
                <TableRow className="hover:bg-gray-50/50 bg-gray-50/80">
                  <TableHead className="w-16 font-medium text-center">
                    ID
                  </TableHead>
                  <TableHead className="w-24 font-medium">年份</TableHead>
                  <TableHead className="w-28 font-medium">考试类型</TableHead>
                  <TableHead className="w-28 font-medium">作文类型</TableHead>
                  <TableHead className="font-medium">题目内容</TableHead>
                  <TableHead className="w-20 font-medium text-center">
                    图片
                  </TableHead>
                  <TableHead className="w-40 font-medium">创建时间</TableHead>
                  <TableHead className="w-20 text-right font-medium">
                    操作
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTasks.length > 0 ? (
                  filteredTasks.map((task) => (
                    <TableRow
                      key={task.id}
                      className="hover:bg-blue-50/50 group transition-colors cursor-pointer"
                      onClick={() =>
                        router.push(`/admin/writing-tasks/${task.id}/edit`)
                      }
                    >
                      <TableCell className="font-medium text-center">
                        {task.id}
                      </TableCell>
                      <TableCell>{task.year}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Badge
                            variant="outline"
                            className={`px-2 py-0.5 border-gray-200 ${
                              task.type?.includes("英语一")
                                ? "bg-green-50 text-green-700"
                                : task.type?.includes("英语二")
                                  ? "bg-orange-50 text-orange-700"
                                  : "bg-gray-50 text-gray-700"
                            }`}
                          >
                            {task.type}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            task.task_type === "small_composition"
                              ? "outline"
                              : "secondary"
                          }
                          className={`px-2.5 py-0.5 ${
                            task.task_type === "small_composition"
                              ? "border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100"
                              : "bg-purple-100 text-purple-700 hover:bg-purple-200"
                          }`}
                        >
                          {task.task_type === "small_composition"
                            ? "小作文"
                            : "大作文"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div
                          className="line-clamp-2 cursor-help"
                          title={task.content}
                        >
                          {truncateText(task.content)}
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        {task.image_url ? (
                          <div className="flex items-center justify-center">
                            <ImageIcon className="h-4 w-4 text-blue-500" />
                          </div>
                        ) : (
                          <span className="text-xs text-gray-400">无</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Calendar className="h-3.5 w-3.5 text-gray-500 mr-1.5" />
                          <span className="text-sm text-gray-600">
                            {formatDate(task.created_at)}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(`/admin/writing-tasks/${task.id}/edit`);
                          }}
                          className="opacity-70 group-hover:opacity-100 transition-opacity"
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          编辑
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-10">
                      {isLoading ? (
                        <div className="flex items-center justify-center gap-2">
                          <RefreshCw className="h-5 w-5 animate-spin text-gray-400" />
                          <span className="text-gray-500">加载中...</span>
                        </div>
                      ) : searchQuery ? (
                        <div className="text-gray-500">
                          <Search className="h-5 w-5 mx-auto mb-2 text-gray-400" />
                          <p>没有找到匹配的记录</p>
                        </div>
                      ) : (
                        <div className="text-gray-500">
                          <PlusCircle className="h-5 w-5 mx-auto mb-2 text-gray-400" />
                          <p>暂无数据，点击"添加写作任务"创建一个新任务</p>
                        </div>
                      )}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </Tabs>
    </div>
  );
};

export default WritingTasksPage;
