"use server";

import OpenAI from "openai";
import { ChatCompletionMessageParam } from "openai/resources/chat/completions";

// 确保设置了环境变量
// DEEPSEEK_API_KEY

// 创建DeepSeek客户端
export async function createDeepSeekClient() {
  // 检查API密钥是否存在
  const apiKey = process.env.DEEPSEEK_API_KEY;
  if (!apiKey) {
    throw new Error("未设置DEEPSEEK_API_KEY环境变量");
  }

  // 仅打印密钥的前几个字符和长度，用于调试
  console.log(
    `API密钥前缀: ${apiKey.substring(0, 5)}..., 长度: ${apiKey.length}`
  );

  // 创建兼容OpenAI的客户端
  return new OpenAI({
    baseURL: "https://api.deepseek.com",
    apiKey: apiKey,
  });
}

// 非流式调用
export async function chatWithDeepSeek(
  messages: ChatCompletionMessageParam[],
  systemPrompt?: string
) {
  try {
    const client = await createDeepSeekClient();
    const allMessages: ChatCompletionMessageParam[] = systemPrompt
      ? [{ role: "system", content: systemPrompt }, ...messages]
      : messages;

    const response = await client.chat.completions.create({
      messages: allMessages,
      model: "deepseek-chat",
    });

    return response.choices[0].message.content;
  } catch (error) {
    console.error("DeepSeek API 调用失败:", error);
    throw error;
  }
}

// 流式调用
export async function streamChatWithDeepSeek(
  messages: ChatCompletionMessageParam[],
  systemPrompt?: string
) {
  try {
    const client = await createDeepSeekClient();
    const allMessages: ChatCompletionMessageParam[] = systemPrompt
      ? [{ role: "system", content: systemPrompt }, ...messages]
      : messages;

    const stream = await client.chat.completions.create({
      messages: allMessages,
      model: "deepseek-chat",
      stream: true,
    });

    return stream;
  } catch (error) {
    console.error("DeepSeek API 流式调用失败:", error);
    throw error;
  }
}

// 英语学习助手系统提示
export async function getEnglishLearningSystemPrompt() {
  return `
你是一个专业的英语学习助手，主要帮助中国学生准备考研英语考试。
你需要：
1. 解答英语词汇、语法、阅读理解、翻译和写作方面的问题
2. 提供考研英语备考建议和学习策略
3. 分析英语句子结构和翻译技巧
4. 纠正学生的英语错误并给出改进建议
5. 提供英语学习资源和方法推荐

请使用简洁、准确的语言，并根据学生的英语水平调整回答的复杂度。
当涉及到英语知识点解释时，尽量提供易于理解的中文解释并结合实例。
`;
}
