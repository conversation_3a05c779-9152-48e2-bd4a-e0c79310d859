"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";

const NewSentencePage = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    year: new Date().getFullYear().toString(),
    type: "英语一",
    sectionType: "translation",
    originalContent: "",
    paragraphNum: 1,
    indexNum: 1,
    explain_md: "",
  });

  // 处理表单字段变化
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      // 首先创建或获取对应的papers记录
      const paperResponse = await fetch("/api/papers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          year: formData.year,
          type: formData.type,
          section_type: formData.sectionType,
        }),
      });

      const paperData = await paperResponse.json();

      if (!paperData.success) {
        throw new Error(paperData.error || "创建文章记录失败");
      }

      // 然后创建句子记录
      const sentenceResponse = await fetch("/api/sentences", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify([
          {
            article_id: paperData.data.id,
            content: formData.originalContent,
            paragraph_num: formData.paragraphNum,
            sequence: formData.indexNum,
            explain_md: formData.explain_md,
          },
        ]),
      });

      const data = await sentenceResponse.json();

      if (data.success) {
        // 跳转到句子列表页面
        router.push("/admin/sentences");
      } else {
        throw new Error(data.error || "创建失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "创建失败");
      console.error("创建句子错误:", err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full space-y-8">
      {/* 顶部标题和操作栏 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
            添加句子
          </h1>
          <p className="text-gray-500 mt-2 text-lg">创建新的句子分析记录</p>
        </div>
        <Link
          href="/admin/sentences"
          className="px-6 py-3 bg-gray-100 text-gray-700 text-lg rounded-xl hover:bg-gray-200 transition-colors"
        >
          返回列表
        </Link>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-500"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* 表单 */}
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* 基本信息 */}
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <h2 className="text-xl font-bold text-gray-800 mb-6">基本信息</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <label className="text-gray-700 font-medium">年份</label>
              <select
                name="year"
                value={formData.year}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                {Array.from({ length: 16 }, (_, i) => 2010 + i).map((year) => (
                  <option key={year} value={year.toString()}>
                    {year}年
                  </option>
                ))}
              </select>
            </div>

            <div className="space-y-2">
              <label className="text-gray-700 font-medium">类型</label>
              <select
                name="type"
                value={formData.type}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="英语一">英语一</option>
                <option value="英语二">英语二</option>
              </select>
            </div>

            <div className="space-y-2">
              <label className="text-gray-700 font-medium">题型</label>
              <select
                name="sectionType"
                value={formData.sectionType}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="use_of_english">完形填空</option>
                <option value="reading_comprehension">阅读理解</option>
                <option value="translation">翻译</option>
                <option value="writing">写作</option>
              </select>
            </div>
          </div>

          <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <label className="text-gray-700 font-medium">段落编号</label>
              <input
                type="number"
                name="paragraphNum"
                value={formData.paragraphNum}
                onChange={handleChange}
                min="1"
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            <div className="space-y-2">
              <label className="text-gray-700 font-medium">句子编号</label>
              <input
                type="number"
                name="indexNum"
                value={formData.indexNum}
                onChange={handleChange}
                min="1"
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
          </div>

          <div className="mt-6">
            <div className="space-y-2">
              <label className="text-gray-700 font-medium">原文句子</label>
              <textarea
                name="originalContent"
                value={formData.originalContent}
                onChange={handleChange}
                placeholder="输入原文句子..."
                rows={4}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
          </div>

          <div className="mt-6">
            <div className="space-y-2">
              <label className="text-gray-700 font-medium">
                Markdown解析内容
              </label>
              <textarea
                name="explain_md"
                value={formData.explain_md}
                onChange={handleChange}
                placeholder="使用Markdown格式编写解析内容..."
                rows={10}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono"
              />
              <p className="text-sm text-gray-500">
                使用Markdown格式编写解析内容，支持标题、列表、加粗、链接等格式
              </p>
            </div>
          </div>
        </div>

        {/* 表单提交按钮 */}
        <div className="flex justify-end space-x-4">
          <Link
            href="/admin/sentences"
            className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors"
          >
            取消
          </Link>
          <button
            type="submit"
            disabled={loading}
            className={`px-6 py-3 bg-blue-600 text-white rounded-xl transition-colors ${
              loading ? "opacity-70 cursor-not-allowed" : "hover:bg-blue-700"
            }`}
          >
            {loading ? "提交中..." : "保存句子"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default NewSentencePage;
