"use client";

import { createClient } from "@/utils/supabase/client";
import { User } from "@supabase/supabase-js";
import Link from "next/link";
import { useEffect, useState } from "react";
import PurchaseHistory from "@/components/dashboard/PurchaseHistory";
import DashboardSkeleton from "@/components/dashboard/DashboardSkeleton";
import { useSearchParams, useRouter } from "next/navigation";

interface DashboardClientProps {
  user?: User | null;
  subscriptionInfo?: {
    isActive: boolean;
    type: string;
    endDate: string;
  } | null;
}

export default function DashboardClient({
  user,
  subscriptionInfo,
}: DashboardClientProps) {
  const [loading, setLoading] = useState(true);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [currentSubscriptionInfo, setCurrentSubscriptionInfo] =
    useState(subscriptionInfo);
  const supabase = createClient();
  const searchParams = useSearchParams();
  const router = useRouter();
  const paymentStatus = searchParams.get("payment_status");

  // 检查支付状态参数并刷新订阅信息
  useEffect(() => {
    const refreshSubscriptionInfo = async () => {
      if (!currentUser) return;

      try {
        // 获取用户订阅信息
        const { data: lifetimeMembership } = await supabase
          .from("zpay_transactions")
          .select("*")
          .eq("user_id", currentUser.id)
          .eq("status", "success")
          .eq("product_id", "lifetime-membership")
          .order("created_at", { ascending: false })
          .limit(1);

        // 如果有终身会员记录
        if (lifetimeMembership && lifetimeMembership.length > 0) {
          setCurrentSubscriptionInfo({
            isActive: true,
            type: "终身会员",
            endDate: "永久有效",
          });
          return;
        }

        // 如果没有终身会员，查询常规订阅
        const now = new Date();
        const { data: subscriptions } = await supabase
          .from("zpay_transactions")
          .select("*")
          .eq("user_id", currentUser.id)
          .eq("status", "success")
          .eq("is_subscription", true)
          .lt("subscription_start", now.toISOString())
          .gt("subscription_end", now.toISOString())
          .order("subscription_end", { ascending: false })
          .limit(1);

        if (subscriptions && subscriptions.length > 0) {
          const subscription = subscriptions[0];
          setCurrentSubscriptionInfo({
            isActive: true,
            type:
              subscription.metadata?.subscription_period === "yearly"
                ? "年付专业版"
                : "月付专业版",
            endDate: subscription.subscription_end,
          });
        } else {
          setCurrentSubscriptionInfo({
            isActive: false,
            type: "",
            endDate: "",
          });
        }
      } catch (error) {
        console.error("获取订阅信息失败:", error);
      }
    };

    // 如果存在支付状态参数，刷新订阅信息
    if (paymentStatus && currentUser) {
      refreshSubscriptionInfo();

      // 刷新后清除URL中的参数，防止刷新页面重复执行
      const newUrl = window.location.pathname;
      window.history.replaceState({}, "", newUrl);
    }
  }, [paymentStatus, currentUser, supabase]);

  // 如果props中的user为undefined，尝试从客户端获取用户
  useEffect(() => {
    const getUserFromClient = async () => {
      console.log("user", user);
      if (user) {
        setCurrentUser(user);
        setLoading(false);
        return;
      }

      try {
        const {
          data: { user: authUser },
        } = await supabase.auth.getUser();
        setCurrentUser(authUser);
      } catch (error) {
        console.error("获取用户信息失败:", error);
      } finally {
        setLoading(false);
      }
    };

    getUserFromClient();
  }, [user, supabase]);

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    window.location.href = "/";
  };

  // 显示加载状态
  if (loading) {
    return <DashboardSkeleton />;
  }

  // 用户未登录
  if (!currentUser) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="flex flex-col justify-center items-center min-h-[40vh]">
          <p className="text-gray-500 mb-4">您尚未登录或会话已过期</p>
          <Link
            href="/sign-in?redirect=/dashboard"
            className="btn-sm text-white bg-blue-600 hover:bg-blue-700 shadow-sm"
          >
            登录
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* 用户信息 */}
      <div className="mb-8 bg-white p-6 rounded-lg shadow-sm">
        <div className="flex flex-col md:flex-row justify-between items-start">
          <div>
            <h2 className="h3 font-cabinet-grotesk mb-2">个人信息</h2>
            <p className="text-gray-600 mb-2">
              <span className="font-medium">邮箱:</span> {currentUser.email}
            </p>
            {currentSubscriptionInfo && (
              <div className="mt-2 text-gray-600">
                <p className="mb-1">
                  <span className="font-medium">订阅状态:</span>{" "}
                  {currentSubscriptionInfo.isActive ? (
                    <span className="text-green-600">订阅中</span>
                  ) : (
                    <span className="text-gray-500">未订阅</span>
                  )}
                </p>
                {currentSubscriptionInfo.isActive && (
                  <>
                    <p className="mb-1">
                      <span className="font-medium">订阅类型:</span>{" "}
                      {currentSubscriptionInfo.type}
                    </p>
                    <p className="mb-1">
                      <span className="font-medium">到期时间:</span>{" "}
                      {currentSubscriptionInfo.type === "终身会员"
                        ? "永久有效"
                        : new Date(
                            currentSubscriptionInfo.endDate
                          ).toLocaleDateString()}
                    </p>
                  </>
                )}
              </div>
            )}
          </div>
          <div className="mt-4 md:mt-0">
            <button
              onClick={handleSignOut}
              style={{
                padding: "0.5rem 1rem",
                fontSize: "0.875rem",
                fontWeight: 500,
                color: "white",
                backgroundColor: "#ef4444",
                borderRadius: "0.375rem",
                boxShadow: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
                cursor: "pointer",
                border: "none",
                transition: "background-color 150ms ease-in-out",
              }}
              onMouseOver={(e) =>
                (e.currentTarget.style.backgroundColor = "#dc2626")
              }
              onMouseOut={(e) =>
                (e.currentTarget.style.backgroundColor = "#ef4444")
              }
            >
              退出登录
            </button>
          </div>
        </div>
      </div>

      {/* 购买历史 */}
      <div className="mb-8">
        <PurchaseHistory />
      </div>
    </div>
  );
}
