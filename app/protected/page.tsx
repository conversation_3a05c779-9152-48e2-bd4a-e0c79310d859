import FetchDataSteps from "@/components/tutorial/fetch-data-steps";
import { createClient } from "@/utils/supabase/server";
import { RiInformationLine, RiUserLine } from "react-icons/ri";
import { redirect } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default async function ProtectedPage() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return redirect("/sign-in");
  }

  return (
    <div className="flex-1 w-full flex flex-col gap-8 max-w-3xl mx-auto px-4 py-8">
      <div className="w-full">
        <div className="bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 p-4 px-5 rounded-md flex gap-3 items-center border border-blue-200 dark:border-blue-800 shadow-sm">
          <RiInformationLine size={20} />
          <span>这是一个受保护的页面，只有已认证的用户才能访问</span>
        </div>
      </div>

      <Card className="shadow-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RiUserLine className="text-blue-500" />
            <span>您的用户信息</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="text-xs font-mono p-4 rounded-md border bg-gray-50 dark:bg-gray-800 max-h-64 overflow-auto">
            {JSON.stringify(user, null, 2)}
          </pre>
        </CardContent>
      </Card>

      {/* <div>
        <h2 className="font-bold text-2xl mb-4">Next steps</h2>
        <FetchDataSteps />
      </div> */}
    </div>
  );
}
