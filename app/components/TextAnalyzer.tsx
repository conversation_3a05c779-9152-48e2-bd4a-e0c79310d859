"use client";

import { useState } from "react";
import { Dialog } from "@headlessui/react";

interface TextAnalyzerProps {
  isOpen: boolean;
  onClose: () => void;
  onAnalyze: (data: {
    content: string;
    year: string;
    type: string;
    sectionType: string;
    readingTestNumber?: string;
  }) => Promise<void>;
}

const TextAnalyzer: React.FC<TextAnalyzerProps> = ({
  isOpen,
  onClose,
  onAnalyze,
}) => {
  const [year, setYear] = useState<string>("");
  const [type, setType] = useState<string>("");
  const [sectionType, setSectionType] = useState<string>("");
  const [readingTestNumber, setReadingTestNumber] = useState<string>("");
  const [content, setContent] = useState<string>("");
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!content || !year || !type || !sectionType) return;
    if (sectionType === "reading_comprehension" && !readingTestNumber) return;

    try {
      setLoading(true);
      const finalSectionType =
        sectionType === "reading_comprehension"
          ? `reading_comprehension_${readingTestNumber}`
          : sectionType;

      await onAnalyze({
        content,
        year,
        type,
        sectionType: finalSectionType,
      });
      onClose();
    } catch (error) {
      console.error("Analysis failed:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSectionTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSectionType(e.target.value);
    if (e.target.value !== "reading_comprehension") {
      setReadingTestNumber("");
    }
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="mx-auto max-w-3xl w-full rounded-2xl bg-white p-8 shadow-xl">
          <Dialog.Title className="text-2xl font-bold text-gray-900 mb-6 bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
            分析试题内容
          </Dialog.Title>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div
              className={`grid ${sectionType === "reading_comprehension" ? "grid-cols-4" : "grid-cols-3"} gap-6`}
            >
              <div className="space-y-2">
                <label className="block text-gray-700 text-lg font-medium">
                  年份
                </label>
                <select
                  value={year}
                  onChange={(e) => setYear(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  required
                >
                  <option value="">选择年份</option>
                  {Array.from({ length: 16 }, (_, i) => 2010 + i).map((y) => (
                    <option key={y} value={y}>
                      {y}年
                    </option>
                  ))}
                </select>
              </div>

              <div className="space-y-2">
                <label className="block text-gray-700 text-lg font-medium">
                  类型
                </label>
                <select
                  value={type}
                  onChange={(e) => setType(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  required
                >
                  <option value="">选择类型</option>
                  <option value="英语一">英语一</option>
                  <option value="英语二">英语二</option>
                </select>
              </div>

              <div className="space-y-2">
                <label className="block text-gray-700 text-lg font-medium">
                  题目类型
                </label>
                <select
                  value={sectionType}
                  onChange={handleSectionTypeChange}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  required
                >
                  <option value="">选择题型</option>
                  <option value="use_of_english">完形填空</option>
                  <option value="reading_comprehension">阅读理解</option>
                  <option value="translation">翻译</option>
                  <option value="writing">写作</option>
                </select>
              </div>

              {sectionType === "reading_comprehension" && (
                <div className="space-y-2">
                  <label className="block text-gray-700 text-lg font-medium">
                    阅读编号
                  </label>
                  <select
                    value={readingTestNumber}
                    onChange={(e) => setReadingTestNumber(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    required
                  >
                    <option value="">选择编号</option>
                    <option value="test1">Test 1</option>
                    <option value="test2">Test 2</option>
                    <option value="test3">Test 3</option>
                    <option value="test4">Test 4</option>
                  </select>
                </div>
              )}
            </div>

            <div className="space-y-2">
              <label className="block text-gray-700 text-lg font-medium">
                试题内容
              </label>
              <textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                className="w-full h-64 px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                placeholder="请粘贴试题内容..."
                required
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-3 text-gray-700 hover:text-gray-900 text-lg font-medium rounded-xl hover:bg-gray-100 transition-all duration-200"
                disabled={loading}
              >
                取消
              </button>
              <button
                type="submit"
                className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white text-lg font-medium rounded-xl hover:shadow-lg transform hover:scale-105 transition-all duration-200 disabled:opacity-50"
                disabled={loading}
              >
                {loading ? "分析中..." : "开始分析"}
              </button>
            </div>
          </form>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default TextAnalyzer;
