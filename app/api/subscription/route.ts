import { NextRequest, NextResponse } from "next/server";
import { createServerSupabaseClient } from "@/utils/supabase/server";

// 订阅信息接口
export interface SubscriptionInfo {
  isActive: boolean;
  type: string;
  endDate: string;
}

// 导出配置对象，标记为动态路由
export const dynamic = "force-dynamic";

/**
 * 获取当前登录用户的订阅信息
 */
export async function GET(request: NextRequest) {
  let subscriptionInfo: SubscriptionInfo = {
    isActive: false,
    type: "",
    endDate: "",
  };

  try {
    const supabase = await createServerSupabaseClient();

    // 获取用户信息
    const {
      data: { user },
    } = await supabase.auth.getUser();

    // 如果用户未登录，返回未订阅状态
    if (!user) {
      return NextResponse.json(
        {
          subscriptionInfo,
          message: "用户未登录",
        },
        { status: 401 }
      );
    }

    // 查询用户是否在用户表中被标记为订阅用户
    const { data: userData, error: userError } = await supabase
      .from("users_custom")
      .select("is_subscriptionnew")
      .eq("user_id", user.id)
      .single();

    console.log("userData", userData);
    if (userError) {
      console.error("获取用户订阅状态失败:", userError);
    } else if (userData && userData.is_subscriptionnew === true) {
      subscriptionInfo = {
        isActive: true,
        type: "终身会员",
        endDate: "永久有效",
      };

      return NextResponse.json({ subscriptionInfo });
    }

    // 如果用户表中没有标记为订阅用户，继续检查是否有终身会员购买记录
    const { data: lifetimeMembership, error: lifetimeError } = await supabase
      .from("zpay_transactions")
      .select("*")
      .eq("user_id", user.id)
      .eq("status", "success")
      .eq("product_id", "lifetime-membership")
      .order("created_at", { ascending: false })
      .limit(1);

    if (lifetimeError) {
      console.error("获取终身会员信息失败:", lifetimeError);
      return NextResponse.json(
        {
          error: "获取终身会员信息失败",
          details: lifetimeError.message,
        },
        { status: 500 }
      );
    }

    // 如果有终身会员记录
    if (lifetimeMembership && lifetimeMembership.length > 0) {
      subscriptionInfo = {
        isActive: true,
        type: "终身会员",
        endDate: "永久有效",
      };

      return NextResponse.json({ subscriptionInfo });
    }

    // 如果没有终身会员，查询常规订阅
    const now = new Date();
    const { data: subscriptions, error } = await supabase
      .from("zpay_transactions")
      .select("*")
      .eq("user_id", user.id)
      .eq("status", "success")
      .eq("is_subscription", true)
      .lt("subscription_start", now.toISOString())
      .gt("subscription_end", now.toISOString())
      .order("subscription_end", { ascending: false })
      .limit(1);

    if (error) {
      console.error("获取订阅信息失败:", error);
      return NextResponse.json(
        {
          error: "获取订阅信息失败",
          details: error.message,
        },
        { status: 500 }
      );
    }

    if (subscriptions && subscriptions.length > 0) {
      const subscription = subscriptions[0];
      subscriptionInfo = {
        isActive: true,
        type:
          subscription.metadata?.subscription_period === "yearly"
            ? "年付专业版"
            : "月付专业版",
        endDate: subscription.subscription_end,
      };
    }

    return NextResponse.json({ subscriptionInfo });
  } catch (error: any) {
    console.error("获取订阅信息时出错:", error);
    return NextResponse.json(
      {
        error: "获取订阅信息时出错",
        details: error.message,
      },
      { status: 500 }
    );
  }
}
