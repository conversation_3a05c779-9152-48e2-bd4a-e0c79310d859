"use client";

import Link from "next/link";
import { signOutAction } from "@/app/actions";
import { User } from "@supabase/supabase-js";
import { But<PERSON> } from "@/components/ui/button";
import { AiOutlineUser } from "react-icons/ai";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface UserNavigationProps {
  user: User | null;
}

export const UserNavigation = ({ user }: UserNavigationProps) => {
  // 用户已登录状态
  if (user) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="flex items-center gap-2 text-sm px-3 py-2 rounded-md"
          >
            <AiOutlineUser className="h-5 w-5" />
            <span className="hidden md:inline-block max-w-[150px] truncate">
              {user.email}
            </span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuItem asChild>
            <Link href="/dashboard" className="w-full cursor-pointer">
              个人中心
            </Link>
          </DropdownMenuItem>
          <form action={signOutAction} className="w-full">
            <button
              type="submit"
              className="w-full text-left px-2 py-1.5 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 rounded"
            >
              退出登录
            </button>
          </form>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  // 用户未登录状态
  return (
    <div className="hidden md:flex items-center space-x-2">
      <Button asChild variant="ghost" size="sm">
        <Link href="/sign-in">登录</Link>
      </Button>
      <Button asChild variant="default" size="sm">
        <Link href="/sign-up">注册</Link>
      </Button>
    </div>
  );
};
