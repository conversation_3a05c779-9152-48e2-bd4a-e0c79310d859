import { createServerClient } from "@supabase/ssr";
import { type NextRequest, NextResponse } from "next/server";

// 性能计时辅助函数
const timeStart = (label: string) => {
  console.time(`⏱️ MW: ${label}`);
  return Date.now();
};

const timeEnd = (label: string, startTime: number) => {
  const duration = Date.now() - startTime;
  console.timeEnd(`⏱️ MW: ${label}`);
  console.log(`🕒 MW: ${label} 耗时: ${duration}ms`);
  return duration;
};

// 用户信息缓存
type UserCacheEntry = {
  user: any;
  expiry: number;
};

const userCache: Record<string, UserCacheEntry> = {};

// 缓存TTL配置(单位：秒)
const USER_CACHE_TTL = 30;

// 公开API路径列表，不需要验证的路径
const PUBLIC_API_PATHS = ["/api/papers", "/api/papers/"];

// 检查路径是否匹配公开API
const isPublicPath = (path: string): boolean => {
  // 精确匹配
  if (PUBLIC_API_PATHS.includes(path)) {
    return true;
  }

  // 检查路径是否匹配 /api/papers/:id 模式
  if (/^\/api\/papers\/[^\/]+$/.test(path)) {
    return true;
  }

  return false;
};

export const updateSession = async (request: NextRequest) => {
  const totalStartTime = timeStart("中间件总耗时");
  const url = request.nextUrl.pathname;

  // 记录请求路径
  console.log(`🔍 MW: 处理请求 ${url}`);

  // 检查是否为公开API
  const isPublic = isPublicPath(url);
  if (isPublic) {
    console.log(`🔓 MW: 公开API路径 ${url}，跳过用户验证`);
  }

  try {
    // Create an unmodified response
    const createResponseStartTime = timeStart("创建初始响应");
    let response = NextResponse.next({
      request: {
        headers: request.headers,
      },
    });
    timeEnd("创建初始响应", createResponseStartTime);

    const createClientStartTime = timeStart("创建Supabase客户端");
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value }) =>
              request.cookies.set(name, value)
            );
            response = NextResponse.next({
              request,
            });
            cookiesToSet.forEach(({ name, value, options }) =>
              response.cookies.set(name, value, options)
            );
          },
        },
      }
    );
    timeEnd("创建Supabase客户端", createClientStartTime);

    // 对于公开API，跳过用户验证
    if (isPublic) {
      timeEnd("中间件总耗时", totalStartTime);
      return response;
    }

    // 获取授权令牌
    const authCookie = request.cookies.get("sb-auth-token");
    const authToken = authCookie?.value;

    let user: any = { error: null, data: { user: null } };

    if (authToken) {
      // 检查缓存
      const cacheKey = authToken.slice(0, 50); // 取token前50字符作为缓存键
      const cachedUser = userCache[cacheKey];

      const getUserStartTime = timeStart("获取用户信息");
      if (cachedUser && cachedUser.expiry > Date.now()) {
        console.log(`🔄 MW: 使用缓存的用户信息`);
        user = cachedUser.user;
      } else {
        // 缓存未命中，调用API获取用户信息
        user = await supabase.auth.getUser();

        // 缓存用户信息
        if (!user.error) {
          userCache[cacheKey] = {
            user,
            expiry: Date.now() + USER_CACHE_TTL * 1000,
          };
        }
      }
      timeEnd("获取用户信息", getUserStartTime);
    }

    // protected routes
    if (
      request.nextUrl.pathname.startsWith("/protected") &&
      (!user.data?.user || user.error)
    ) {
      timeEnd("中间件总耗时", totalStartTime);
      console.log(`🚫 MW: 未授权访问保护路由，重定向到登录页`);
      return NextResponse.redirect(new URL("/sign-in", request.url));
    }

    timeEnd("中间件总耗时", totalStartTime);
    return response;
  } catch (e) {
    // If you are here, a Supabase client could not be created!
    // This is likely because you have not set up environment variables.
    // Check out http://localhost:3000 for Next Steps.
    timeEnd("中间件总耗时", totalStartTime);
    console.error(`❌ MW: 创建Supabase客户端失败`, e);
    return NextResponse.next({
      request: {
        headers: request.headers,
      },
    });
  }
};
