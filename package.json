{"name": "mbdata", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.0", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@types/node": "20.11.17", "@types/react": "18.2.55", "@types/react-dom": "18.2.19", "autoprefixer": "10.4.17", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "eslint": "8.56.0", "eslint-config-next": "14.1.0", "framer-motion": "^12.6.3", "html-to-image": "^1.11.13", "lucide-react": "^0.468.0", "mysql2": "^3.14.0", "next": "14.1.0", "next-themes": "^0.4.6", "openai": "^4.28.0", "postcss": "8.4.35", "prettier": "^3.3.3", "react": "18.2.0", "react-dom": "18.2.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "tailwind-merge": "^2.6.0", "tailwindcss": "3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "5.3.3"}, "devDependencies": {"@types/node": "22.10.2", "@types/react": "^19.0.2", "@types/react-dom": "19.0.2", "@types/react-syntax-highlighter": "^15.5.11", "postcss": "8.4.49", "tailwind-merge": "^2.5.2", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "5.7.2"}}