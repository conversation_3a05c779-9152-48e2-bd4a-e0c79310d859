export default function Loading() {
  return (
    <div className="flex flex-col space-y-5 w-full">
      {/* 顶部加载状态 */}
      <div className="flex justify-between items-center animate-pulse mb-4">
        <div className="h-6 w-32 bg-gray-200 rounded"></div>
        <div className="h-6 w-24 bg-gray-200 rounded-full"></div>
      </div>

      {/* 模拟内容加载状态 */}
      <div className="space-y-6">
        {/* 第一组单词 */}
        <div>
          <div className="flex items-center mb-2 animate-pulse">
            <div className="h-7 w-7 bg-gray-200 rounded-full mr-2"></div>
            <div className="h-5 w-32 bg-gray-200 rounded"></div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {Array(6)
              .fill(0)
              .map((_, i) => (
                <VocabularyCardSkeleton key={i} />
              ))}
          </div>
        </div>

        {/* 第二组单词 */}
        <div>
          <div className="flex items-center mb-2 animate-pulse">
            <div className="h-7 w-7 bg-gray-200 rounded-full mr-2"></div>
            <div className="h-5 w-32 bg-gray-200 rounded"></div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {Array(3)
              .fill(0)
              .map((_, i) => (
                <VocabularyCardSkeleton key={i} />
              ))}
          </div>
        </div>
      </div>
    </div>
  );
}

function VocabularyCardSkeleton() {
  return (
    <div className="border rounded-lg shadow-sm bg-white overflow-hidden animate-pulse">
      <div className="p-3">
        <div className="flex justify-between items-start">
          <div className="w-full">
            <div className="flex items-center">
              <div className="h-5 w-20 bg-gray-300 rounded"></div>
              <div className="ml-2 h-4 w-4 bg-gray-200 rounded-full"></div>
            </div>
            <div className="h-4 w-3/4 bg-gray-200 rounded mt-1"></div>
          </div>
          <div className="h-6 w-6 bg-gray-200 rounded-full"></div>
        </div>
        <div className="mt-2 pt-2 border-t border-gray-100">
          <div className="h-3 w-full bg-gray-100 rounded"></div>
          <div className="h-3 w-5/6 bg-gray-100 rounded mt-1"></div>
        </div>
      </div>
    </div>
  );
}
