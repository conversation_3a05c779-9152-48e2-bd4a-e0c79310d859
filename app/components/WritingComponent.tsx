"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "../components/ui/textarea";
import { Separator } from "../components/ui/separator";
import Image from "next/image";
import {
  ChevronDown,
  ChevronUp,
  BookOpen,
  Lightbulb,
  PenLine,
  BarChart,
  CheckCircle,
} from "lucide-react";
import { Progress } from "../components/ui/progress";
import { Badge } from "@/components/ui/badge";
import ReactMarkdown from "react-markdown";

interface WritingTask {
  id: number;
  paper_id: number;
  task_type: "small_composition" | "large_composition"; // 小作文/大作文
  writing_type: string; // 具体写作类型（应用文/图表作文/图画作文/议论文）
  prompt: string; // 作文题目描述
  word_requirement?: string; // 字数要求
  ai_reference?: string; // AI生成的参考范文
  scoring_criteria?: string; // 评分标准
  tips?: string; // 写作技巧和建议
  image_url?: string; // 作文图片URL，用于图表/图画作文
}

interface WritingResponse {
  content: string; // 用户的作文内容
  ai_score?: number; // AI评分（百分制）
  ai_feedback?: string; // AI评价和建议
}

interface WritingComponentProps {
  examType: "英语一" | "英语二"; // 考试类型
  tasks: WritingTask[]; // 写作任务（小作文和大作文）
  onSubmit?: (taskId: number, content: string) => Promise<WritingResponse>; // 提交作文回调
}

const WritingComponent: React.FC<WritingComponentProps> = ({
  examType,
  tasks,
  onSubmit,
}) => {
  const [activeTaskId, setActiveTaskId] = useState<number | null>(null);
  const [userContent, setUserContent] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [response, setResponse] = useState<WritingResponse | null>(null);
  const [expandedSections, setExpandedSections] = useState<{
    [key: string]: boolean;
  }>({
    prompt: true,
    reference: false,
    criteria: false,
    tips: false,
  });

  // 小作文和大作文任务
  const smallTask = tasks.find(
    (task) => task.task_type === "small_composition"
  );
  const largeTask = tasks.find(
    (task) => task.task_type === "large_composition"
  );

  // 切换展开/折叠状态
  const toggleSection = (section: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  // 处理任务切换
  const handleTaskChange = (taskId: number) => {
    setActiveTaskId(taskId);
    setUserContent("");
    setResponse(null);
  };

  // 提交作文
  const handleSubmit = async () => {
    if (!activeTaskId || !userContent.trim() || !onSubmit) {
      return;
    }

    try {
      setIsSubmitting(true);
      const result = await onSubmit(activeTaskId, userContent);
      setResponse(result);
    } catch (error) {
      console.error("提交作文失败:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 获取得分等级文字和颜色
  const getScoreLevel = (score?: number) => {
    if (!score) return { text: "未评分", color: "bg-gray-300" };

    if (score >= 90) return { text: "优秀", color: "bg-green-500" };
    if (score >= 80) return { text: "良好", color: "bg-green-400" };
    if (score >= 70) return { text: "中等", color: "bg-yellow-400" };
    if (score >= 60) return { text: "及格", color: "bg-orange-400" };
    return { text: "不及格", color: "bg-red-500" };
  };

  // 渲染写作任务详情
  const renderTaskDetails = (task: WritingTask) => {
    if (!task) return null;

    return (
      <div className="space-y-6">
        {/* 题目提示 */}
        <Card>
          <CardHeader className="pb-2">
            <div
              className="flex justify-between items-center cursor-pointer"
              onClick={() => toggleSection("prompt")}
            >
              <CardTitle className="text-md font-medium flex items-center">
                <PenLine className="h-5 w-5 mr-2" />
                写作题目
              </CardTitle>
              <Button variant="ghost" size="sm">
                {expandedSections["prompt"] ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </div>
          </CardHeader>
          {expandedSections["prompt"] && (
            <CardContent>
              <div className="space-y-4">
                <div className="bg-gray-50 p-4 rounded-md">
                  <pre className="whitespace-pre-wrap text-gray-700">
                    {task.prompt}
                  </pre>
                </div>

                {task.word_requirement && (
                  <div className="flex items-center text-sm text-gray-500">
                    <span>字数要求: {task.word_requirement}</span>
                  </div>
                )}

                {task.image_url && (
                  <div className="mt-4">
                    <p className="text-sm font-medium mb-2">图表/图片:</p>
                    <div className="relative h-60 w-full">
                      <Image
                        src={task.image_url}
                        alt="Writing prompt image"
                        fill
                        style={{ objectFit: "contain" }}
                        className="rounded-md"
                      />
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          )}
        </Card>

        {/* 写作区域 */}
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">你的作文</h3>
          <Textarea
            value={userContent}
            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
              setUserContent(e.target.value)
            }
            placeholder="在此处开始写作..."
            className="min-h-[300px] p-4"
          />
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              当前字数: {userContent.trim().split(/\s+/).length} 词
            </div>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || !userContent.trim()}
            >
              {isSubmitting ? "提交中..." : "提交作文"}
            </Button>
          </div>
        </div>

        {/* AI 评分和反馈 */}
        {response && (
          <Card className="border-t-4 border-blue-500">
            <CardHeader>
              <CardTitle>AI 评分与反馈</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {response.ai_score !== undefined && (
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">总体评分</span>
                    <Badge
                      className={`${getScoreLevel(response.ai_score).color} text-white`}
                    >
                      {response.ai_score} 分 -{" "}
                      {getScoreLevel(response.ai_score).text}
                    </Badge>
                  </div>
                  <Progress
                    value={response.ai_score}
                    max={100}
                    className="h-2"
                  />
                </div>
              )}

              {response.ai_feedback && (
                <div className="mt-4 bg-gray-50 p-4 rounded-md">
                  <h4 className="text-sm font-medium mb-2">详细反馈:</h4>
                  <div className="text-gray-700 prose prose-sm max-w-none">
                    <ReactMarkdown>{response.ai_feedback}</ReactMarkdown>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* 参考范文 */}
        <Card>
          <CardHeader className="pb-2">
            <div
              className="flex justify-between items-center cursor-pointer"
              onClick={() => toggleSection("reference")}
            >
              <CardTitle className="text-md font-medium flex items-center">
                <BookOpen className="h-5 w-5 mr-2" />
                参考范文
              </CardTitle>
              <Button variant="ghost" size="sm">
                {expandedSections["reference"] ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </div>
          </CardHeader>
          {expandedSections["reference"] && task.ai_reference && (
            <CardContent>
              <div className="bg-gray-50 p-4 rounded-md">
                <div className="prose prose-sm max-w-none">
                  <ReactMarkdown>{task.ai_reference}</ReactMarkdown>
                </div>
              </div>
            </CardContent>
          )}
        </Card>

        {/* 评分标准 */}
        <Card>
          <CardHeader className="pb-2">
            <div
              className="flex justify-between items-center cursor-pointer"
              onClick={() => toggleSection("criteria")}
            >
              <CardTitle className="text-md font-medium flex items-center">
                <BarChart className="h-5 w-5 mr-2" />
                评分标准
              </CardTitle>
              <Button variant="ghost" size="sm">
                {expandedSections["criteria"] ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </div>
          </CardHeader>
          {expandedSections["criteria"] && task.scoring_criteria && (
            <CardContent>
              <div className="bg-gray-50 p-4 rounded-md">
                <pre className="whitespace-pre-wrap text-gray-700 text-sm">
                  {task.scoring_criteria}
                </pre>
              </div>
            </CardContent>
          )}
        </Card>

        {/* 写作技巧 */}
        <Card>
          <CardHeader className="pb-2">
            <div
              className="flex justify-between items-center cursor-pointer"
              onClick={() => toggleSection("tips")}
            >
              <CardTitle className="text-md font-medium flex items-center">
                <Lightbulb className="h-5 w-5 mr-2" />
                写作技巧
              </CardTitle>
              <Button variant="ghost" size="sm">
                {expandedSections["tips"] ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </div>
          </CardHeader>
          {expandedSections["tips"] && task.tips && (
            <CardContent>
              <div className="bg-gray-50 p-4 rounded-md">
                <div className="prose prose-sm max-w-none">
                  <ReactMarkdown>{task.tips}</ReactMarkdown>
                </div>
              </div>
            </CardContent>
          )}
        </Card>
      </div>
    );
  };

  // 渲染页面主体
  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">
        {examType === "英语一" ? "英语一写作" : "英语二写作"}
      </h2>

      <Tabs defaultValue={smallTask?.id.toString() || ""} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger
            value={smallTask?.id.toString() || "small"}
            onClick={() => smallTask && handleTaskChange(smallTask.id)}
            disabled={!smallTask}
          >
            小作文
            <Badge variant="outline" className="ml-2">
              {smallTask?.writing_type || ""}
            </Badge>
          </TabsTrigger>
          <TabsTrigger
            value={largeTask?.id.toString() || "large"}
            onClick={() => largeTask && handleTaskChange(largeTask.id)}
            disabled={!largeTask}
          >
            大作文
            <Badge variant="outline" className="ml-2">
              {largeTask?.writing_type || ""}
            </Badge>
          </TabsTrigger>
        </TabsList>

        {smallTask && (
          <TabsContent value={smallTask.id.toString()} className="mt-4">
            {renderTaskDetails(smallTask)}
          </TabsContent>
        )}

        {largeTask && (
          <TabsContent value={largeTask.id.toString()} className="mt-4">
            {renderTaskDetails(largeTask)}
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
};

export default WritingComponent;
