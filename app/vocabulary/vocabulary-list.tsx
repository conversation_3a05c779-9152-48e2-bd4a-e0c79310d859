"use client";

import { Vocabulary } from "@/types/vocabulary";
import { useEffect, useState } from "react";
import VocabularyCard from "./vocabulary-card";

// 客户端获取词汇数据的函数
async function fetchVocabulary(
  letter: string,
  search?: string
): Promise<Vocabulary[]> {
  try {
    // 构建请求URL
    const params = new URLSearchParams();
    params.append("letter", letter);
    if (search) {
      params.append("search", search);
    }
    // 添加时间戳防止缓存
    params.append("t", Date.now().toString());

    const response = await fetch(`/api/vocabulary?${params.toString()}`, {
      cache: "no-store",
    });

    if (!response.ok) {
      throw new Error(`获取词汇失败: ${response.status}`);
    }

    const data = await response.json();
    return data.vocabulary || [];
  } catch (error) {
    console.error("Error fetching vocabulary:", error);
    return [];
  }
}

// 骨架屏组件
function WordCardSkeleton() {
  return (
    <div className="border rounded-lg shadow-sm bg-white overflow-hidden">
      <div className="p-4 animate-pulse">
        <div className="h-6 w-24 bg-gray-200 rounded mb-2"></div>
        <div className="h-4 w-32 bg-gray-100 rounded mb-3"></div>
        <div className="h-4 w-full bg-gray-100 rounded"></div>
      </div>
    </div>
  );
}

export default function VocabularyList({
  searchParams,
}: {
  searchParams?: { letter?: string; search?: string };
}) {
  // 默认使用字母A
  const letter = searchParams?.letter || "a";
  const search = searchParams?.search;

  // 当搜索内容为空时，确保显示指定字母的词汇
  const actualSearch = search && search.trim() !== "" ? search : undefined;

  const [vocabularyData, setVocabularyData] = useState<Vocabulary[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 客户端获取数据
  useEffect(() => {
    setLoading(true);
    setError(null);

    fetchVocabulary(letter, actualSearch)
      .then((data) => {
        setVocabularyData(data);
        setLoading(false);
      })
      .catch((err) => {
        console.error("获取词汇出错:", err);
        setError("获取词汇数据失败，请稍后再试");
        setLoading(false);
      });
  }, [letter, actualSearch]);

  // 加载状态
  if (loading) {
    return (
      <div>
        <div className="mb-6 flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-800">正在加载...</h2>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(9)].map((_, index) => (
            <WordCardSkeleton key={index} />
          ))}
        </div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-8 w-8 text-red-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
        </div>
        <h3 className="text-xl font-medium text-gray-800 mb-2">{error}</h3>
        <p className="text-gray-500 max-w-md">尝试刷新页面或稍后再试。</p>
      </div>
    );
  }

  // 结果为空
  if (vocabularyData.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-8 w-8 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <h3 className="text-xl font-medium text-gray-800 mb-2">
          未找到匹配的词汇
        </h3>
        <p className="text-gray-500 max-w-md">
          尝试切换到其他字母或使用不同的搜索词。
        </p>
      </div>
    );
  }

  // 词汇总数
  const totalWords = vocabularyData.length;

  return (
    <div>
      {/* 词汇统计 */}
      <div className="mb-6 flex justify-between items-center">
        <h2 className="text-xl font-bold text-gray-800">
          {actualSearch ? "搜索结果" : `${letter.toUpperCase()} 开头的单词`}
        </h2>
        <div className="text-sm bg-blue-50 text-blue-600 px-3 py-1 rounded-full">
          共 {totalWords} 个单词
        </div>
      </div>

      {/* 词汇列表 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {vocabularyData.map((word) => (
          <VocabularyCard key={word.id} word={word} />
        ))}
      </div>
    </div>
  );
}
