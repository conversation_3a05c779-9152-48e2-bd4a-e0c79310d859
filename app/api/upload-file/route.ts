import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// 增加文件大小限制（50MB）
export const dynamic = "force-dynamic";
export const maxDuration = 60;
export const runtime = "nodejs";

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const fileName = formData.get("fileName") as string;

    if (!file) {
      return NextResponse.json(
        { error: "没有找到上传的文件" },
        { status: 400 }
      );
    }

    if (!fileName) {
      return NextResponse.json({ error: "没有提供文件名" }, { status: 400 });
    }

    // 获取文件类型
    const contentType = file.type;

    // 将文件内容转换为Buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // 上传到Supabase存储
    const { data, error } = await supabase.storage
      .from("mbdata")
      .upload(fileName, buffer, {
        contentType,
        cacheControl: "3600",
      });

    if (error) {
      throw error;
    }

    // 获取公共URL
    const { data: urlData } = supabase.storage
      .from("mbdata")
      .getPublicUrl(fileName);

    return NextResponse.json({
      success: true,
      fileUrl: urlData.publicUrl,
    });
  } catch (error) {
    console.error("上传文件失败:", error);
    return NextResponse.json(
      {
        error: "上传文件失败",
        details: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 }
    );
  }
}
