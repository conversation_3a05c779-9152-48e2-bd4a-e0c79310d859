import { NextResponse } from "next/server";
import { supabase } from "@/lib/db";

// GET方法：获取单个翻译
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    if (!id) {
      return NextResponse.json({ error: "ID is required" }, { status: 400 });
    }

    // 查询翻译及相关的试卷信息
    const { data, error } = await supabase
      .from("translations")
      .select("*, papers(year, type, section_type)")
      .eq("id", id)
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      return NextResponse.json(
        { error: "Translation not found" },
        { status: 404 }
      );
    }

    // 格式化返回数据
    const formattedData = {
      ...data,
      year: data.papers.year,
      type: data.papers.type,
      section_type: data.papers.section_type,
      title: `${data.papers.year}年全国硕士研究生招生考试${data.papers.type}试题`,
    };

    return NextResponse.json({
      success: true,
      data: formattedData,
    });
  } catch (error) {
    console.error("Error fetching translation:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch translation",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// PUT方法：更新翻译
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    if (!id) {
      return NextResponse.json({ error: "ID is required" }, { status: 400 });
    }

    const { content, reference_translation, analysis } = await request.json();

    if (!content) {
      return NextResponse.json(
        { error: "Content is required" },
        { status: 400 }
      );
    }

    // 首先检查翻译是否存在
    const { data: existingTranslation, error: checkError } = await supabase
      .from("translations")
      .select("id")
      .eq("id", id)
      .single();

    if (checkError || !existingTranslation) {
      return NextResponse.json(
        { error: "Translation not found" },
        { status: 404 }
      );
    }

    // 更新翻译
    const { error: updateError } = await supabase
      .from("translations")
      .update({
        content,
        reference_translation,
        analysis,
      })
      .eq("id", id);

    if (updateError) {
      throw updateError;
    }

    return NextResponse.json({
      success: true,
      message: "Translation updated successfully",
    });
  } catch (error) {
    console.error("Error updating translation:", error);
    return NextResponse.json(
      {
        error: "Failed to update translation",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// DELETE方法：删除翻译
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    if (!id) {
      return NextResponse.json({ error: "ID is required" }, { status: 400 });
    }

    // 首先检查翻译是否存在
    const { data: existingTranslation, error: checkError } = await supabase
      .from("translations")
      .select("id")
      .eq("id", id)
      .single();

    if (checkError || !existingTranslation) {
      return NextResponse.json(
        { error: "Translation not found" },
        { status: 404 }
      );
    }

    // 删除翻译
    const { error: deleteError } = await supabase
      .from("translations")
      .delete()
      .eq("id", id);

    if (deleteError) {
      throw deleteError;
    }

    return NextResponse.json({
      success: true,
      message: "Translation deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting translation:", error);
    return NextResponse.json(
      {
        error: "Failed to delete translation",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
