"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import SentenceAnalysis from "@/app/components/SentenceAnalysis";

interface Translation {
  main: string;
  alternatives: string[];
  difficulties: string[];
  tips: string[];
}

interface GrammarAnalysis {
  structure: string;
  keyPoints: string[];
  specialCases: string[];
  learningTips: string[];
}

interface Word {
  word: string;
  pos: string;
  meaning: string;
  collocations: string[];
  synonyms: string[];
  memoryTips: string;
}

interface Phrase {
  phrase: string;
  meaning: string;
  usage: string;
  examples: string[];
}

interface Vocabulary {
  keyWords: Word[];
  phrases: Phrase[];
}

interface SentenceStructure {
  type: string;
  components: string[];
  clauses: string[];
  specialPatterns: string[];
  rewriteSuggestions: string[];
}

interface KnowledgePoints {
  examPoints: string[];
  relatedQuestions: string[];
  commonMistakes: string[];
  learningTips: string[];
  reviewFocus: string[];
}

interface AnalysisResult {
  translation: Translation;
  grammarAnalysis: GrammarAnalysis;
  vocabulary: Vocabulary;
  sentenceStructure: SentenceStructure;
  knowledgePoints: KnowledgePoints;
}

interface Sentence extends AnalysisResult {
  id: string;
  content: string;
  isAnalyzed: boolean;
}

const PaperEditPage = ({ params }: { params: { id: string } }) => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<"content" | "sentences">(
    "content"
  );
  const [content, setContent] = useState("");
  const [sentences, setSentences] = useState<Sentence[]>([]);
  const [selectedSentence, setSelectedSentence] = useState<Sentence | null>(
    null
  );

  const handleContentSplit = async () => {
    try {
      const response = await fetch("/api/split", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ content }),
      });

      if (!response.ok) {
        throw new Error("拆分失败");
      }

      const data = await response.json();

      // 将拆分后的句子转换为正确的格式
      const formattedSentences: Sentence[] = data.sentences.map(
        (content: string, index: number) => ({
          id: `sentence-${index + 1}`,
          content,
          isAnalyzed: false,
          translation: {
            main: "",
            alternatives: [],
            difficulties: [],
            tips: [],
          },
          grammarAnalysis: {
            structure: "",
            keyPoints: [],
            specialCases: [],
            learningTips: [],
          },
          vocabulary: {
            keyWords: [],
            phrases: [],
          },
          sentenceStructure: {
            type: "",
            components: [],
            clauses: [],
            specialPatterns: [],
            rewriteSuggestions: [],
          },
          knowledgePoints: {
            examPoints: [],
            relatedQuestions: [],
            commonMistakes: [],
            learningTips: [],
            reviewFocus: [],
          },
        })
      );

      setSentences(formattedSentences);
    } catch (error) {
      console.error("内容拆分失败:", error);
    }
  };

  const handleSentenceAnalyze = async (sentenceId: string) => {
    try {
      const sentence = sentences.find((s) => s.id === sentenceId);
      if (!sentence) return;

      const response = await fetch("/api/analyze-sentence", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ content: sentence.content }),
      });

      if (!response.ok) {
        throw new Error("分析失败");
      }

      const result = await response.json();

      // 确保 result 包含所有必需的字段
      if (
        !result.translation ||
        !result.grammarAnalysis ||
        !result.vocabulary ||
        !result.sentenceStructure ||
        !result.knowledgePoints
      ) {
        throw new Error("分析结果格式不正确");
      }

      setSentences((prev) =>
        prev.map((s) =>
          s.id === sentenceId
            ? {
                ...s,
                translation: result.translation,
                grammarAnalysis: result.grammarAnalysis,
                vocabulary: result.vocabulary,
                sentenceStructure: result.sentenceStructure,
                knowledgePoints: result.knowledgePoints,
                isAnalyzed: true,
              }
            : s
        )
      );
    } catch (error) {
      console.error("句子分析失败:", error);
    }
  };

  const handleSentenceTranslate = async (sentenceId: string) => {
    try {
      const response = await fetch("/api/analyze-sentence", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: sentences.find((s) => s.id === sentenceId)?.content,
        }),
      });

      if (!response.ok) {
        throw new Error("翻译失败");
      }

      const result = await response.json();

      setSentences((prev) =>
        prev.map((s) =>
          s.id === sentenceId
            ? {
                ...s,
                translation: result.translation,
                isAnalyzed: true,
              }
            : s
        )
      );
    } catch (error) {
      console.error("句子翻译失败:", error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">编辑真题</h1>
        <div className="space-x-4">
          <button
            onClick={() => router.back()}
            className="px-4 py-2 border rounded-md hover:bg-gray-50"
          >
            返回
          </button>
          <button
            onClick={() => {
              // 保存更改
              router.push("/admin/papers");
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            保存
          </button>
        </div>
      </div>

      {/* 标签页 */}
      <div className="border-b">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab("content")}
            className={`${
              activeTab === "content"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
          >
            内容编辑
          </button>
          <button
            onClick={() => setActiveTab("sentences")}
            className={`${
              activeTab === "sentences"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
          >
            句子分析
          </button>
        </nav>
      </div>

      {/* 内容编辑 */}
      {activeTab === "content" && (
        <div className="space-y-4">
          <div className="bg-white rounded-lg shadow p-4">
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              className="w-full h-96 p-4 border rounded-md"
              placeholder="请输入真题内容..."
            />
          </div>
          <div className="flex justify-end">
            <button
              onClick={handleContentSplit}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              拆分内容
            </button>
          </div>
        </div>
      )}

      {/* 句子分析 */}
      {activeTab === "sentences" && (
        <div className="grid grid-cols-2 gap-6">
          {/* 句子列表 */}
          <div className="bg-white rounded-lg shadow p-4">
            <h2 className="text-lg font-medium mb-4">句子列表</h2>
            <div className="space-y-2">
              {sentences.map((sentence) => (
                <div
                  key={sentence.id}
                  className={`p-4 border rounded-md cursor-pointer ${
                    selectedSentence?.id === sentence.id
                      ? "border-blue-500 bg-blue-50"
                      : "hover:bg-gray-50"
                  }`}
                  onClick={() => setSelectedSentence(sentence)}
                >
                  <p className="text-sm">{sentence.content}</p>
                  {sentence.translation && (
                    <p className="text-sm text-gray-500 mt-1">
                      {sentence.translation.main}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* 句子分析面板 */}
          <div className="bg-white rounded-lg shadow p-4">
            <h2 className="text-lg font-medium mb-4">句子分析</h2>
            {selectedSentence ? (
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium">原文</h3>
                  <p className="mt-1">{selectedSentence.content}</p>
                </div>
                {selectedSentence.isAnalyzed ? (
                  <SentenceAnalysis
                    result={{
                      translation: selectedSentence.translation,
                      grammarAnalysis: selectedSentence.grammarAnalysis,
                      vocabulary: selectedSentence.vocabulary,
                      sentenceStructure: selectedSentence.sentenceStructure,
                      knowledgePoints: selectedSentence.knowledgePoints,
                    }}
                  />
                ) : (
                  <div className="flex justify-center">
                    <button
                      onClick={() => handleSentenceAnalyze(selectedSentence.id)}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      开始分析
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-500">请选择一个句子进行分析</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default PaperEditPage;
