"use client";

import { useState, useEffect } from "react";
import {
  Loader2,
  RefreshCw,
  Download,
  Users,
  Clock,
  Calendar,
} from "lucide-react";

interface DownloadStat {
  ip_address: string;
  count: number;
  created_at: string;
  updated_at: string;
}

interface AnalyticsData {
  totalDownloads: number;
  todayDownloads: number;
  uniqueVisitors: number;
  todayUniqueVisitors: number;
  maxedOutUsers: number;
  todayMaxedOutUsers: number;
  ipData: DownloadStat[];
  loading: boolean;
}

export default function AnalyticsPage() {
  const [stats, setStats] = useState<AnalyticsData>({
    totalDownloads: 0,
    todayDownloads: 0,
    uniqueVisitors: 0,
    todayUniqueVisitors: 0,
    maxedOutUsers: 0,
    todayMaxedOutUsers: 0,
    ipData: [],
    loading: true,
  });
  const [freeLimit, setFreeLimit] = useState(3);
  const [selectedDate, setSelectedDate] = useState<string>(
    new Date().toISOString().split("T")[0]
  );

  // 获取下载统计数据
  const fetchAnalyticsData = async (date?: string) => {
    setStats((prev) => ({ ...prev, loading: true }));
    try {
      const dateParam = date || selectedDate;
      const response = await fetch(`/api/admin/analytics?date=${dateParam}`);
      if (!response.ok) {
        throw new Error("获取数据失败");
      }
      const data = await response.json();
      setStats({
        totalDownloads: data.totalDownloads,
        todayDownloads: data.todayDownloads,
        uniqueVisitors: data.uniqueVisitors,
        todayUniqueVisitors: data.todayUniqueVisitors,
        maxedOutUsers: data.maxedOutUsers,
        todayMaxedOutUsers: data.todayMaxedOutUsers,
        ipData: data.ipData,
        loading: false,
      });
      setFreeLimit(data.freeLimit);
    } catch (error) {
      console.error("获取统计数据错误:", error);
      setStats((prev) => ({ ...prev, loading: false }));
    }
  };

  useEffect(() => {
    fetchAnalyticsData();
  }, []);

  // 处理日期变更
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDate = e.target.value;
    setSelectedDate(newDate);
    fetchAnalyticsData(newDate);
  };

  return (
    <div className="space-y-6 max-w-full">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-800">下载统计分析</h1>
        <div className="flex items-center gap-4">
          <div className="flex items-center bg-white p-2 rounded-lg shadow-sm">
            <Calendar className="w-5 h-5 text-gray-500 mr-2" />
            <input
              type="date"
              value={selectedDate}
              onChange={handleDateChange}
              className="border-none focus:outline-none text-gray-700"
            />
          </div>
          <button
            onClick={() => fetchAnalyticsData()}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            刷新数据
          </button>
        </div>
      </div>

      {stats.loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="w-12 h-12 text-blue-600 animate-spin" />
        </div>
      ) : (
        <>
          {/* 统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-5">
            <StatCard
              title="总下载次数"
              value={stats.totalDownloads.toString()}
              icon={<Download className="h-7 w-7" />}
              iconBg="bg-blue-100"
              iconColor="text-blue-500"
              bgColor="bg-blue-50"
            />
            <StatCard
              title="今日下载量"
              value={stats.todayDownloads.toString()}
              icon={<Clock className="h-7 w-7" />}
              iconBg="bg-green-100"
              iconColor="text-green-500"
              bgColor="bg-green-50"
            />
            <StatCard
              title="总独立访问用户"
              value={stats.uniqueVisitors.toString()}
              icon={<Users className="h-7 w-7" />}
              iconBg="bg-blue-100"
              iconColor="text-blue-500"
              bgColor="bg-blue-50"
            />
            <StatCard
              title="今日独立访问用户"
              value={stats.todayUniqueVisitors.toString()}
              icon={<Users className="h-7 w-7" />}
              iconBg="bg-purple-100"
              iconColor="text-purple-500"
              bgColor="bg-purple-50"
            />
            <StatCard
              title="今日达到限额用户"
              value={stats.todayMaxedOutUsers.toString()}
              icon={<Users className="h-7 w-7" />}
              iconBg="bg-amber-100"
              iconColor="text-amber-500"
              bgColor="bg-amber-50"
              description={`已达到${freeLimit}次下载限制的用户数量`}
            />
          </div>

          {/* IP数据表格 */}
          <div className="bg-white rounded-xl shadow-md overflow-hidden mt-8">
            <div className="p-6 bg-gray-50">
              <h2 className="text-xl font-semibold text-gray-800">
                IP详细数据
              </h2>
              <p className="text-gray-500 mt-1">
                {selectedDate === new Date().toISOString().split("T")[0]
                  ? "今日"
                  : selectedDate}{" "}
                访问者的IP地址和下载次数信息
              </p>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      IP地址
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      下载次数
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      首次访问时间
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      最近访问时间
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      状态
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {stats.ipData.map((item, index) => (
                    <tr
                      key={index}
                      className={item.count >= freeLimit ? "bg-amber-50" : ""}
                    >
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {item.ip_address}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.count}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(item.created_at).toLocaleString("zh-CN")}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(item.updated_at).toLocaleString("zh-CN")}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            item.count >= freeLimit
                              ? "bg-red-100 text-red-800"
                              : "bg-green-100 text-green-800"
                          }`}
                        >
                          {item.count >= freeLimit ? "已达限额" : "正常"}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {stats.ipData.length === 0 && (
                <div className="text-center py-8 text-gray-500">暂无数据</div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
}

// 统计卡片组件
function StatCard({
  title,
  value,
  icon,
  description,
  bgColor = "bg-blue-50",
  iconBg = "bg-blue-100",
  iconColor = "text-blue-500",
}: {
  title: string;
  value: string;
  icon: React.ReactNode;
  description?: string;
  bgColor?: string;
  iconBg?: string;
  iconColor?: string;
}) {
  return (
    <div
      className={`${bgColor} rounded-2xl p-6 transition-all duration-300 hover:shadow`}
    >
      <div className="flex flex-col h-full">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-sm font-medium text-gray-600">{title}</h3>
          <div className={`p-2 rounded-full ${iconBg}`}>
            <div className={iconColor}>{icon}</div>
          </div>
        </div>
        <div className="mt-auto">
          <p className="text-4xl font-bold">{value}</p>
          {description && (
            <p className="text-xs text-gray-500 mt-2 max-w-[180px]">
              {description}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
