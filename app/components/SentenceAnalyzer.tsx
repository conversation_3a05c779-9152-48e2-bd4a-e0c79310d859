"use client";

import { useState } from "react";

interface SentenceAnalysis {
  id: number;
  content: string;
  translation: string;
  grammarAnalysis: string;
  keyWords: string[];
  examPoints: string[];
}

interface SentenceAnalyzerProps {
  sentence: SentenceAnalysis;
  onTranslate: (sentenceId: number) => Promise<void>;
  onAnalyze: (sentenceId: number) => Promise<void>;
  onUpdate: (
    sentenceId: number,
    updates: Partial<SentenceAnalysis>
  ) => Promise<void>;
}

const SentenceAnalyzer: React.FC<SentenceAnalyzerProps> = ({
  sentence,
  onTranslate,
  onAnalyze,
  onUpdate,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedAnalysis, setEditedAnalysis] = useState<
    Partial<SentenceAnalysis>
  >({});

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = async () => {
    await onUpdate(sentence.id, editedAnalysis);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedAnalysis({});
    setIsEditing(false);
  };

  return (
    <div className="bg-white rounded-lg shadow p-6 space-y-6">
      {/* 原文 */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">原文</h3>
        <p className="text-gray-700">{sentence.content}</p>
      </div>

      {/* 翻译 */}
      <div>
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-medium text-gray-900">翻译</h3>
          <div className="space-x-2">
            {!isEditing && (
              <button
                onClick={() => onTranslate(sentence.id)}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                重新翻译
              </button>
            )}
          </div>
        </div>
        {isEditing ? (
          <textarea
            value={editedAnalysis.translation || sentence.translation}
            onChange={(e) =>
              setEditedAnalysis({
                ...editedAnalysis,
                translation: e.target.value,
              })
            }
            className="w-full h-32 p-2 border rounded-md"
            placeholder="输入翻译..."
          />
        ) : (
          <p className="text-gray-700">{sentence.translation}</p>
        )}
      </div>

      {/* 语法分析 */}
      <div>
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-medium text-gray-900">语法分析</h3>
          <div className="space-x-2">
            {!isEditing && (
              <button
                onClick={() => onAnalyze(sentence.id)}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                重新分析
              </button>
            )}
          </div>
        </div>
        {isEditing ? (
          <textarea
            value={editedAnalysis.grammarAnalysis || sentence.grammarAnalysis}
            onChange={(e) =>
              setEditedAnalysis({
                ...editedAnalysis,
                grammarAnalysis: e.target.value,
              })
            }
            className="w-full h-32 p-2 border rounded-md"
            placeholder="输入语法分析..."
          />
        ) : (
          <p className="text-gray-700 whitespace-pre-wrap">
            {sentence.grammarAnalysis}
          </p>
        )}
      </div>

      {/* 重点词汇 */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">重点词汇</h3>
        {isEditing ? (
          <textarea
            value={
              editedAnalysis.keyWords?.join("\n") ||
              sentence.keyWords.join("\n")
            }
            onChange={(e) =>
              setEditedAnalysis({
                ...editedAnalysis,
                keyWords: e.target.value
                  .split("\n")
                  .filter((word) => word.trim()),
              })
            }
            className="w-full h-32 p-2 border rounded-md"
            placeholder="每行输入一个单词..."
          />
        ) : (
          <div className="flex flex-wrap gap-2">
            {sentence.keyWords.map((word, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
              >
                {word}
              </span>
            ))}
          </div>
        )}
      </div>

      {/* 考点提示 */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">考点提示</h3>
        {isEditing ? (
          <textarea
            value={
              editedAnalysis.examPoints?.join("\n") ||
              sentence.examPoints.join("\n")
            }
            onChange={(e) =>
              setEditedAnalysis({
                ...editedAnalysis,
                examPoints: e.target.value
                  .split("\n")
                  .filter((point) => point.trim()),
              })
            }
            className="w-full h-32 p-2 border rounded-md"
            placeholder="每行输入一个考点..."
          />
        ) : (
          <ul className="list-disc list-inside space-y-1">
            {sentence.examPoints.map((point, index) => (
              <li key={index} className="text-gray-700">
                {point}
              </li>
            ))}
          </ul>
        )}
      </div>

      {/* 编辑按钮 */}
      <div className="flex justify-end space-x-2">
        {isEditing ? (
          <>
            <button
              onClick={handleCancel}
              className="px-4 py-2 text-sm text-gray-700 hover:text-gray-900"
            >
              取消
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              保存
            </button>
          </>
        ) : (
          <button
            onClick={handleEdit}
            className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            编辑
          </button>
        )}
      </div>
    </div>
  );
};

export default SentenceAnalyzer;
