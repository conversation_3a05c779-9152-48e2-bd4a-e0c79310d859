export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      papers: {
        Row: {
          id: number;
          year: string;
          type: string;
          section_type: string;
          task_type: string | null;
          content: string | null;
          reference_translation: string | null;
          ai_reference: string | null;
          writing_analysis: string | null;
          translation_analysis: string | null;
          image_url: string | null;
          image_description: string | null;
          is_public: boolean;
          created_at: string;
          updated_at: string;
          preview_content: string | null;
        };
        Insert: {
          id?: number;
          year: string;
          type: string;
          section_type: string;
          task_type?: string | null;
          content?: string | null;
          reference_translation?: string | null;
          ai_reference?: string | null;
          writing_analysis?: string | null;
          translation_analysis?: string | null;
          image_url?: string | null;
          image_description?: string | null;
          is_public?: boolean;
          created_at?: string;
          updated_at?: string;
          preview_content?: string | null;
        };
        Update: {
          id?: number;
          year?: string;
          type?: string;
          section_type?: string;
          task_type?: string | null;
          content?: string | null;
          reference_translation?: string | null;
          ai_reference?: string | null;
          writing_analysis?: string | null;
          translation_analysis?: string | null;
          image_url?: string | null;
          image_description?: string | null;
          is_public?: boolean;
          created_at?: string;
          updated_at?: string;
          preview_content?: string | null;
        };
      };
      sentences: {
        Row: {
          id: number;
          article_id: number;
          explain_md: string | null;
          paragraph_num: number;
          sequence: number;
          content: string;
          is_marked: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: number;
          article_id: number;
          explain_md?: string | null;
          paragraph_num?: number;
          sequence?: number;
          content: string;
          is_marked?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: number;
          article_id?: number;
          explain_md?: string | null;
          paragraph_num?: number;
          sequence?: number;
          content?: string;
          is_marked?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      vocabulary: {
        Row: {
          id: number;
          word: string;
          first_letter: string;
          simple_translation: string;
          pronunciation: string | null;
          analysis: string | null;
          draw_explain: string | null;
          draw_prompt: string | null;
          is_public: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: number;
          word: string;
          first_letter: string;
          simple_translation: string;
          pronunciation?: string | null;
          analysis?: string | null;
          draw_explain?: string | null;
          draw_prompt?: string | null;
          is_public?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: number;
          word?: string;
          first_letter?: string;
          simple_translation?: string;
          pronunciation?: string | null;
          analysis?: string | null;
          draw_explain?: string | null;
          draw_prompt?: string | null;
          is_public?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}
