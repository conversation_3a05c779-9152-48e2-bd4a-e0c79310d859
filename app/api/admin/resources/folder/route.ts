import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// POST请求处理函数 - 创建文件夹
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { folderName, path } = body;

    if (!folderName || !folderName.trim()) {
      return NextResponse.json(
        { error: "文件夹名称不能为空" },
        { status: 400 }
      );
    }

    // 验证文件夹名称（不能包含特殊字符）
    const validNameRegex = /^[a-zA-Z0-9\u4e00-\u9fa5\s_-]+$/;
    if (!validNameRegex.test(folderName.trim())) {
      return NextResponse.json(
        { error: "文件夹名称只能包含字母、数字、中文、空格、下划线和短横线" },
        { status: 400 }
      );
    }

    // 构建文件夹路径
    const cleanFolderName = folderName.trim();
    const folderPath = path ? `${path}/${cleanFolderName}` : cleanFolderName;

    // 在Supabase Storage中创建文件夹（通过上传一个隐藏文件）
    // Supabase Storage需要至少有一个文件才能创建文件夹
    const placeholderContent = new TextEncoder().encode("folder_placeholder");
    const placeholderPath = `${folderPath}/.gitkeep`;

    const { data, error } = await supabase.storage
      .from("mbdata")
      .upload(placeholderPath, placeholderContent, {
        contentType: "text/plain",
        upsert: false, // 不覆盖已存在的文件
      });

    if (error) {
      console.error("创建文件夹失败:", error);

      // 如果是因为文件夹已存在
      if (error.message.includes("already exists")) {
        return NextResponse.json({ error: "文件夹已存在" }, { status: 409 });
      }

      return NextResponse.json(
        { error: "创建文件夹失败", details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "文件夹创建成功",
      folder: {
        name: cleanFolderName,
        path: folderPath,
      },
    });
  } catch (error) {
    console.error("创建文件夹过程中出现错误:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
