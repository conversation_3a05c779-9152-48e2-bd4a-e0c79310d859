import { NextResponse } from "next/server";
import OpenAI from "openai";

// 创建OpenAI实例，实际使用的是百炼API
const createOpenAI = () => {
  return new OpenAI({
    apiKey: process.env.DASHSCOPE_API_KEY || "",
    baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1",
  });
};

export async function POST(request: Request) {
  try {
    const { imageUrl } = await request.json();

    if (!imageUrl) {
      return NextResponse.json({ error: "缺少图片URL" }, { status: 400 });
    }

    // 使用百炼API分析图片
    const openai = createOpenAI();
    const completion = await openai.chat.completions.create({
      model: "qwen-vl-max", // 使用通义千问的多模态模型
      messages: [
        {
          role: "system",
          content:
            "你是一位专业的图像分析助手，精通英语教学和写作分析。请用中文详细分析这张图片的内容，包括图片中的主要元素、场景和可能的含义。你的分析将被用于英语考研大作文考题的理解。请从多个角度分析图片的含义，并提供可能的写作主题和观点。",
        },
        {
          role: "user",
          content: [
            {
              type: "text",
              text: "请分析以下图片的内容和含义，尽可能详细。这是一张英语考研大作文的题目图片，需要充分理解其内涵。",
            },
            { type: "image_url", image_url: { url: imageUrl } },
          ],
        },
      ],
    });

    const analysisResult = completion.choices[0].message.content;

    return NextResponse.json({
      success: true,
      imageDescription: analysisResult,
    });
  } catch (error) {
    console.error("分析图片失败:", error);
    return NextResponse.json(
      {
        error: "分析图片失败",
        details: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 }
    );
  }
}
