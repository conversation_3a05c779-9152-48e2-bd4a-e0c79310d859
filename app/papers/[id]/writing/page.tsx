import { createClient } from "@supabase/supabase-js";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/app/components/ui/separator";
import {
  ChevronLeft,
  BookOpen,
  PenTool,
  FileText,
  BarChart4,
  BookText,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { WritingAnalysisPreview } from "@/app/components/writing-analysis-preview";
import ReactMarkdown from "react-markdown";

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// 定义作文类型
const taskTypesMap = {
  small_composition: "小作文",
  large_composition: "大作文",
  other: "其他作文类型",
};

// 定义英语一和英语二的作文特点
const examTypeDetails = {
  英语一: {
    small_composition: {
      name: "应用文",
      description:
        "如通知、邮件、备忘录等日常应用文体，要求格式规范，内容具体，语言简明扼要。",
      icon: <BookText className="h-5 w-5" />,
    },
    large_composition: {
      name: "图表作文",
      description:
        "基于图表数据进行描述、分析和评论，要求全面准确地描述图表并进行合理的分析和评价。",
      icon: <BarChart4 className="h-5 w-5" />,
    },
  },
  英语二: {
    small_composition: {
      name: "提纲作文",
      description: "根据所给提纲展开论述，要求语言流畅，内容连贯，结构清晰。",
      icon: <FileText className="h-5 w-5" />,
    },
    large_composition: {
      name: "图画/漫画作文",
      description: "针对给定的图画或漫画，进行描述和阐释，表达个人观点和态度。",
      icon: <PenTool className="h-5 w-5" />,
    },
  },
};

// 从服务器获取数据
async function getPaperData(id: string) {
  // 首先获取当前试卷数据
  const { data: currentPaper, error: currentError } = await supabase
    .from("papers")
    .select("*")
    .eq("id", id)
    .eq("section_type", "writing")
    .single();

  if (currentError) {
    console.error("获取试卷数据失败:", currentError);
    throw new Error("获取试卷数据失败");
  }

  if (!currentPaper) {
    throw new Error("未找到该试卷");
  }

  // 获取同一年份同一类型的其他作文题目
  const { data: relatedPapers, error: relatedError } = await supabase
    .from("papers")
    .select("*")
    .eq("year", currentPaper.year)
    .eq("type", currentPaper.type)
    .eq("section_type", "writing")
    .neq("id", id); // 排除当前试卷

  if (relatedError) {
    console.error("获取相关试卷数据失败:", relatedError);
    // 相关试卷获取失败不影响当前试卷的显示
  }

  // 整理大小作文数据
  const papers = {
    current: currentPaper,
    small: currentPaper.task_type === "small_composition" ? currentPaper : null,
    large: currentPaper.task_type === "large_composition" ? currentPaper : null,
  };

  // 如果找到相关试卷，添加到对应类型
  if (relatedPapers && relatedPapers.length > 0) {
    relatedPapers.forEach((paper) => {
      if (paper.task_type === "small_composition" && !papers.small) {
        papers.small = paper;
      } else if (paper.task_type === "large_composition" && !papers.large) {
        papers.large = paper;
      }
    });
  }

  return papers;
}

export default async function WritingPage({
  params,
}: {
  params: { id: string };
}) {
  const papers = await getPaperData(params.id);
  const currentPaper = papers.current;
  const examType = currentPaper.type as "英语一" | "英语二";
  const year = currentPaper.year;

  return (
    <div className="container mx-auto py-10 px-4 sm:px-6 max-w-5xl">
      {/* 页面头部：返回链接、标题和标签 */}
      <div className="mb-10">
        <Link
          href="/"
          className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors mb-6 text-sm font-medium"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          <span>返回真题列表</span>
        </Link>

        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl shadow-sm mb-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Badge
                  variant="outline"
                  className="bg-blue-100 text-blue-800 border-blue-200 py-1.5"
                >
                  {year}年
                </Badge>
                <Badge
                  variant="outline"
                  className="bg-indigo-100 text-indigo-800 border-indigo-200 py-1.5"
                >
                  {examType}
                </Badge>
              </div>
              <h1 className="text-3xl font-bold text-gray-800 mb-3">
                写作真题
              </h1>
              <p className="text-gray-600 max-w-2xl">
                考研{examType}写作包含小作文和大作文两部分。
                {examType === "英语一"
                  ? "小作文为应用文写作（如通知、邮件等），大作文为图表作文。"
                  : "小作文为提纲作文，大作文为图画/漫画作文。"}
              </p>
            </div>

            <div className="hidden md:flex md:flex-shrink-0 items-center justify-center bg-white h-16 w-16 rounded-full shadow-md mt-4 md:mt-0">
              <BookOpen className="h-8 w-8 text-blue-600" />
            </div>
          </div>
        </div>

        {/* 大小作文切换选项卡 */}
        <Tabs
          defaultValue={papers.current.task_type || "small_composition"}
          className="space-y-8"
        >
          <div className="bg-white rounded-xl shadow-sm p-1">
            <TabsList className="grid w-full grid-cols-2 h-14">
              <TabsTrigger
                value="small_composition"
                disabled={!papers.small}
                className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 rounded-lg h-12"
              >
                <div className="flex items-center gap-2">
                  {examTypeDetails[examType]?.small_composition.icon}
                  <span className="font-medium">小作文</span>
                  <span className="text-xs text-gray-500 hidden md:inline">
                    （{examTypeDetails[examType]?.small_composition.name}）
                  </span>
                </div>
              </TabsTrigger>
              <TabsTrigger
                value="large_composition"
                disabled={!papers.large}
                className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 rounded-lg h-12"
              >
                <div className="flex items-center gap-2">
                  {examTypeDetails[examType]?.large_composition.icon}
                  <span className="font-medium">大作文</span>
                  <span className="text-xs text-gray-500 hidden md:inline">
                    （{examTypeDetails[examType]?.large_composition.name}）
                  </span>
                </div>
              </TabsTrigger>
            </TabsList>
          </div>

          {/* 小作文内容 */}
          <TabsContent value="small_composition">
            {papers.small ? (
              <WritingTabContent
                paper={papers.small}
                examType={examType}
                taskType="small_composition"
              />
            ) : (
              <Card className="border border-dashed border-gray-300 bg-gray-50">
                <CardContent className="py-12 text-center">
                  <p className="text-gray-500">暂无小作文数据</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* 大作文内容 */}
          <TabsContent value="large_composition">
            {papers.large ? (
              <WritingTabContent
                paper={papers.large}
                examType={examType}
                taskType="large_composition"
              />
            ) : (
              <Card className="border border-dashed border-gray-300 bg-gray-50">
                <CardContent className="py-12 text-center">
                  <p className="text-gray-500">暂无大作文数据</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

// 写作内容组件
function WritingTabContent({
  paper,
  examType,
  taskType,
}: {
  paper: any;
  examType: "英语一" | "英语二";
  taskType: "small_composition" | "large_composition";
}) {
  // 获取写作类型的详细信息
  const writingDetails = examTypeDetails[examType]?.[taskType];

  // 解析写作分析
  const writingAnalysis = paper.writing_analysis
    ? JSON.parse(paper.writing_analysis)
    : null;

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center gap-3 px-1">
        <Badge
          variant="secondary"
          className="inline-flex items-center gap-1.5 py-1.5 px-3 text-sm bg-indigo-100 text-indigo-800 border-0 self-start"
        >
          {writingDetails?.icon}
          <span>{writingDetails?.name || taskTypesMap[taskType]}</span>
        </Badge>

        <p className="text-sm text-gray-600 flex-1">
          {writingDetails?.description}
        </p>
      </div>

      <Tabs defaultValue="prompt" className="space-y-6">
        <div className="bg-white rounded-xl p-1 shadow-sm">
          <TabsList className="grid w-full grid-cols-3 p-1 h-12">
            <TabsTrigger
              value="prompt"
              className="rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700"
            >
              <div className="flex items-center gap-1.5">
                <FileText className="h-4 w-4" />
                <span>题目</span>
              </div>
            </TabsTrigger>
            <TabsTrigger
              value="reference"
              className="rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700"
            >
              <div className="flex items-center gap-1.5">
                <BookOpen className="h-4 w-4" />
                <span>参考答案</span>
              </div>
            </TabsTrigger>
            <TabsTrigger
              value="analysis"
              className="rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700"
            >
              <div className="flex items-center gap-1.5">
                <PenTool className="h-4 w-4" />
                <span>写作分析</span>
              </div>
            </TabsTrigger>
          </TabsList>
        </div>

        {/* 题目部分 */}
        <TabsContent value="prompt">
          <Card className="border-0 shadow-md overflow-hidden">
            <CardHeader className="bg-blue-50 border-b border-blue-100 pb-3">
              <CardTitle className="text-xl text-blue-900 flex items-center gap-2">
                <FileText className="h-5 w-5" />
                写作题目
              </CardTitle>
              <CardDescription className="text-blue-700">
                {taskType === "small_composition"
                  ? `${examType === "英语一" ? "应用文" : "提纲作文"}题目`
                  : `${examType === "英语一" ? "图表作文" : "图画/漫画作文"}题目`}
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6 space-y-6">
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-100 font-serif text-gray-800 whitespace-pre-wrap">
                {paper.content}
              </div>

              {/* 如果是大作文且有图片 */}
              {taskType === "large_composition" && paper.image_url && (
                <div className="mt-8 bg-white p-4 rounded-lg border border-gray-200">
                  <h3 className="text-lg font-semibold mb-4 text-gray-800 flex items-center gap-2">
                    {examType === "英语一" ? (
                      <>
                        <BarChart4 className="h-5 w-5 text-blue-600" /> 图表
                      </>
                    ) : (
                      <>
                        <PenTool className="h-5 w-5 text-blue-600" /> 图画/漫画
                      </>
                    )}
                  </h3>
                  <div className="relative h-96 w-full border rounded-lg overflow-hidden bg-white">
                    <Image
                      src={paper.image_url}
                      alt="写作题目图片"
                      fill
                      style={{ objectFit: "contain" }}
                      className="p-4"
                    />
                  </div>

                  {paper.image_description && (
                    <div className="mt-6 bg-blue-50 p-5 rounded-lg border border-blue-100">
                      <h4 className="font-medium text-blue-800 mb-3 flex items-center gap-1.5">
                        <BookText className="h-4 w-4" />
                        图片描述:
                      </h4>
                      <div className="prose prose-blue prose-sm max-w-none prose-headings:text-blue-800 prose-headings:font-semibold prose-h3:text-lg prose-h4:text-base prose-p:text-gray-700 prose-p:leading-relaxed prose-a:text-blue-600 prose-strong:text-blue-700 prose-strong:font-medium prose-li:text-gray-700 prose-li:leading-relaxed prose-li:marker:text-blue-500">
                        <ReactMarkdown>{paper.image_description}</ReactMarkdown>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 参考答案部分 */}
        <TabsContent value="reference">
          <Card className="border-0 shadow-md overflow-hidden">
            <CardHeader className="bg-green-50 border-b border-green-100 pb-3">
              <CardTitle className="text-xl text-green-900 flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                参考答案
              </CardTitle>
              <CardDescription className="text-green-700">
                AI生成的参考范文，仅供参考
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              {paper.ai_reference ? (
                <div className="prose prose-green max-w-none whitespace-pre-wrap font-serif text-gray-800 leading-relaxed">
                  <ReactMarkdown>{paper.ai_reference}</ReactMarkdown>
                </div>
              ) : (
                <div className="text-center py-12 bg-gray-50 rounded-lg border border-dashed border-gray-200">
                  <p className="text-gray-500">暂无参考答案</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 写作分析部分 */}
        <TabsContent value="analysis">
          <Card className="border-0 shadow-md overflow-hidden">
            <CardHeader className="bg-indigo-50 border-b border-indigo-100 pb-3">
              <CardTitle className="text-xl text-indigo-900 flex items-center gap-2">
                <PenTool className="h-5 w-5" />
                写作分析
              </CardTitle>
              <CardDescription className="text-indigo-700">
                重点难点分析与写作技巧
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              {paper.writing_analysis ? (
                <WritingAnalysisPreview content={paper.writing_analysis} />
              ) : (
                <div className="text-center py-12 bg-gray-50 rounded-lg border border-dashed border-gray-200">
                  <p className="text-gray-500">暂无写作分析</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
