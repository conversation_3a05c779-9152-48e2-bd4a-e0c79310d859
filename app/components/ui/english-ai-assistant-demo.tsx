"use client";

import { useState, FormEvent } from "react";
import { Send, BookO<PERSON>, Paperclip, Mic } from "lucide-react";
import { RiTranslate, RiPenNibLine, RiBookReadLine } from "react-icons/ri";
import { Button } from "@/app/components/ui/button";
import {
  ChatBubble,
  ChatBubbleAvatar,
  ChatBubbleMessage,
} from "@/app/components/ui/chat-bubble";
import { ChatInput } from "@/app/components/ui/chat-input";
import {
  ExpandableChat,
  ExpandableChatHeader,
  ExpandableChatBody,
  ExpandableChatFooter,
} from "@/app/components/ui/expandable-chat";
import { ChatMessageList } from "@/app/components/ui/chat-message-list";

// 头像路径 - 使用Unsplash示例
const USER_AVATAR =
  "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=64&h=64&q=80&crop=faces&fit=crop";
const AI_AVATAR =
  "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=64&h=64&q=80&crop=faces&fit=crop";

type Message = {
  id: string;
  content: string;
  role: "user" | "assistant";
};

export function EnglishAIAssistantDemo() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "initial",
      content: "你好！我是你的考研英语学习助手。请问有什么问题需要我解答？",
      role: "assistant",
    },
  ]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [mode, setMode] = useState<"chat" | "translate" | "writing">("chat");

  // 辅助功能标签
  const assistModes = [
    {
      id: "chat",
      label: "问答",
      icon: <RiBookReadLine className="h-4 w-4" />,
      description: "解答英语学习问题",
    },
    {
      id: "translate",
      label: "翻译",
      icon: <RiTranslate className="h-4 w-4" />,
      description: "中英互译和解析",
    },
    {
      id: "writing",
      label: "写作",
      icon: <RiPenNibLine className="h-4 w-4" />,
      description: "写作指导和修改",
    },
  ];

  // 模拟消息发送（演示用）
  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    // 创建用户消息
    const userMessage: Message = {
      id: Date.now().toString(),
      content: input,
      role: "user",
    };

    // 更新消息并清空输入
    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setIsLoading(true);

    // 模拟AI响应
    setTimeout(() => {
      let aiResponse = "";

      switch (mode) {
        case "chat":
          if (input.includes("词汇") || input.includes("单词")) {
            aiResponse =
              "提高考研英语词汇量的关键是系统学习和定期复习。我建议：\n\n1. 使用考研英语词汇书如《考研英语词汇词根+联想记忆法》\n2. 制定每日学习计划，如每天学习30-50个单词\n3. 结合真题语境记忆单词\n4. 使用间隔重复法进行复习\n5. 多做真题，在实际语境中巩固词汇";
          } else if (input.includes("阅读") || input.includes("理解")) {
            aiResponse =
              "提高考研英语阅读理解能力的方法：\n\n1. 扩大词汇量，特别是学术词汇\n2. 熟悉各类文章结构和常见题型\n3. 练习快速阅读和把握主旨大意的能力\n4. 定期做真题，总结出题规律\n5. 学习从上下文推断词义的技巧\n6. 注意长难句分析，理清句子结构";
          } else {
            aiResponse =
              "作为考研英语学习助手，我可以帮你解答词汇、语法、阅读理解、翻译和写作方面的问题。你也可以向我请教备考策略或请我分析特定的英语句子或段落。";
          }
          break;

        case "translate":
          if (/[a-zA-Z]/.test(input)) {
            // 输入包含英文，进行英译中
            aiResponse = `原文：${input}\n\n翻译：${input.length > 30 ? "这是一段英文的中文翻译示例。翻译时需要注意语境和表达习惯，避免直译导致的不通顺。" : "这是翻译示例。"}\n\n要点解析：\n1. 句式结构分析\n2. 重点词汇讲解\n3. 翻译技巧说明`;
          } else {
            // 中译英
            aiResponse = `原文：${input}\n\n翻译：${input.length > 30 ? "This is an example of Chinese to English translation. When translating, it's important to pay attention to context and expression habits to avoid awkward direct translations." : "This is a translation example."}\n\n要点解析：\n1. 表达习惯差异\n2. 重点词汇选择\n3. 地道表达建议`;
          }
          break;

        case "writing":
          aiResponse = `我已收到你的写作内容，以下是我的修改建议：\n\n1. 内容组织：文章结构清晰，但过渡部分可以增强\n2. 语言表达：部分句子可以更简洁，避免冗余\n3. 词汇使用：可以使用更多高级词汇，如将"very important"改为"crucial"\n4. 语法：注意时态一致性，特别是第二段的时态混用\n\n修改后的版本：\n[这里是修改后的示例文本]`;
          break;
      }

      // 添加AI回复
      setMessages((prev) => [
        ...prev,
        {
          id: (Date.now() + 1).toString(),
          content: aiResponse,
          role: "assistant",
        },
      ]);
      setIsLoading(false);
    }, 1500);
  };

  // 根据选择的模式更改输入提示
  const getPlaceholderByMode = () => {
    switch (mode) {
      case "chat":
        return "输入你的英语学习问题...";
      case "translate":
        return "输入要翻译的文本...";
      case "writing":
        return "输入要修改或点评的英语写作...";
      default:
        return "输入消息...";
    }
  };

  // 切换模式处理
  const handleModeChange = (newMode: "chat" | "translate" | "writing") => {
    setMode(newMode);
    // 根据模式添加系统消息
    const modeMessages = {
      chat: "有什么英语问题需要我帮助吗？",
      translate: "请输入需要翻译的文本，我会提供翻译和解析。",
      writing: "请输入你的英语写作，我会提供修改和建议。",
    };

    setMessages((prev) => [
      ...prev,
      {
        id: Date.now().toString(),
        content: modeMessages[newMode],
        role: "assistant",
      },
    ]);
  };

  return (
    <div className="h-[600px] relative">
      <ExpandableChat
        size="lg"
        position="bottom-right"
        icon={<BookOpen className="h-6 w-6" />}
      >
        <ExpandableChatHeader className="flex-col text-center justify-center bg-blue-50 dark:bg-gray-800">
          <h1 className="text-xl font-semibold">考研英语学习助手</h1>
          <p className="text-sm text-muted-foreground">
            专业解答英语学习问题，提供翻译和写作指导
          </p>

          {/* 模式选择器 */}
          <div className="flex space-x-2 mt-2">
            {assistModes.map((item) => (
              <Button
                key={item.id}
                variant={mode === item.id ? "default" : "outline"}
                size="sm"
                onClick={() =>
                  handleModeChange(item.id as "chat" | "translate" | "writing")
                }
                className="flex items-center gap-1"
              >
                {item.icon}
                <span>{item.label}</span>
              </Button>
            ))}
          </div>
        </ExpandableChatHeader>

        <ExpandableChatBody>
          <ChatMessageList>
            {messages.map((message) => (
              <ChatBubble
                key={message.id}
                variant={message.role === "user" ? "sent" : "received"}
              >
                <ChatBubbleAvatar
                  className="h-8 w-8 shrink-0"
                  src={message.role === "user" ? USER_AVATAR : AI_AVATAR}
                  fallback={message.role === "user" ? "我" : "AI"}
                />
                <ChatBubbleMessage
                  variant={message.role === "user" ? "sent" : "received"}
                >
                  {message.content}
                </ChatBubbleMessage>
              </ChatBubble>
            ))}

            {isLoading && (
              <ChatBubble variant="received">
                <ChatBubbleAvatar
                  className="h-8 w-8 shrink-0"
                  src={AI_AVATAR}
                  fallback="AI"
                />
                <ChatBubbleMessage isLoading />
              </ChatBubble>
            )}
          </ChatMessageList>
        </ExpandableChatBody>

        <ExpandableChatFooter>
          <form
            onSubmit={handleSubmit}
            className="relative rounded-lg border bg-background focus-within:ring-1 focus-within:ring-ring p-1"
          >
            <ChatInput
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder={getPlaceholderByMode()}
              className="min-h-12 resize-none rounded-lg bg-background border-0 p-3 shadow-none focus-visible:ring-0"
            />
            <div className="flex items-center p-3 pt-0 justify-between">
              <div className="flex">
                <Button
                  variant="ghost"
                  size="icon"
                  type="button"
                  title="上传附件"
                >
                  <Paperclip className="size-4" />
                </Button>

                <Button
                  variant="ghost"
                  size="icon"
                  type="button"
                  title="语音输入"
                >
                  <Mic className="size-4" />
                </Button>
              </div>
              <Button type="submit" size="sm" className="ml-auto gap-1.5">
                发送
                <Send className="size-3.5" />
              </Button>
            </div>
          </form>
        </ExpandableChatFooter>
      </ExpandableChat>
    </div>
  );
}
