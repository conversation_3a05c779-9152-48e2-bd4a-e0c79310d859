"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePara<PERSON>, useRouter } from "next/navigation";
import ReactMarkdown from "react-markdown";

interface TranslationPaper {
  id: string;
  title: string;
  year: string;
  type: string;
  sectionType: string;
  content: string;
  reference_translation: string;
  translation_analysis: string;
  createdAt: string;
}

interface AssessmentResult {
  score: number;
  accuracy_analysis: string;
  expression_analysis: string;
  errors: string;
  overall_comment: string;
}

const TranslationDetailPage = () => {
  const params = useParams();
  const router = useRouter();
  const paperId = params?.id as string;

  const [paper, setPaper] = useState<TranslationPaper | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userTranslation, setUserTranslation] = useState("");
  const [assessment, setAssessment] = useState<AssessmentResult | null>(null);
  const [isAssessing, setIsAssessing] = useState(false);
  const [assessmentError, setAssessmentError] = useState<string | null>(null);
  const [showReferenceTranslation, setShowReferenceTranslation] =
    useState(false);

  // 获取翻译详情
  useEffect(() => {
    const fetchTranslationDetail = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/papers/${paperId}`);

        if (!response.ok) {
          throw new Error("获取翻译详情失败");
        }

        const data = await response.json();

        if (data.success) {
          setPaper(data.data);
        } else {
          throw new Error(data.error || "获取数据失败");
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "未知错误");
        console.error("获取翻译详情错误:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchTranslationDetail();
  }, [paperId]);

  // 提交用户翻译评估
  const handleSubmitTranslation = async () => {
    try {
      // 重置之前的评估结果
      setAssessment(null);
      setAssessmentError(null);
      setIsAssessing(true);

      if (!userTranslation.trim()) {
        setAssessmentError("请输入您的翻译");
        setIsAssessing(false);
        return;
      }

      const response = await fetch("/api/translation-assessment", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          originalText: paper?.content,
          userTranslation: userTranslation,
          referenceTranslation: paper?.reference_translation || "",
        }),
      });

      const responseData = await response.json();

      if (!response.ok) {
        console.error("评估请求失败:", responseData);
        throw new Error(responseData.error || "评估请求失败");
      }

      if (responseData.error) {
        console.error("评估处理错误:", responseData);
        throw new Error(`评估处理错误: ${responseData.error}`);
      }

      setAssessment(responseData);
    } catch (err) {
      setAssessmentError(err instanceof Error ? err.message : "评估失败");
      console.error("翻译评估错误:", err);
    } finally {
      setIsAssessing(false);
    }
  };

  const toggleReferenceTranslation = () => {
    setShowReferenceTranslation(!showReferenceTranslation);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
          <p className="text-blue-700 font-medium">正在加载翻译内容...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="bg-white rounded-xl shadow-md p-8 max-w-md w-full text-center">
          <div className="flex justify-center mb-4">
            <div className="bg-red-100 p-3 rounded-full">
              <svg
                className="h-8 w-8 text-red-500"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
          </div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">加载失败</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <div className="flex justify-center">
            <Link
              href="/"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              返回首页
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!paper) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="bg-white rounded-xl shadow-md p-8 max-w-md w-full text-center">
          <div className="flex justify-center mb-4">
            <div className="bg-yellow-100 p-3 rounded-full">
              <svg
                className="h-8 w-8 text-yellow-500"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
          </div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">未找到翻译</h2>
          <p className="text-gray-600 mb-6">
            未能找到指定的翻译内容，可能已被删除或不存在。
          </p>
          <div className="flex justify-center">
            <Link
              href="/"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              返回首页
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pb-12">
      {/* 顶部导航栏 */}
      <div className="bg-white shadow-sm sticky top-0 z-10">
        <div className="container mx-auto px-4 py-4 flex items-center">
          <Link
            href="/"
            className="text-blue-600 hover:text-blue-800 flex items-center"
          >
            <svg
              className="h-5 w-5 mr-2"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
            返回首页
          </Link>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* 翻译标题和基本信息 */}
        <div className="bg-white rounded-xl shadow-md p-6 mb-8">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-800 mb-4">
            {paper.title || `${paper.year}年${paper.type}翻译真题`}
          </h1>

          <div className="flex flex-wrap gap-4">
            <div className="bg-blue-50 px-4 py-2 rounded-lg flex items-center">
              <span className="text-blue-800 font-medium">{paper.year}年</span>
            </div>

            <div className="bg-blue-50 px-4 py-2 rounded-lg flex items-center">
              <span className="text-blue-800 font-medium">{paper.type}</span>
            </div>

            <div className="bg-blue-50 px-4 py-2 rounded-lg flex items-center">
              <span className="text-blue-800 font-medium">翻译</span>
            </div>
          </div>
        </div>

        {/* 原文内容 */}
        <div className="bg-white rounded-xl shadow-md p-6 mb-8">
          <h2 className="text-xl font-bold text-blue-700 mb-4 border-b pb-2 border-blue-200">
            原文内容
          </h2>
          <div className="text-gray-800 leading-relaxed text-lg">
            {paper.content || "无原文内容"}
          </div>
        </div>

        {/* 用户翻译输入 */}
        <div className="bg-white rounded-xl shadow-md p-6 mb-8">
          <h2 className="text-xl font-bold text-blue-700 mb-4 border-b pb-2 border-blue-200">
            我的翻译
          </h2>
          <div className="mb-4">
            <textarea
              className="w-full border border-gray-300 rounded-lg p-4 min-h-[200px] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="请在此输入您的翻译..."
              value={userTranslation}
              onChange={(e) => setUserTranslation(e.target.value)}
            ></textarea>
          </div>

          <div className="flex flex-wrap gap-4">
            <button
              onClick={handleSubmitTranslation}
              disabled={isAssessing || userTranslation.trim() === ""}
              className={`px-6 py-3 rounded-lg text-white font-medium flex items-center space-x-2 ${
                isAssessing || userTranslation.trim() === ""
                  ? "bg-gray-400 cursor-not-allowed"
                  : "bg-blue-600 hover:bg-blue-700"
              }`}
            >
              {isAssessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>评估中...</span>
                </>
              ) : (
                <>
                  <span>AI评估我的翻译</span>
                </>
              )}
            </button>

            {paper.reference_translation && (
              <button
                onClick={toggleReferenceTranslation}
                className="px-6 py-3 rounded-lg bg-white text-blue-600 border border-blue-600 font-medium hover:bg-blue-50"
              >
                {showReferenceTranslation ? "隐藏参考译文" : "查看参考译文"}
              </button>
            )}
          </div>
        </div>

        {/* AI 评分结果 */}
        {assessment && (
          <div className="bg-white rounded-xl shadow-md p-6 mb-8">
            <div className="flex items-center justify-between mb-4 border-b pb-2 border-blue-200">
              <h2 className="text-xl font-bold text-blue-700">AI评分结果</h2>
              <div className="flex items-center">
                <div className="bg-blue-100 text-blue-800 font-bold text-2xl px-4 py-2 rounded-lg">
                  {assessment.score}分
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                准确性分析
              </h3>
              <div className="bg-gray-50 p-4 rounded-lg text-gray-700">
                {assessment.accuracy_analysis}
              </div>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                语言表达
              </h3>
              <div className="bg-gray-50 p-4 rounded-lg text-gray-700">
                {assessment.expression_analysis}
              </div>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                错误分析与改进建议
              </h3>
              <div className="bg-gray-50 p-4 rounded-lg text-gray-700">
                {assessment.errors}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                总体评价
              </h3>
              <div className="bg-gray-50 p-4 rounded-lg text-gray-700">
                {assessment.overall_comment}
              </div>
            </div>
          </div>
        )}

        {/* 评估错误提示 */}
        {assessmentError && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-8">
            <div className="flex items-start text-red-600">
              <svg
                className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <div>
                <p className="font-medium">评估失败</p>
                <p className="mt-1">{assessmentError}</p>
                <p className="mt-2 text-sm">请稍后再试或联系管理员寻求帮助。</p>
              </div>
            </div>
          </div>
        )}

        {/* 参考译文（可切换显示） */}
        {showReferenceTranslation && paper.reference_translation && (
          <div className="bg-white rounded-xl shadow-md p-6 mb-8">
            <h2 className="text-xl font-bold text-blue-700 mb-4 border-b pb-2 border-blue-200">
              参考译文
            </h2>
            <div className="text-gray-800 leading-relaxed text-lg">
              {paper.reference_translation}
            </div>
          </div>
        )}

        {/* 翻译分析 */}
        {paper.translation_analysis && (
          <div className="bg-white rounded-xl shadow-md p-6">
            <h2 className="text-xl font-bold text-blue-700 mb-4 border-b pb-2 border-blue-200">
              翻译分析
            </h2>
            <div className="prose prose-blue max-w-none">
              <ReactMarkdown>{paper.translation_analysis}</ReactMarkdown>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TranslationDetailPage;
