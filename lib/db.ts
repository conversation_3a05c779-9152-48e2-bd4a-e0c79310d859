import { createClient } from "@supabase/supabase-js";

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || "";
export const supabase = createClient(supabaseUrl, supabaseKey);

// 向后兼容性，提供pool对象以便逐步迁移
export const pool = {
  getConnection: async () => {
    console.warn("DEPRECATED: 使用了已弃用的MySQL连接池，请迁移到Supabase");
    return {
      query: async () => {
        throw new Error("MySQL连接已弃用，请使用Supabase");
      },
      execute: async () => {
        throw new Error("MySQL连接已弃用，请使用Supabase");
      },
      release: () => {},
      beginTransaction: async () => {},
      commit: async () => {},
      rollback: async () => {},
    };
  },
};
