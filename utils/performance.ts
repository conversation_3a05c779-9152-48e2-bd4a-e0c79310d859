/**
 * 性能测量工具
 * 用于测量API和函数执行时间
 */

// 存储每个操作的开始时间
const timers: Record<string, number> = {};

/**
 * 开始计时一个操作
 * @param label 操作标签
 * @returns 当前时间戳（毫秒）
 */
export function timeStart(label: string): number {
  const startTime = Date.now();
  timers[label] = startTime;
  return startTime;
}

/**
 * 结束计时一个操作并输出耗时
 * @param label 操作标签
 * @param startTime 开始时间戳（毫秒），如果提供则使用此值代替记录的时间戳
 * @returns 操作耗时（毫秒）
 */
export function timeEnd(label: string, startTime?: number): number {
  const endTime = Date.now();
  const start = startTime || timers[label];

  if (!start) {
    console.warn(`没有找到标签为 "${label}" 的计时器起始记录`);
    return 0;
  }

  const duration = endTime - start;

  console.log(`[性能统计] ${label}: ${duration}ms`);

  // 清除计时器
  if (!startTime) {
    delete timers[label];
  }

  return duration;
}

/**
 * 测量函数执行时间的装饰器
 * @param target 目标对象
 * @param propertyKey 属性键
 * @param descriptor 属性描述符
 */
export function measure(
  target: any,
  propertyKey: string,
  descriptor: PropertyDescriptor
) {
  const originalMethod = descriptor.value;

  descriptor.value = function (...args: any[]) {
    const startTime = Date.now();
    const result = originalMethod.apply(this, args);

    if (result instanceof Promise) {
      return result.then((value) => {
        const duration = Date.now() - startTime;
        console.log(`[性能统计] ${propertyKey}: ${duration}ms`);
        return value;
      });
    }

    const duration = Date.now() - startTime;
    console.log(`[性能统计] ${propertyKey}: ${duration}ms`);
    return result;
  };

  return descriptor;
}
