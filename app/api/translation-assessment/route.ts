import { NextResponse } from "next/server";
import { DeepSeek } from "@/app/services/deepseek";

interface AssessmentResult {
  score: number;
  accuracy_analysis: string;
  expression_analysis: string;
  errors: string;
  overall_comment: string;
}

export async function POST(request: Request) {
  try {
    const { originalText, userTranslation, referenceTranslation } =
      await request.json();

    if (!originalText || !userTranslation) {
      return NextResponse.json(
        { error: "Original text and user translation are required" },
        { status: 400 }
      );
    }

    const prompt = `
你是一位精通英汉翻译的考研英语专家评分员。请对以下翻译进行评分和详细分析。

原文:
${originalText}

用户翻译:
${userTranslation}

${
  referenceTranslation
    ? `参考译文:
${referenceTranslation}`
    : ""
}

请对用户翻译进行评分和分析，给出以下内容：
1. 总体评分: 0-100分
2. 准确性分析: 对译文准确性的评价，指出译文在内容还原方面的优缺点
3. 语言表达: 对译文语言流畅性和表达自然度的评价
4. 翻译错误: 指出译文中的具体错误，并给出修改建议
5. 总体评价: 对翻译的整体评价和改进建议

请以JSON格式返回结果:
{
  "score": 分数,
  "accuracy_analysis": "准确性分析",
  "expression_analysis": "语言表达分析",
  "errors": "错误分析和改进建议",
  "overall_comment": "总体评价"
}

注意:
1. 必须使用中文进行评价
2. 评分要客观公正，既要肯定优点，也要指出不足
3. 提供具体且有建设性的改进意见
4. 确保返回有效的JSON格式
`;

    const deepseek = new DeepSeek();
    const response = await deepseek.chat({
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
    });

    // 解析AI返回的JSON结果
    try {
      const resultContent = response.choices[0].message.content.trim();

      // 处理可能被包含在Markdown代码块中的JSON
      let jsonContent = resultContent;

      // 检查是否是Markdown代码块格式(```json ... ```)
      const codeBlockMatch = resultContent.match(
        /```(?:json)?\s*([\s\S]*?)\s*```/
      );
      if (codeBlockMatch && codeBlockMatch[1]) {
        jsonContent = codeBlockMatch[1].trim();
      }

      // 尝试移除任何非JSON内容
      // 尝试找到第一个{和最后一个}之间的内容
      const jsonMatch = jsonContent.match(/{[\s\S]*}/);
      if (jsonMatch) {
        jsonContent = jsonMatch[0];
      }

      console.log("Parsing JSON content:", jsonContent);
      const assessmentResult = JSON.parse(jsonContent) as AssessmentResult;

      // 验证结果格式
      if (
        typeof assessmentResult.score !== "number" ||
        !assessmentResult.accuracy_analysis ||
        !assessmentResult.expression_analysis ||
        !assessmentResult.errors ||
        !assessmentResult.overall_comment
      ) {
        console.warn("Missing fields in assessment result:", assessmentResult);
        throw new Error("Invalid response format - missing required fields");
      }

      return NextResponse.json(assessmentResult);
    } catch (parseError) {
      console.error("Failed to parse DeepSeek response:", parseError);
      return NextResponse.json(
        {
          error: "Failed to parse assessment result",
          rawResponse: response.choices[0].message.content,
          parseError:
            parseError instanceof Error
              ? parseError.message
              : String(parseError),
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Translation assessment failed:", error);
    return NextResponse.json(
      {
        error: "Failed to assess translation",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
