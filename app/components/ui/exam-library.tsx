"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import {
  BookOpen,
  BookText,
  Languages,
  PenTool,
  Search,
  Info,
  Lock,
  CheckCircle,
  Loader2,
} from "lucide-react";

interface Paper {
  id: number;
  title: string;
  year: string;
  type: string;
  sectionType: string;
  partNumber?: number; // 阅读理解的篇章编号
  task_type?: string; // 写作任务类型（大作文/小作文）
  createdAt: string;
  updatedAt: string;
}

interface SubscriptionInfo {
  isSubscribed?: boolean;
  plan?: string;
  expiresAt?: string;
  isLifetimeMember?: boolean;
  // 新增API实际返回的字段
  isActive?: boolean;
  type?: string;
  endDate?: string;
}

// 固定的题型列表
const SECTION_TYPES = [
  {
    id: "use_of_english",
    name: "完形填空",
    icon: <BookText className="w-4 h-4" />,
  },
  {
    id: "reading_comprehension",
    name: "阅读理解",
    icon: <BookOpen className="w-4 h-4" />,
    subTypes: [
      { id: "reading_comprehension_test1", name: "第一篇" },
      { id: "reading_comprehension_test2", name: "第二篇" },
      { id: "reading_comprehension_test3", name: "第三篇" },
      { id: "reading_comprehension_test4", name: "第四篇" },
    ],
  },
  { id: "translation", name: "翻译", icon: <Languages className="w-4 h-4" /> },
  { id: "writing", name: "写作", icon: <PenTool className="w-4 h-4" /> },
];

// 非会员可访问的年份
const FREE_ACCESS_YEARS = ["2024", "2025"];

const ExamLibrary = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  // 状态管理
  const [papers, setPapers] = useState<Paper[]>([]);
  const [selectedType, setSelectedType] = useState(
    searchParams?.get("type") || "英语一" // 默认选择英语一
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [yearsList, setYearsList] = useState<string[]>([]);

  // 订阅相关状态
  const [subscriptionInfo, setSubscriptionInfo] =
    useState<SubscriptionInfo | null>(null);
  const [subscriptionLoading, setSubscriptionLoading] = useState(false);
  const [subscriptionError, setSubscriptionError] = useState<Error | null>(
    null
  );

  // 处理英语一/英语二切换
  const handleTypeChange = (type: string) => {
    setSelectedType(type);
  };

  // 获取订阅信息的函数
  const fetchSubscriptionInfo = async () => {
    try {
      setSubscriptionLoading(true);

      const response = await fetch("/api/subscription");

      // 如果是401错误，表示用户未登录，不显示错误提示
      if (response.status === 401) {
        // 设置为未订阅的默认状态，但不设置错误
        setSubscriptionInfo({ isActive: false });
        return;
      }

      if (!response.ok) {
        throw new Error("获取订阅信息失败");
      }

      const data = await response.json();

      // 适配API实际返回格式
      if (data.subscriptionInfo) {
        setSubscriptionInfo(data.subscriptionInfo);
      } else if (data.success && data.data) {
        // 兼容旧格式
        setSubscriptionInfo(data.data);
      } else {
        // 如果无法获取有效的订阅信息，默认设置为未订阅
        setSubscriptionInfo({ isActive: false });
      }
    } catch (error) {
      // 不向用户显示错误，只在控制台记录
      console.error("获取订阅信息错误:", error);
      // 默认为未订阅状态
      setSubscriptionInfo({ isActive: false });
    } finally {
      setSubscriptionLoading(false);
    }
  };

  // 从数据库加载papers列表
  const fetchPapers = async () => {
    try {
      setLoading(true);
      setError(null);

      // 构建查询参数 - 只按类型（英语一/英语二）筛选
      const params = new URLSearchParams();
      params.append("type", selectedType);

      // 获取所有数据用于表格展示 - 使用较大的页码大小
      params.append("pageSize", "1000");

      const response = await fetch(`/api/papers?${params.toString()}`);

      if (!response.ok) {
        throw new Error("获取真题列表失败");
      }

      const data = await response.json();

      if (data.success) {
        setPapers(data.data);

        // 提取所有年份并排序（降序）
        const yearsSet = new Set<string>();
        data.data.forEach((paper: Paper) => yearsSet.add(paper.year));
        const yearArray = Array.from(yearsSet) as string[];
        yearArray.sort((a, b) => Number(b) - Number(a)); // 字符串年份转换为数字比较
        setYearsList(yearArray);
      } else {
        throw new Error(data.error || "获取数据失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "未知错误");
      console.error("获取真题列表错误:", err);
    } finally {
      setLoading(false);
    }
  };

  // 初始加载和类型变化时获取数据
  useEffect(() => {
    fetchPapers();
    fetchSubscriptionInfo();

    // 更新URL查询参数
    const url = `${window.location.pathname}?type=${selectedType}`;
    window.history.replaceState({}, "", url);
  }, [selectedType]);

  // 判断用户是否有权限访问特定年份
  const canAccessYear = (year: string) => {
    // 如果有订阅
    if (
      subscriptionInfo?.isSubscribed ||
      subscriptionInfo?.isLifetimeMember ||
      // 新增API实际返回格式的判断
      subscriptionInfo?.isActive
    ) {
      return true;
    }

    // 未订阅用户只能访问指定年份
    return FREE_ACCESS_YEARS.includes(year);
  };

  // 根据年份和题型查找试卷
  const findPapers = (year: string, sectionType: string) => {
    return papers.filter(
      (paper) => paper.year === year && paper.sectionType === sectionType
    );
  };

  // 查找所有阅读理解文章
  const findReadingPapers = (year: string) => {
    return papers.filter(
      (paper) =>
        paper.year === year &&
        paper.sectionType.startsWith("reading_comprehension_test")
    );
  };

  // 查找所有写作相关的试卷
  const findWritingPapers = (year: string) => {
    return papers.filter(
      (paper) => paper.year === year && paper.sectionType === "writing"
    );
  };

  // 渲染阅读理解篇章链接
  const renderReadingLinks = (year: string) => {
    const readingPapers = findReadingPapers(year);

    if (readingPapers.length === 0) {
      return <span className="text-gray-400 text-sm">暂无</span>;
    }

    // 检查用户是否有权限访问
    if (!canAccessYear(year)) {
      return (
        <div className="flex items-center gap-1 text-gray-500">
          <Lock className="w-3 h-3" />
          <span className="text-sm">订阅专享</span>
        </div>
      );
    }

    return (
      <div className="flex flex-wrap gap-2">
        {SECTION_TYPES[1].subTypes?.map((subType, index) => {
          const paper = readingPapers.find((p) => p.sectionType === subType.id);
          if (!paper) return null;

          return (
            <Link
              key={paper.id}
              href={`/papers/${paper.id}`}
              className="px-2 py-1 text-sm bg-blue-50 text-blue-700 rounded hover:bg-blue-100 transition-colors"
            >
              {subType.name}
            </Link>
          );
        })}
      </div>
    );
  };

  // 渲染写作链接
  const renderWritingLinks = (year: string) => {
    const writingPapers = findWritingPapers(year);

    if (writingPapers.length === 0) {
      return <span className="text-gray-400 text-sm">暂无</span>;
    }

    // 检查用户是否有权限访问
    if (!canAccessYear(year)) {
      return (
        <div className="flex items-center gap-1 text-gray-500">
          <Lock className="w-3 h-3" />
          <span className="text-sm">订阅专享</span>
        </div>
      );
    }

    // 查找小作文和大作文
    const smallComposition = writingPapers.find(
      (p) => p.task_type === "small_composition"
    );
    const largeComposition = writingPapers.find(
      (p) => p.task_type === "large_composition"
    );

    // 默认使用第一篇写作文章的链接 - 优先使用小作文ID
    const defaultPaperId =
      smallComposition?.id || largeComposition?.id || writingPapers[0].id;

    return (
      <div className="flex flex-wrap gap-2">
        <Link
          href={`/papers/${defaultPaperId}/writing`}
          className="px-2 py-1 text-sm bg-blue-50 text-blue-700 rounded hover:bg-blue-100 transition-colors"
        >
          查看真题
        </Link>
      </div>
    );
  };

  // 渲染单个试卷链接
  const renderPaperLink = (papers: Paper[]) => {
    if (papers.length === 0)
      return <span className="text-gray-400 text-sm">暂无</span>;

    // 检查用户是否有权限访问
    if (!canAccessYear(papers[0].year)) {
      return (
        <div className="flex items-center gap-1 text-gray-500">
          <Lock className="w-3 h-3" />
          <span className="text-sm">订阅专享</span>
        </div>
      );
    }

    // 为完形填空创建特殊链接
    if (papers[0].sectionType === "use_of_english") {
      return (
        <Link
          href={`/use-of-english/${papers[0].id}`}
          className="px-2 py-1 text-sm bg-blue-50 text-blue-700 rounded hover:bg-blue-100 transition-colors"
        >
          查看真题
        </Link>
      );
    }

    // 为翻译类型创建特殊链接
    if (papers[0].sectionType === "translation") {
      // 根据类型（英语一/英语二）使用不同的URL
      if (papers[0].type === "英语一") {
        return (
          <Link
            href={`/translation/english1/${papers[0].id}`}
            className="px-2 py-1 text-sm bg-blue-50 text-blue-700 rounded hover:bg-blue-100 transition-colors"
          >
            查看真题
          </Link>
        );
      } else {
        return (
          <Link
            href={`/papers/${papers[0].id}/translation`}
            className="px-2 py-1 text-sm bg-blue-50 text-blue-700 rounded hover:bg-blue-100 transition-colors"
          >
            查看真题
          </Link>
        );
      }
    }

    // 写作类型使用专用的渲染方法
    if (papers[0].sectionType === "writing") {
      return null; // 不在这里渲染写作链接，由专用方法处理
    }

    return (
      <Link
        href={`/papers/${papers[0].id}`}
        className="px-2 py-1 text-sm bg-blue-50 text-blue-700 rounded hover:bg-blue-100 transition-colors"
      >
        查看真题
      </Link>
    );
  };

  // 渲染订阅状态信息区块
  const renderSubscriptionBanner = () => {
    if (subscriptionLoading) {
      return (
        <div className="bg-gray-50 p-4 rounded-lg flex items-center justify-center">
          <Loader2 className="w-5 h-5 text-blue-600 animate-spin mr-2" />
          <span className="text-gray-600">正在加载订阅信息...</span>
        </div>
      );
    }

    // 有订阅信息且是订阅用户或终身会员
    if (
      subscriptionInfo?.isSubscribed ||
      subscriptionInfo?.isLifetimeMember ||
      // 新增API实际返回格式的判断
      subscriptionInfo?.isActive
    ) {
      const isLifetimeMember =
        subscriptionInfo?.isLifetimeMember ||
        subscriptionInfo?.type === "终身会员" ||
        subscriptionInfo?.endDate === "永久有效";

      return (
        <div className="bg-green-50 p-4 rounded-lg flex items-start gap-3">
          <div className="flex-shrink-0 text-green-600 mt-1">
            <CheckCircle className="w-5 h-5" />
          </div>
          <div>
            <h3 className="font-medium text-green-800">订阅专享内容已解锁</h3>
            <p className="text-sm text-green-700 mt-1">
              {isLifetimeMember
                ? "尊敬的终身订阅用户，您已解锁全部历年真题内容，可以无限制地访问所有年份的考研英语真题。"
                : "作为订阅用户，您可以无限制访问所有历年真题内容。感谢您对我们平台的支持！"}
            </p>
          </div>
        </div>
      );
    }

    // 未订阅用户或未登录用户
    return (
      <div className="bg-blue-50 p-4 rounded-lg flex items-start gap-3">
        <div className="flex-shrink-0 text-blue-600 mt-1">
          <Info className="w-5 h-5" />
        </div>
        <div>
          <h3 className="font-medium text-blue-800">订阅专享权益</h3>
          <p className="text-sm text-blue-700 mt-1">
            您当前可以免费访问 {FREE_ACCESS_YEARS.join("、")}{" "}
            年的考研英语真题。订阅后可无限制访问所有历年真题。
          </p>
          <div className="mt-3 flex gap-2">
            {/* <Link
              href="/sign-in"
              className="px-4 py-2 bg-white border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
            >
              登录账号
            </Link> */}
            <Link
              href="/pricing"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              立即订阅
            </Link>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* 类型切换 */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-800">
          考研英语真题逐句详解
        </h1>

        <div className="flex border border-gray-300 rounded-lg overflow-hidden">
          <button
            onClick={() => handleTypeChange("英语一")}
            className={`px-4 py-2 ${
              selectedType === "英语一"
                ? "bg-blue-600 text-white"
                : "bg-white text-gray-700 hover:bg-gray-100"
            }`}
          >
            英语一
          </button>
          <button
            onClick={() => handleTypeChange("英语二")}
            className={`px-4 py-2 ${
              selectedType === "英语二"
                ? "bg-blue-600 text-white"
                : "bg-white text-gray-700 hover:bg-gray-100"
            }`}
          >
            英语二
          </button>
        </div>
      </div>

      {/* 订阅状态区块 */}
      {renderSubscriptionBanner()}

      {/* 真题表格 */}
      {loading ? (
        <div className="flex justify-center items-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
        </div>
      ) : error ? (
        <div className="bg-white rounded-xl shadow p-8 text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <button
            onClick={fetchPapers}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            重试
          </button>
        </div>
      ) : yearsList.length === 0 ? (
        <div className="bg-white rounded-xl shadow p-12 text-center">
          <p className="text-lg text-gray-500">暂无{selectedType}真题</p>
        </div>
      ) : (
        <>
          {/* 桌面端表格视图 - 在小屏幕上隐藏 */}
          <div className="hidden md:block overflow-x-auto bg-white rounded-xl shadow">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-blue-50">
                  <th className="px-4 py-3 text-left text-gray-700 font-semibold border-b border-gray-200 w-30">
                    年份
                  </th>
                  {SECTION_TYPES.map((section) => (
                    <th
                      key={section.id}
                      className="px-4 py-3 text-left text-gray-700 font-semibold border-b border-gray-200"
                    >
                      <div className="flex items-center gap-2">
                        {section.icon}
                        <span>{section.name}</span>
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {yearsList.map((year) => {
                  const isLocked = !canAccessYear(year);
                  return (
                    <tr
                      key={year}
                      className={`hover:bg-gray-50 ${isLocked ? "bg-gray-50" : ""}`}
                    >
                      <td className="px-4 py-4 border-b border-gray-200 font-medium text-blue-900">
                        {year}年
                        {isLocked && (
                          <span className="ml-2 inline-flex items-center">
                            <Lock className="w-3 h-3 text-gray-500" />
                          </span>
                        )}
                      </td>

                      {/* 完形填空 */}
                      <td className="px-4 py-4 border-b border-gray-200">
                        {renderPaperLink(findPapers(year, "use_of_english"))}
                      </td>

                      {/* 阅读理解 */}
                      <td className="px-4 py-4 border-b border-gray-200">
                        {renderReadingLinks(year)}
                      </td>

                      {/* 翻译 */}
                      <td className="px-4 py-4 border-b border-gray-200">
                        {renderPaperLink(findPapers(year, "translation"))}
                      </td>

                      {/* 写作 */}
                      <td className="px-4 py-4 border-b border-gray-200">
                        {renderWritingLinks(year)}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {/* 移动端卡片视图 - 在中等及以上屏幕上隐藏 */}
          <div className="md:hidden space-y-4">
            {yearsList.map((year) => {
              const isLocked = !canAccessYear(year);
              return (
                <div
                  key={year}
                  className="bg-white rounded-xl shadow overflow-hidden"
                >
                  <div
                    className={`px-4 py-3 ${isLocked ? "bg-gray-50" : "bg-blue-50"} font-semibold ${isLocked ? "text-gray-700" : "text-blue-900"} flex justify-between items-center`}
                  >
                    <span>
                      {year}年 {selectedType}
                    </span>
                    {isLocked && <Lock className="w-4 h-4 text-gray-500" />}
                  </div>
                  <div className="divide-y divide-gray-100">
                    {/* 完形填空 */}
                    <div className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <BookText className="w-4 h-4 text-blue-600" />
                        <span className="font-medium">完形填空</span>
                      </div>
                      <div className="pl-6">
                        {renderPaperLink(findPapers(year, "use_of_english"))}
                      </div>
                    </div>

                    {/* 阅读理解 */}
                    <div className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <BookOpen className="w-4 h-4 text-blue-600" />
                        <span className="font-medium">阅读理解</span>
                      </div>
                      <div className="pl-6">{renderReadingLinks(year)}</div>
                    </div>

                    {/* 翻译 */}
                    <div className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <Languages className="w-4 h-4 text-blue-600" />
                        <span className="font-medium">翻译</span>
                      </div>
                      <div className="pl-6">
                        {renderPaperLink(findPapers(year, "translation"))}
                      </div>
                    </div>

                    {/* 写作 */}
                    <div className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <PenTool className="w-4 h-4 text-blue-600" />
                        <span className="font-medium">写作</span>
                      </div>
                      <div className="pl-6">{renderWritingLinks(year)}</div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </>
      )}

      {/* 表格下方的提示信息 */}
      <div className="bg-blue-50 p-4 rounded-lg flex items-start gap-3">
        <div className="flex-shrink-0 text-blue-600 mt-1">
          <Info className="w-5 h-5" />
        </div>
        <div>
          <h3 className="font-medium text-blue-800">使用说明</h3>
          <p className="text-sm text-blue-700 mt-1">
            表格展示了{selectedType}
            历年真题的逐句详解，涵盖完形填空、阅读理解、翻译和写作四种题型。点击相应链接可查看详细解析内容。
            如需切换英语一/英语二，请使用顶部的切换按钮。
            {!(
              subscriptionInfo?.isSubscribed ||
              subscriptionInfo?.isLifetimeMember ||
              subscriptionInfo?.isActive
            ) && "非订阅用户仅可查看最近两年的真题详解。"}
            <strong className="block mt-2 text-blue-800">
              关注公众号「面包资料屋」，获取更多独家考研英语学习资料和历年真题解析！
            </strong>
          </p>
        </div>
      </div>
    </div>
  );
};

export { ExamLibrary };
