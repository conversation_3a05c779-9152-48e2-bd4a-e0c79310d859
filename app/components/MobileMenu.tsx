"use client";

import { useState } from "react";
import Link from "next/link";
import { useTheme } from "next-themes";
import { FiMenu, FiX } from "react-icons/fi";
import { BsMoonStarsFill, BsSunFill } from "react-icons/bs";
import { User } from "@supabase/supabase-js";
import { signOutAction } from "@/app/actions";
import { ReactNode } from "react";
import { usePathname } from "next/navigation";

interface NavLink {
  href: string;
  label: string;
  icon: ReactNode;
}

interface MobileMenuProps {
  navLinks: NavLink[];
  user: User | null;
}

const MobileMenu = ({ navLinks, user }: MobileMenuProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();
  const { theme, setTheme } = useTheme();

  const toggleMenu = () => setIsOpen(!isOpen);
  const isActive = (href: string) => pathname === href;

  return (
    <div className="md:hidden">
      <button
        onClick={toggleMenu}
        className="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400"
        aria-label="切换菜单"
      >
        {isOpen ? <FiX className="h-6 w-6" /> : <FiMenu className="h-6 w-6" />}
      </button>

      {isOpen && (
        <div className="absolute left-0 right-0 top-16 p-4 border-t border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 shadow-lg z-50">
          <nav className="flex flex-col space-y-4">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className={`flex items-center text-sm font-medium transition-colors hover:text-blue-600 ${
                  isActive(link.href)
                    ? "text-blue-600 dark:text-blue-400"
                    : "text-gray-700 dark:text-gray-300"
                }`}
                onClick={() => setIsOpen(false)}
              >
                {link.icon}
                {link.label}
              </Link>
            ))}

            {/* 认证相关链接 */}
            <div className="pt-2 mt-2 border-t border-gray-200 dark:border-gray-800">
              {user ? (
                <>
                  <div className="flex items-center mb-3 text-sm font-medium text-gray-700 dark:text-gray-300">
                    <span className="truncate max-w-[250px]">
                      欢迎，{user.email}
                    </span>
                  </div>
                  <Link
                    href="/dashboard"
                    className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-3"
                    onClick={() => setIsOpen(false)}
                  >
                    个人中心
                  </Link>
                  <form action={signOutAction}>
                    <button
                      type="submit"
                      className="w-full text-left text-sm font-medium text-gray-700 dark:text-gray-300"
                      onClick={() => setIsOpen(false)}
                    >
                      退出登录
                    </button>
                  </form>
                </>
              ) : (
                <>
                  <Link
                    href="/sign-in"
                    className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-3"
                    onClick={() => setIsOpen(false)}
                  >
                    登录
                  </Link>
                  <Link
                    href="/sign-up"
                    className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300"
                    onClick={() => setIsOpen(false)}
                  >
                    注册
                  </Link>
                </>
              )}
            </div>

            {/* 主题切换 */}
            <div className="pt-2 border-t border-gray-200 dark:border-gray-800">
              <button
                onClick={() => {
                  setTheme(theme === "dark" ? "light" : "dark");
                  setIsOpen(false);
                }}
                className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                {theme === "dark" ? (
                  <>
                    <BsSunFill className="mr-2 h-4 w-4 text-yellow-500" />
                    亮色模式
                  </>
                ) : (
                  <>
                    <BsMoonStarsFill className="mr-2 h-4 w-4 text-gray-700" />
                    暗色模式
                  </>
                )}
              </button>
            </div>
          </nav>
        </div>
      )}
    </div>
  );
};

export default MobileMenu;
