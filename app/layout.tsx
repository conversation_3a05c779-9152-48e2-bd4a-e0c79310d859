import type { Metadata } from "next";
import "./globals.css";
import Navbar from "./components/Navbar";
import { EnglishAIChatAssistant } from "./components/EnglishAIChatAssistant";

export const metadata: Metadata = {
  title: "考试真题资源平台",
  description:
    "提供考研英语、政治、数学、专业课以及四六级、专四专八等历年真题与解析，助力备考",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <body className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex-1">{children}</div>

        {/* 英语学习助手 - 在所有页面显示 */}
        {/* <EnglishAIChatAssistant /> */}

        {/* 页脚 */}
        <footer className="bg-gray-800 text-white py-4">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                <h3 className="text-xl font-semibold mb-4">考试真题资源平台</h3>
                <p className="text-gray-300">
                  提供全面的考研英语、政治、数学、专业课以及四六级、专四专八等考试真题与详解，助力备考提升。
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">快速链接</h3>
                <ul className="space-y-2">
                  <li>
                    <a
                      href="/"
                      className="text-gray-300 hover:text-white transition-colors"
                    >
                      首页
                    </a>
                  </li>
                  <li>
                    <a
                      href="/exam-library"
                      className="text-gray-300 hover:text-white transition-colors"
                    >
                      考研英语真题逐句详解
                    </a>
                  </li>
                  <li>
                    <a
                      href="/learning-resources"
                      className="text-gray-300 hover:text-white transition-colors"
                    >
                      真题资源下载
                    </a>
                  </li>
                  <li>
                    <a
                      href="/vocabulary"
                      className="text-gray-300 hover:text-white transition-colors"
                    >
                      词汇学习
                    </a>
                  </li>
                  {/* <li>
                    <a
                      href="/translation-writing-example"
                      className="text-gray-300 hover:text-white transition-colors"
                    >
                      翻译写作练习
                    </a>
                  </li>
                  <li>
                    <a
                      href="/admin/papers"
                      className="text-gray-300 hover:text-white transition-colors"
                    >
                      句子分析
                    </a>
                  </li> */}
                  <li>
                    <a
                      href="/"
                      className="flex items-center gap-1.5 text-gray-300 hover:text-white transition-colors"
                    >
                      <span className="inline-block w-4 h-4">📱</span>
                      公众号：面包资料屋
                    </a>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">联系我们</h3>
                <p className="text-gray-300 mb-2">邮箱: <EMAIL></p>
                <p className="text-gray-300">微信: salute777888</p>
                <div className="mt-4 p-3 bg-gray-700 rounded-lg">
                  <p className="text-white font-medium mb-2">🎁 获取更多资源</p>
                  <p className="text-gray-300 text-sm">
                    关注公众号「面包资料屋」获取更多资源
                  </p>
                </div>
                <div className="mt-3 p-3 bg-blue-800 rounded-lg">
                  <p className="text-white font-medium mb-1">💬 遇到问题？</p>
                  <p className="text-gray-300 text-sm">
                    添加微信 salute777888，及时获取技术支持和学习指导
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-gray-700 text-gray-400 text-sm text-center">
              <p>
                &copy; {new Date().getFullYear()} 考试真题资源平台 | 面包资料屋
                版权所有
              </p>
            </div>
          </div>
        </footer>
      </body>
    </html>
  );
}
