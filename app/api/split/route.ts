import { NextResponse } from "next/server";
import { DeepSeek } from "@/app/services/deepseek";

export async function POST(request: Request) {
  try {
    const { content } = await request.json();

    if (!content) {
      return NextResponse.json(
        { error: "Content is required" },
        { status: 400 }
      );
    }

    const prompt = `
你是一个专业的英语考试分析助手。请将以下英语文本按句子进行分割并标明段落和序号。

要求：
1. 每个句子必须是完整的语法单位
2. 保持句子的原始格式和标点符号
3. 返回一个 JSON 数组，数组中的每个元素是一个对象，包含以下属性：
   - content: 句子的完整内容
   - paragraph_num: 句子所在的段落编号（从1开始）
   - sequence: 句子在段落中的序号（从1开始）
4. 段落的划分依据是原文的段落结构，通常由空行或缩进表示
5. 对于完形填空题中的空格，保留原文中的数字标记
6. 确保返回的是有效的 JSON 格式

例如，输入：
"The weather was cold.

But we still went outside to play."

应该返回：
[
  {
    "content": "The weather was cold.",
    "paragraph_num": 1,
    "sequence": 1
  },
  {
    "content": "But we still went outside to play.",
    "paragraph_num": 2,
    "sequence": 1
  }
]

文本内容：
${content}

请直接返回 JSON 数组，不要包含任何其他说明文字。
`;

    const deepseek = new DeepSeek();
    const response = await deepseek.chat({
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
    });

    try {
      // 尝试解析 AI 返回的内容
      const parsedResponse = JSON.parse(
        response.choices[0].message.content.trim()
      );

      // 验证返回的是否为数组
      if (!Array.isArray(parsedResponse)) {
        throw new Error("Response is not an array");
      }

      // 确保每个元素都有正确的结构
      const sentences = parsedResponse.map((item, index) => {
        if (typeof item === "string") {
          // 如果返回的是字符串数组，转换为对象格式
          return {
            content: item,
            paragraph_num: 1,
            sequence: index + 1,
          };
        } else if (typeof item === "object" && item !== null) {
          // 确保对象有必要的属性
          return {
            content: item.content || "",
            paragraph_num: item.paragraph_num || 1,
            sequence: item.sequence || index + 1,
          };
        } else {
          // 无效项
          return {
            content: "",
            paragraph_num: 1,
            sequence: index + 1,
          };
        }
      });

      return NextResponse.json({ sentences });
    } catch (parseError) {
      console.error("Failed to parse AI response:", parseError);
      console.log("AI response:", response.choices[0].message.content);

      // 如果解析失败，尝试简单的分割方法
      const fallbackSentences = content
        .split(/(?<=[.!?])\s+/)
        .filter((s: string) => s.trim().length > 0)
        .map((s: string, index: number) => ({
          content: s,
          paragraph_num: 1,
          sequence: index + 1,
        }));

      return NextResponse.json({
        sentences: fallbackSentences,
        warning: "Used fallback sentence splitting due to AI parsing error",
      });
    }
  } catch (error: unknown) {
    console.error("Split text failed:", error);
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    return NextResponse.json(
      { error: "Failed to split text", details: errorMessage },
      { status: 500 }
    );
  }
}
