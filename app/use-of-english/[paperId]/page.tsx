import { Metadata } from "next";
import { UseClozeTestExam } from "./components/cloze-test-exam";

export const metadata: Metadata = {
  title: "考研英语完形填空真题练习",
  description:
    "使用mbdata在线练习考研英语真题完形填空，同步显示原文、题目和解析",
};

interface PageProps {
  params: {
    paperId: string;
  };
}

export default function UseOfEnglishPage({ params }: PageProps) {
  return (
    <div className="container mx-auto px-4 py-8">
      <UseClozeTestExam paperId={parseInt(params.paperId)} />
    </div>
  );
}
