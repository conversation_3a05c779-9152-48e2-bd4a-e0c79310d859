"use client";

import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import {
  Download,
  ArrowLeft,
  FileText,
  Calendar,
  Archive,
  Info,
  CheckCircle,
  LogIn,
  Home,
  GraduationCap,
} from "lucide-react";
import { Resource } from "@/app/lib/learning-resources-config";

// 文件类型图标组件
const PdfIcon = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="text-red-500"
  >
    <path
      d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="#FEEFEE"
    />
    <path
      d="M14 2V8H20"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path d="M11.5 12.5H12.5V16.5H11.5V12.5Z" fill="currentColor" />
    <path
      d="M9 13C9 12.7239 9.22386 12.5 9.5 12.5H10.5C10.7761 12.5 11 12.7239 11 13V16C11 16.2761 10.7761 16.5 10.5 16.5H9.5C9.22386 16.5 9 16.2761 9 16V13Z"
      fill="currentColor"
    />
    <path
      d="M13 13C13 12.7239 13.2239 12.5 13.5 12.5H14.5C14.7761 12.5 15 12.7239 15 13V16C15 16.2761 14.7761 16.5 14.5 16.5H13.5C13.2239 16.5 13 16.2761 13 16V13Z"
      fill="currentColor"
    />
    <path
      d="M7 16.5H17"
      stroke="currentColor"
      strokeWidth="1"
      strokeLinecap="round"
    />
  </svg>
);

// 通用文件图标
function getFileIcon(type: string) {
  if (type.includes("pdf")) return <PdfIcon />;
  return <FileText className="w-8 h-8 text-gray-500" />;
}

// 格式化文件大小
function formatFileSize(bytes: number) {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

// 获取用户友好的文件类型名称
function getReadableFileType(mimeType: string): string {
  const type = mimeType.toLowerCase();
  if (type.includes("pdf")) return "PDF文档";
  if (type.includes("word") || type.includes("doc")) return "Word文档";
  if (type.includes("sheet") || type.includes("excel") || type.includes("xls"))
    return "Excel表格";
  if (
    type.includes("presentation") ||
    type.includes("powerpoint") ||
    type.includes("ppt")
  )
    return "PPT演示文稿";
  if (type.includes("image")) return "图片文件";
  if (type.includes("text")) return "文本文件";
  if (
    type.includes("audio") ||
    type.includes("mp3") ||
    type.includes("wav") ||
    type.includes("m4a") ||
    type.includes("aac")
  )
    return "音频文件";
  return "文档文件";
}

// 获取教资分类样式
function getTeacherCategoryStyles(category: string) {
  switch (category) {
    case "科目一（综合素质）":
      return "bg-blue-50 text-blue-600 border border-blue-100";
    case "科目二（保教知识与能力）":
    case "科目二（教育教学知识与能力）":
    case "科目二（教育知识与能力）":
      return "bg-green-50 text-green-600 border border-green-100";
    case "科目三":
      return "bg-purple-50 text-purple-600 border border-purple-100";
    default:
      return "bg-gray-50 text-gray-600 border border-gray-100";
  }
}

// 获取教资部分样式
function getTeacherSectionStyles(section: string) {
  switch (section) {
    case "历年真题":
      return "bg-orange-50 text-orange-600 border border-orange-100";
    case "答案解析":
      return "bg-yellow-50 text-yellow-700 border border-yellow-100";
    default:
      return "bg-gray-50 text-gray-600 border border-gray-100";
  }
}

// 订阅信息接口定义
interface SubscriptionInfo {
  isActive?: boolean;
  type?: string;
  endDate?: string;
  isSubscribed?: boolean;
  isLifetimeMember?: boolean;
}

// 获取教资资源描述
function getTeacherResourceDescription(resource: Resource, pathSegments: string[]) {
  const lastSection = pathSegments[pathSegments.length - 2]; // 倒数第二个是section（历年真题/答案解析）
  const subject = pathSegments.length > 3 ? pathSegments[pathSegments.length - 3] : ""; // 学科名称

  if (lastSection === "历年真题") {
    return (
      <div className="space-y-2">
        <p className="text-gray-700">
          <strong>📋 真题说明：</strong>本资源为教师资格证
          {pathSegments.length > 2 ? `${pathSegments[0]}${subject ? ` ${subject}` : ""}` : pathSegments[0]}
          历年真题，包含完整的试题内容，是备考教资的核心资料。
        </p>
        <p className="text-gray-600 text-sm">
          ✅ 教资真题是了解考试题型、把握出题规律、提高应试能力的最佳途径。通过练习历年真题可以熟悉考试形式和难度。
        </p>
      </div>
    );
  } else if (lastSection === "答案解析") {
    return (
      <div className="space-y-2">
        <p className="text-gray-700">
          <strong>📝 解析说明：</strong>本资源为教师资格证
          {pathSegments.length > 2 ? `${pathSegments[0]}${subject ? ` ${subject}` : ""}` : pathSegments[0]}
          真题的详细答案解析，提供解题思路和知识点分析。
        </p>
        <p className="text-gray-600 text-sm">
          ✅ 答案解析不仅给出正确答案，更重要的是解题方法和思路，帮助理解教育学、心理学等核心知识点。
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <p className="text-gray-700">
        <strong>📄 资源说明：</strong>本资源为教师资格证相关学习材料，为教资考试备考提供支持。
      </p>
      <p className="text-gray-600 text-sm">
        ✅ 系统性学习配合针对性练习，是通过教师资格证考试的有效途径。
      </p>
    </div>
  );
}

// 获取教资使用建议
function getTeacherUsageTips(pathSegments: string[]) {
  const lastSection = pathSegments[pathSegments.length - 2]; // 倒数第二个是section

  if (lastSection === "历年真题") {
    return (
      <div className="space-y-3">
        <h4 className="font-medium text-blue-800">📌 使用建议：</h4>
        <ul className="space-y-1 text-sm text-blue-700">
          <li>• 建议按时间顺序从近年开始练习，把握最新考试趋势</li>
          <li>• 严格按照考试时间限制完成，培养时间观念</li>
          <li>• 练习后要认真对答案，分析错误原因</li>
          <li>• 建议配合答案解析一起使用，效果更佳</li>
          <li>• 重点关注教育法律法规、职业理念等高频考点</li>
        </ul>
      </div>
    );
  } else if (lastSection === "答案解析") {
    return (
      <div className="space-y-3">
        <h4 className="font-medium text-blue-800">📌 使用建议：</h4>
        <ul className="space-y-1 text-sm text-blue-700">
          <li>• 先独立完成题目，再查看解析</li>
          <li>• 重点关注解题思路和方法</li>
          <li>• 标记易错点，定期回顾</li>
          <li>• 总结同类型题目的解题规律</li>
          <li>• 重点掌握教育学、心理学核心理论</li>
        </ul>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <h4 className="font-medium text-blue-800">📌 使用建议：</h4>
      <ul className="space-y-1 text-sm text-blue-700">
        <li>• 结合自身学习计划合理安排使用时间</li>
        <li>• 配合其他学习资料综合提高</li>
        <li>• 定期检测学习效果，调整学习策略</li>
        <li>• 坚持练习，循序渐进</li>
      </ul>
    </div>
  );
}

export default function TeacherResourceDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [resource, setResource] = useState<Resource | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [downloadCount, setDownloadCount] = useState<number>(0);
  const [maxDownloads, setMaxDownloads] = useState<number>(3);
  const [subscriptionInfo, setSubscriptionInfo] =
    useState<SubscriptionInfo | null>(null);
  const [downloadMessage, setDownloadMessage] = useState<string | null>(null);

  const { type, path } = params;
  
  // 解析路径参数
  const pathSegments = Array.isArray(path) ? path : [path];
  const resourceId = pathSegments[pathSegments.length - 1]; // 最后一个是资源ID
  const nestedPath = pathSegments.slice(0, -1); // 除了最后一个ID，其他都是路径

  // 获取订阅信息
  useEffect(() => {
    const fetchSubscriptionInfo = async () => {
      try {
        const response = await fetch("/api/subscription");
        if (response.status === 401) {
          setSubscriptionInfo(null);
          return;
        }
        if (!response.ok) return;

        const data = await response.json();
        if (data.subscriptionInfo) {
          setSubscriptionInfo(data.subscriptionInfo);
        } else if (data.success && data.data) {
          setSubscriptionInfo(data.data);
        } else {
          setSubscriptionInfo({ isActive: false });
        }
      } catch (error) {
        console.error("获取订阅信息错误:", error);
        setSubscriptionInfo({ isActive: false });
      }
    };

    fetchSubscriptionInfo();
  }, []);

  // 获取下载次数
  useEffect(() => {
    const fetchDownloadCount = async () => {
      try {
        const response = await fetch("/api/download-count");
        if (!response.ok) return;

        const data = await response.json();
        setDownloadCount(data.currentDownloads || 0);
        setMaxDownloads(data.maxDownloads || 3);
      } catch (error) {
        console.error("获取下载次数出错:", error);
      }
    };

    fetchDownloadCount();
  }, []);

  // 获取资源详情
  useEffect(() => {
    const fetchResource = async () => {
      try {
        setLoading(true);
        
        // 构建嵌套路径参数
        const nestedPathParam = nestedPath.map(decodeURIComponent).join("|");
        
        // 使用教资专用的API端点
        const response = await fetch(
          `/api/learning-resources/${resourceId}?type=${type}&nestedPath=${encodeURIComponent(nestedPathParam)}`
        );

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error("资源不存在");
          }
          throw new Error(`获取资源失败: ${response.status}`);
        }

        const data = await response.json();
        setResource(data.resource);
        setError(null);
      } catch (err) {
        console.error("获取资源详情出错:", err);
        setError(
          err instanceof Error ? err.message : "加载资源失败，请稍后再试"
        );
      } finally {
        setLoading(false);
      }
    };

    if (type && pathSegments.length > 0 && resourceId) {
      fetchResource();
    }
  }, [type, pathSegments, resourceId, nestedPath]);

  // 处理下载
  const handleDownload = async () => {
    if (!resource) return;

    const isSubscribed =
      subscriptionInfo?.isSubscribed ||
      subscriptionInfo?.isLifetimeMember ||
      subscriptionInfo?.isActive ||
      false;
    const isLoggedIn = subscriptionInfo !== null;

    // 检查下载权限
    if (!isSubscribed && downloadCount >= maxDownloads) {
      if (isLoggedIn) {
        router.push("/pricing");
        return;
      } else {
        router.push("/sign-in?redirectTo=" + encodeURIComponent("/pricing"));
        return;
      }
    }

    setDownloadMessage("正在准备下载...");
    setTimeout(() => setDownloadMessage(null), 3000);

    try {
      const link = document.createElement("a");
      link.href = resource.url;
      link.setAttribute("download", resource.name);
      link.setAttribute("target", "_blank");
      document.body.appendChild(link);
      link.click();
      setTimeout(() => document.body.removeChild(link), 100);

      // 更新下载计数
      if (!isSubscribed) {
        await fetch("/api/download-count", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
        });
        setDownloadCount((prev) => prev + 1);
      }
    } catch (error) {
      console.error("下载文件失败:", error);
      window.location.href = resource.url;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-50 py-10">
        <div className="container mx-auto px-4">
          <div className="text-center py-20">
            <div className="text-7xl mb-4">⏳</div>
            <h3 className="text-xl font-medium text-gray-800 mb-2">
              加载中...
            </h3>
            <p className="text-gray-600">正在获取教资资源详情，请稍候</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !resource) {
    return (
      <div className="min-h-screen bg-slate-50 py-10">
        <div className="container mx-auto px-4">
          <div className="text-center py-20">
            <div className="text-7xl mb-4">😞</div>
            <h3 className="text-xl font-medium text-gray-800 mb-2">
              资源不存在
            </h3>
            <p className="text-gray-600 mb-6">{error || "请求的资源未找到"}</p>
            <Link
              href="/learning-resources"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回资源库
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* SEO Meta Tags */}
      <head>
        <title>
          {resource.name} - 教师资格证 - 面包资料屋
        </title>
        <meta
          name="description"
          content={`免费下载教师资格证资料《${resource.name}》，文件大小${formatFileSize(resource.size)}，${getReadableFileType(resource.type)}格式。面包资料屋提供教资考试优质学习资源。`}
        />
      </head>

      <div className="min-h-screen bg-slate-50 py-10">
        {/* 下载提示信息 */}
        {downloadMessage && (
          <div className="fixed top-5 left-1/2 transform -translate-x-1/2 z-50 bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded shadow-lg">
            <div className="flex items-center">
              <Info className="w-5 h-5 mr-2" />
              <p>{downloadMessage}</p>
            </div>
          </div>
        )}

        <div className="container mx-auto px-4">
          {/* 面包屑导航 */}
          <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-8">
            <Link href="/" className="hover:text-blue-600 transition-colors">
              <Home className="w-4 h-4" />
            </Link>
            <span>/</span>
            <Link
              href="/learning-resources"
              className="hover:text-blue-600 transition-colors"
            >
              资源库
            </Link>
            <span>/</span>
            <span className="hover:text-blue-600 transition-colors">
              教师资格证
            </span>
            {nestedPath.map((segment, index) => (
              <span key={index}>
                <span>/</span>
                <span className="text-gray-700">{decodeURIComponent(segment)}</span>
              </span>
            ))}
            <span>/</span>
            <span className="text-gray-900 font-medium truncate max-w-xs">
              {resource.name}
            </span>
          </nav>

          {/* 返回按钮 */}
          <div className="mb-6">
            <Link
              href="/learning-resources"
              className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              返回资源库
            </Link>
          </div>

          {/* 主要内容 */}
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="p-8">
              {/* 资源标题和基本信息 */}
              <div className="flex flex-col lg:flex-row lg:items-start gap-8">
                {/* 文件图标 */}
                <div className="flex-shrink-0">
                  <div className="w-24 h-24 bg-gray-50 rounded-lg flex items-center justify-center">
                    {getFileIcon(resource.type)}
                  </div>
                </div>

                {/* 资源信息 */}
                <div className="flex-1 min-w-0">
                  <h1 className="text-3xl font-bold text-gray-900 mb-4 break-words">
                    {resource.name}
                  </h1>

                  {/* 标签 */}
                  <div className="flex flex-wrap gap-3 mb-6">
                    <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-indigo-50 text-indigo-600 border border-indigo-100">
                      <GraduationCap className="w-4 h-4 mr-1" />
                      教师资格证
                    </span>
                    {nestedPath.map((segment, index) => (
                      <span
                        key={index}
                        className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${
                          index === nestedPath.length - 1 
                            ? getTeacherSectionStyles(decodeURIComponent(segment))
                            : getTeacherCategoryStyles(decodeURIComponent(segment))
                        }`}
                      >
                        {decodeURIComponent(segment)}
                      </span>
                    ))}
                  </div>

                  {/* 文件详情 */}
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-8">
                    <div className="flex items-center">
                      <FileText className="w-5 h-5 text-gray-400 mr-3" />
                      <div>
                        <div className="text-sm text-gray-500">文件类型</div>
                        <div className="font-medium">
                          {getReadableFileType(resource.type)}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <Archive className="w-5 h-5 text-gray-400 mr-3" />
                      <div>
                        <div className="text-sm text-gray-500">文件大小</div>
                        <div className="font-medium">
                          {formatFileSize(resource.size)}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <Calendar className="w-5 h-5 text-gray-400 mr-3" />
                      <div>
                        <div className="text-sm text-gray-500">上传时间</div>
                        <div className="font-medium">
                          {new Date(resource.created_at).toLocaleDateString(
                            "zh-CN"
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 资源描述与使用指南 */}
                  <div className="mb-8">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      资源说明
                    </h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      {getTeacherResourceDescription(resource, nestedPath)}
                    </div>
                  </div>

                  {/* 使用建议 */}
                  <div className="mb-8">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      使用建议
                    </h3>
                    <div className="bg-blue-50 rounded-lg p-4">
                      {getTeacherUsageTips(nestedPath)}
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex flex-col sm:flex-row gap-4">
                    <button
                      onClick={handleDownload}
                      className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors font-medium"
                    >
                      <Download className="w-5 h-5 mr-2" />
                      立即下载
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* 下载权限说明 */}
            <div className="border-t border-gray-200 p-8 bg-gray-50">
              {subscriptionInfo?.isSubscribed ||
              subscriptionInfo?.isLifetimeMember ||
              subscriptionInfo?.isActive ? (
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-6 h-6 text-green-600 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="font-medium text-green-800 mb-1">
                      订阅用户专享
                    </h3>
                    <p className="text-sm text-green-700">
                      您已解锁无限下载权限，可以自由下载所有教资学习资源。感谢您的支持！
                    </p>
                  </div>
                </div>
              ) : (
                <div className="flex items-start gap-3">
                  <Info className="w-6 h-6 text-blue-600 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="font-medium text-blue-800 mb-1">
                      下载权限说明
                    </h3>
                    <p className="text-sm text-blue-700 mb-3">
                      您当前可以免费下载 {maxDownloads} 个学习资源，已下载{" "}
                      {downloadCount} 个。 订阅后可无限制下载所有资源。
                    </p>
                    <div className="flex gap-3">
                      {subscriptionInfo !== null ? (
                        <Link
                          href="/pricing"
                          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                        >
                          <CheckCircle className="w-4 h-4 mr-1" />
                          立即订阅
                        </Link>
                      ) : (
                        <Link
                          href="/sign-in"
                          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                        >
                          <LogIn className="w-4 h-4 mr-1" />
                          登录账号
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 相关推荐 */}
          <div className="mt-12 bg-blue-50 border border-blue-100 rounded-xl p-6 text-center">
            <h3 className="text-2xl font-semibold text-blue-800 mb-4">
              想要更多教资备考资料？
            </h3>
            <p className="text-blue-700 mb-6">
              关注公众号「面包资料屋」获取更多独家教师资格证考试资料和学习方法。
            </p>
            <div className="inline-flex items-center justify-center px-5 py-3 bg-blue-600 text-white rounded-lg text-lg">
              <span className="mr-2">📱</span>
              公众号：面包资料屋
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
