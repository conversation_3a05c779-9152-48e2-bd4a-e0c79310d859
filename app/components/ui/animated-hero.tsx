"use client";

import { useEffect, useMemo, useState } from "react";
import { motion } from "framer-motion";
import { MoveRight, BookOpen } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";

function AnimatedHero() {
  const [titleNumber, setTitleNumber] = useState(0);
  const titles = useMemo(() => ["高效", "智能", "系统", "专业", "全面"], []);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (titleNumber === titles.length - 1) {
        setTitleNumber(0);
      } else {
        setTitleNumber(titleNumber + 1);
      }
    }, 2000);
    return () => clearTimeout(timeoutId);
  }, [titleNumber, titles]);

  return (
    <div className="w-full bg-gradient-to-r from-blue-50 via-blue-100 to-indigo-100 text-gray-800">
      <div className="container mx-auto">
        <div className="flex gap-8 py-16 md:py-24 items-center justify-center flex-col">
          <div>
            <Button
              variant="secondary"
              size="sm"
              className="gap-4 bg-blue-600 text-white hover:bg-blue-700"
              asChild
            >
              <Link href="/learning-resources">
                查看最新真题资源 <MoveRight className="w-4 h-4" />
              </Link>
            </Button>
          </div>
          <div className="flex gap-4 flex-col">
            <h1 className="text-5xl md:text-7xl max-w-2xl tracking-tighter text-center font-regular">
              <span className="text-blue-800">考试真题资源</span>
              <span className="relative flex w-full justify-center overflow-hidden text-center md:pb-4 md:pt-1">
                &nbsp;
                {titles.map((title, index) => (
                  <motion.span
                    key={index}
                    className="absolute font-semibold text-blue-600"
                    initial={{ opacity: 0, y: "-100" }}
                    transition={{ type: "spring", stiffness: 50 }}
                    animate={
                      titleNumber === index
                        ? {
                            y: 0,
                            opacity: 1,
                          }
                        : {
                            y: titleNumber > index ? -150 : 150,
                            opacity: 0,
                          }
                    }
                  >
                    {title}
                  </motion.span>
                ))}
              </span>
            </h1>

            <p className="text-lg md:text-xl leading-relaxed tracking-tight text-gray-600 max-w-2xl text-center">
              考研英语、政治、数学、专业课以及四六级、专四专八等考试备考不再困难。我们提供历年真题解析和专项训练，
              帮助您系统备考，高效提分，轻松应对各类考试挑战。
              <span className="text-blue-700 font-medium">
                关注公众号「面包资料屋」获取更多备考资源！
              </span>
            </p>
          </div>
          <div className="flex flex-row gap-3">
            <Button
              size="lg"
              className="gap-4 bg-white text-blue-700 hover:bg-gray-100 border-blue-200"
              variant="outline"
              asChild
            >
              <Link href="/learning-resources">
                浏览真题库 <BookOpen className="w-4 h-4" />
              </Link>
            </Button>
            <Button
              size="lg"
              className="gap-4 bg-blue-600 text-white hover:bg-blue-700"
              asChild
            >
              <Link href="/learning-resources">
                立即开始学习 <MoveRight className="w-4 h-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export { AnimatedHero };
