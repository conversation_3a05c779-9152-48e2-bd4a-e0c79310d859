import { NextRequest } from "next/server";
import {
  streamChatWithDeepSeek,
  getEnglishLearningSystemPrompt,
} from "@/app/lib/deepseek-api";
import { ChatCompletionMessageParam } from "openai/resources/chat/completions";
import { Stream } from "openai/streaming";

// POST /api/chat
export async function POST(req: NextRequest) {
  try {
    // 检查环境变量
    const apiKey = process.env.DEEPSEEK_API_KEY;
    if (!apiKey) {
      console.error("未设置 DEEPSEEK_API_KEY 环境变量");
      return new Response(
        JSON.stringify({
          error: "服务器配置错误: 未设置 API 密钥",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    const body = await req.json();
    const { messages } = body;

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return new Response(
        JSON.stringify({
          error: "请提供有效的消息内容",
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    // 添加日志
    console.log(`处理聊天请求，消息数量: ${messages.length}`);

    try {
      // 获取系统提示
      const systemPrompt = await getEnglishLearningSystemPrompt();

      // 使用流式 API 响应
      const stream = await streamChatWithDeepSeek(
        messages as ChatCompletionMessageParam[],
        systemPrompt
      );

      // 创建一个新的 ReadableStream
      const textEncoder = new TextEncoder();
      const readable = new ReadableStream({
        async start(controller) {
          // 正确处理 OpenAI 的流响应
          if (stream instanceof Stream) {
            for await (const chunk of stream) {
              // 检查是否有增量内容
              if (chunk.choices && chunk.choices[0]?.delta?.content) {
                // 格式化为 SSE 格式
                const text = `data: ${JSON.stringify(chunk)}\n\n`;
                controller.enqueue(textEncoder.encode(text));
              }
            }
            // 流结束
            controller.enqueue(textEncoder.encode("data: [DONE]\n\n"));
            controller.close();
          } else {
            // 处理非预期的响应格式
            controller.enqueue(
              textEncoder.encode(
                `data: ${JSON.stringify({ error: "无效的流响应" })}\n\n`
              )
            );
            controller.enqueue(textEncoder.encode("data: [DONE]\n\n"));
            controller.close();
          }
        },
        cancel() {
          // 流被取消（例如客户端断开连接）
          console.log("流被取消");
        },
      });

      return new Response(readable, {
        headers: {
          "Content-Type": "text/event-stream",
          "Cache-Control": "no-cache",
          Connection: "keep-alive",
        },
      });
    } catch (streamError: any) {
      console.error("DeepSeek API 调用失败:", streamError);
      return new Response(
        JSON.stringify({
          error: `调用 DeepSeek API 失败: ${streamError.message || "未知错误"}`,
        }),
        {
          status: 502,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  } catch (error: any) {
    console.error("Chat API 处理错误:", error);
    return new Response(
      JSON.stringify({
        error: `处理请求出错: ${error.message || "未知错误"}`,
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
