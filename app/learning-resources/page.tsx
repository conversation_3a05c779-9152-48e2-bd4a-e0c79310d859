"use client";

import { useEffect, useState, useRef, useCallback } from "react";
import {
  Resource,
  RESOURCE_TYPE_CONFIGS,
  getResourceTypeConfig,
  getApiResourceTypes,
  getSpecialResourceTypes,
  ResourceTypeConfig,
  getCategoryIcon,
  getSectionIcon,
  getCategoryStyles,
  getSectionStyles,
  TEACHER_RESOURCE_CONFIGS,
  getNestedKeys,
  getNestedPath,
  isLeafNode,
  getBreadcrumbPath,
  getDefaultPath,
  NestedSectionConfig,
} from "@/app/lib/learning-resources-config";
import { Resource as InnovationResource } from "../api/innovation-entrepreneurship/route";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Head from "next/head";
import {
  Info,
  LogIn,
  CheckCircle,
  Loader2,
  Download,
  FileText,
  FileImage,
  File,
} from "lucide-react";
import {
  innovationResourceTypes,
  getInnovationCategoryStyles,
  getInnovationTypeName,
  fetchInnovationResources,
} from "../lib/innovation-resources";

// 文件类型图标组件
const PdfIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="text-red-500"
  >
    <path
      d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="#FEEFEE"
    />
    <path
      d="M14 2V8H20"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path d="M11.5 12.5H12.5V16.5H11.5V12.5Z" fill="currentColor" />
    <path
      d="M9 13C9 12.7239 9.22386 12.5 9.5 12.5H10.5C10.7761 12.5 11 12.7239 11 13V16C11 16.2761 10.7761 16.5 10.5 16.5H9.5C9.22386 16.5 9 16.2761 9 16V13Z"
      fill="currentColor"
    />
    <path
      d="M13 13C13 12.7239 13.2239 12.5 13.5 12.5H14.5C14.7761 12.5 15 12.7239 15 13V16C15 16.2761 14.7761 16.5 14.5 16.5H13.5C13.2239 16.5 13 16.2761 13 16V13Z"
      fill="currentColor"
    />
    <path
      d="M7 16.5H17"
      stroke="currentColor"
      strokeWidth="1"
      strokeLinecap="round"
    />
  </svg>
);

const DocIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="text-blue-600"
  >
    <path
      d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="#EFF6FF"
    />
    <path
      d="M14 2V8H20"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <rect x="8" y="14" width="8" height="1.5" rx="0.75" fill="currentColor" />
    <rect x="8" y="11" width="8" height="1.5" rx="0.75" fill="currentColor" />
    <rect x="8" y="17" width="8" height="1.5" rx="0.75" fill="currentColor" />
  </svg>
);

const XlsIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="text-green-600"
  >
    <path
      d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="#ECFDF5"
    />
    <path
      d="M14 2V8H20"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <rect
      x="6"
      y="13"
      width="12"
      height="7"
      rx="1"
      fill="currentColor"
      fillOpacity="0.2"
    />
    <path d="M12 13V20" stroke="currentColor" strokeLinecap="round" />
    <path d="M6 16.5H18" stroke="currentColor" strokeLinecap="round" />
  </svg>
);

const PptIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="text-orange-500"
  >
    <path
      d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="#FFF7ED"
    />
    <path
      d="M14 2V8H20"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <rect
      x="8"
      y="12"
      width="8"
      height="6"
      rx="1"
      fill="currentColor"
      fillOpacity="0.2"
    />
    <circle cx="12" cy="10" r="2" fill="currentColor" fillOpacity="0.5" />
  </svg>
);

// 新增音频文件图标
const AudioIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="text-purple-600"
  >
    <path
      d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="#F3E8FF"
    />
    <path
      d="M14 2V8H20"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <circle cx="12" cy="13" r="2" fill="currentColor" />
    <path
      d="M12 11V13L14 12.5"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8 15.5C8.5 16 9.5 16.5 10.5 16.5C11.5 16.5 12.5 16 13 15.5"
      stroke="currentColor"
      strokeWidth="1"
      strokeLinecap="round"
    />
  </svg>
);

// 通过文件名获取文件类型图标
function getFileIcon(type: string) {
  if (type.includes("pdf")) return <PdfIcon />;
  if (type.includes("word") || type.includes("doc")) return <DocIcon />;
  if (type.includes("excel") || type.includes("sheet") || type.includes("xls"))
    return <XlsIcon />;
  if (
    type.includes("powerpoint") ||
    type.includes("presentation") ||
    type.includes("ppt")
  )
    return <PptIcon />;
  if (type.includes("image"))
    return <FileImage className="w-6 h-6 text-gray-500" />;
  if (type.includes("text"))
    return <FileText className="w-6 h-6 text-gray-500" />;
  if (
    type.includes("audio") ||
    type.includes("mp3") ||
    type.includes("wav") ||
    type.includes("m4a") ||
    type.includes("aac")
  )
    return <AudioIcon />;
  if (
    type.includes("zip") ||
    type.includes("rar") ||
    type.includes("compressed")
  )
    return <File className="w-6 h-6 text-gray-500" />;

  // 提取扩展名作为后备显示方式
  const extension = type.split("/").pop()?.split(".").pop();
  if (extension && extension.length < 6)
    return <File className="w-6 h-6 text-gray-500" />;

  return <File className="w-6 h-6 text-gray-500" />;
}

// 格式化文件大小
function formatFileSize(bytes: number) {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

// 获取部分样式

// 使用配置文件中的资源类型定义
const resourceTypes = RESOURCE_TYPE_CONFIGS;

// 订阅信息接口定义
interface SubscriptionInfo {
  isActive?: boolean;
  type?: string;
  endDate?: string;
  isSubscribed?: boolean;
  isLifetimeMember?: boolean;
}

// 获取用户友好的文件类型名称
function getReadableFileType(mimeType: string): string {
  const type = mimeType.toLowerCase();

  if (type.includes("pdf")) return "PDF文档";
  if (type.includes("word") || type.includes("doc")) return "Word文档";
  if (type.includes("sheet") || type.includes("excel") || type.includes("xls"))
    return "Excel表格";
  if (
    type.includes("presentation") ||
    type.includes("powerpoint") ||
    type.includes("ppt")
  )
    return "PPT演示文稿";
  if (type.includes("image")) return "图片文件";
  if (type.includes("text")) return "文本文件";
  if (
    type.includes("audio") ||
    type.includes("mp3") ||
    type.includes("wav") ||
    type.includes("m4a") ||
    type.includes("aac")
  )
    return "音频文件";
  if (
    type.includes("zip") ||
    type.includes("rar") ||
    type.includes("compressed")
  )
    return "压缩文件";

  // 提取扩展名作为后备显示方式
  const extension = mimeType.split("/").pop()?.split(".").pop();
  if (extension && extension.length < 6)
    return extension.toUpperCase() + "文件";

  return "文档文件";
}

// 生成考试资源详情页面的URL (考研、四六级等)
function getExamResourceDetailUrl(resource: Resource, type: string): string {
  // 对分类进行URL编码
  const encodedCategory = encodeURIComponent(resource.category || "");
  return `/learning-resources/exams/${type}/${encodedCategory}/${resource.id}`;
}

// 生成教资资源详情页面的URL
function getTeacherResourceDetailUrl(
  resource: Resource,
  type: string,
  teacherSelectionPath: string[]
): string {
  // 对分类进行URL编码
  const encodedCategory = encodeURIComponent(teacherSelectionPath[0] || "");
  const encodedSection = encodeURIComponent(teacherSelectionPath[1] || "");
  return `/learning-resources/teacher/${type}/${encodedCategory}/${encodedSection}/${resource.id}`;
}

// 生成大创资源详情页面的URL
function getInnovationResourceDetailUrl(
  resource: InnovationResource,
  type: string
): string {
  // 对分类进行URL编码
  const encodedCategory = encodeURIComponent(resource.category || "");
  return `/learning-resources/innovation/${type}/${encodedCategory}/${resource.id}`;
}

// 兼容性函数 - 根据当前显示的模块类型生成正确的URL
function getResourceDetailUrl(resource: Resource, type: string): string {
  // 对分类进行URL编码
  const encodedCategory = encodeURIComponent(resource.category || "");
  return `/learning-resources/exams/${type}/${encodedCategory}/${resource.id}`;
}

// 客户端组件不能使用metadata导出，改用动态head处理

export default function LearningResourcesPage() {
  const router = useRouter();
  const [resources, setResources] = useState<Resource[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedType, setSelectedType] = useState("kaoyan1"); // 默认为考研英语一
  const [filterSection, setFilterSection] = useState<string | null>("真题"); // 默认选择真题

  // 大创资源相关状态
  const [showInnovationResources, setShowInnovationResources] = useState(false);
  const [innovationResources, setInnovationResources] = useState<
    InnovationResource[]
  >([]);
  const [innovationLoading, setInnovationLoading] = useState(false);
  const [innovationError, setInnovationError] = useState<string | null>(null);
  const [selectedInnovationType, setSelectedInnovationType] =
    useState("recommended");

  // 教资资源相关状态 - 更新为支持嵌套选择
  const [showTeacherResources, setShowTeacherResources] = useState(false);
  const [teacherResources, setTeacherResources] = useState<Resource[]>([]);
  const [teacherLoading, setTeacherLoading] = useState(false);
  const [teacherError, setTeacherError] = useState<string | null>(null);
  const [selectedTeacherType, setSelectedTeacherType] =
    useState("teacher_secondary");

  // 嵌套选择状态
  const [teacherSelectionPath, setTeacherSelectionPath] = useState<string[]>(
    []
  );
  const [availableOptions, setAvailableOptions] = useState<string[]>([]);

  // 教资资源的section筛选状态
  const [teacherFilterSection, setTeacherFilterSection] = useState<
    string | null
  >(null);

  // 获取当前选择路径下的可用选项
  const getCurrentOptions = (
    nestedConfig: NestedSectionConfig,
    currentPath: string[]
  ): string[] => {
    let current: any = nestedConfig;

    for (const key of currentPath) {
      if (current && typeof current === "object" && key in current) {
        current = current[key];
      } else {
        return [];
      }
    }

    if (current && typeof current === "object") {
      return Object.keys(current);
    }

    return [];
  };

  // 检查当前路径是否可以获取资源（是叶子节点）
  const canFetchResources = (
    nestedConfig: NestedSectionConfig,
    currentPath: string[]
  ): boolean => {
    if (currentPath.length === 0) return false;
    return isLeafNode(nestedConfig, currentPath);
  };

  // 获取教资资源在叶子节点的可用sections
  const getTeacherLeafSections = (
    nestedConfig: NestedSectionConfig,
    currentPath: string[]
  ): string[] => {
    if (!canFetchResources(nestedConfig, currentPath)) return [];

    let current: any = nestedConfig;
    for (const key of currentPath) {
      if (current && typeof current === "object" && key in current) {
        current = current[key];
      } else {
        return [];
      }
    }

    if (typeof current === "object" && current !== null) {
      return Object.keys(current);
    }

    return [];
  };

  // 获取筛选后的教资资源
  const getFilteredTeacherResources = (): Resource[] => {
    if (!teacherFilterSection) return teacherResources;
    return teacherResources.filter(
      (resource) => resource.section === teacherFilterSection
    );
  };

  // 现有状态保持不变
  const [downloadCount, setDownloadCount] = useState<number>(0); // 实际下载次数
  const [maxDownloads, setMaxDownloads] = useState<number>(3); // 最大下载次数
  const [downloadMessage, setDownloadMessage] = useState<string | null>(null);
  const [subscriptionInfo, setSubscriptionInfo] =
    useState<SubscriptionInfo | null>(null);
  const [subscriptionLoading, setSubscriptionLoading] = useState(false);
  const [downloadCountLoading, setDownloadCountLoading] = useState(false); // 新增：下载计数加载状态
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const downloadFrameRef = useRef<HTMLIFrameElement>(null);
  const [isWeixinBrowser, setIsWeixinBrowser] = useState<boolean>(false);
  const [isSafariBrowser, setIsSafariBrowser] = useState<boolean>(false);

  // 检测浏览器环境
  useEffect(() => {
    const ua = navigator.userAgent.toLowerCase();
    // 检测微信
    const isWeixin = ua.indexOf("micromessenger") !== -1;
    setIsWeixinBrowser(isWeixin);

    // 检测Safari
    const isSafari = /^((?!chrome|android).)*safari/i.test(ua);
    setIsSafariBrowser(isSafari && !isWeixin); // 排除微信内的Safari
  }, []);

  // 初始化读取下载次数
  useEffect(() => {
    // 从API读取下载次数
    const fetchDownloadCount = async () => {
      try {
        setDownloadCountLoading(true);
        const response = await fetch("/api/download-count");

        if (!response.ok) {
          console.error("获取下载次数失败:", response.statusText);
          return;
        }

        const data = await response.json();
        setDownloadCount(data.currentDownloads || 0);
        setMaxDownloads(data.maxDownloads || 3);
      } catch (error) {
        console.error("获取下载次数出错:", error);
      } finally {
        setDownloadCountLoading(false);
      }
    };

    fetchDownloadCount();
  }, []);

  // 在组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // 更新下载次数的函数
  const updateDownloadCount = useCallback(async () => {
    try {
      const response = await fetch("/api/download-count", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        console.error("更新下载次数失败:", response.statusText);
        return;
      }

      const data = await response.json();
      setDownloadCount(data.currentDownloads || 0);
      setMaxDownloads(data.maxDownloads || 3);
    } catch (error) {
      console.error("更新下载次数出错:", error);
    }
  }, []);

  // 获取订阅信息的函数
  const fetchSubscriptionInfo = useCallback(async () => {
    try {
      setSubscriptionLoading(true);

      const response = await fetch("/api/subscription");

      // 如果是401错误，表示用户未登录，不显示错误提示
      if (response.status === 401) {
        // 未登录状态，设置subscriptionInfo为null
        setSubscriptionInfo(null);
        return false;
      }

      if (!response.ok) {
        return false;
      }

      const data = await response.json();

      // 适配API实际返回格式
      if (data.subscriptionInfo) {
        setSubscriptionInfo(data.subscriptionInfo);
        return data.subscriptionInfo.isActive;
      } else if (data.success && data.data) {
        // 兼容旧格式
        setSubscriptionInfo(data.data);
        return data.data.isSubscribed || data.data.isLifetimeMember;
      } else {
        // 如果无法获取有效的订阅信息，默认设置为未订阅
        setSubscriptionInfo({ isActive: false });
        return false;
      }
    } catch (error) {
      // 不向用户显示错误，只在控制台记录
      console.error("获取订阅信息错误:", error);
      // 默认为未订阅状态
      setSubscriptionInfo({ isActive: false });
      return false;
    } finally {
      setSubscriptionLoading(false);
    }
  }, []);

  // 在客户端获取资源
  useEffect(() => {
    const fetchResources = async () => {
      // 获取当前资源类型配置
      const currentConfig = getResourceTypeConfig(selectedType);

      // 如果不需要调用API（如打印类型），跳过API调用
      if (!currentConfig?.needsApi) {
        setResources([]);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        // 添加时间戳防止缓存
        const timestamp = new Date().getTime();
        let url = `/api/learning-resources?t=${timestamp}&type=${selectedType}`;

        // 如果有选择的section，添加到URL中
        if (filterSection) {
          url += `&section=${encodeURIComponent(filterSection)}`;
        }

        const response = await fetch(url, {
          cache: "no-store",
        });

        if (!response.ok) {
          throw new Error(`API请求失败: ${response.status}`);
        }

        const data = await response.json();
        setResources(data.resources || []);
        setError(null);
      } catch (err) {
        console.error("获取学习资源出错:", err);
        setError("加载资源失败，请稍后再试");
      } finally {
        setLoading(false);
      }
    };

    fetchResources();
  }, [selectedType, filterSection]); // 添加filterSection到依赖数组

  // 仅在组件首次挂载时获取订阅信息，与资源类型切换无关
  useEffect(() => {
    // 获取订阅信息
    fetchSubscriptionInfo();
  }, [fetchSubscriptionInfo]);

  // 获取大创资源
  useEffect(() => {
    if (showInnovationResources) {
      const getInnovationResources = async () => {
        try {
          setInnovationLoading(true);
          const resources = await fetchInnovationResources(
            selectedInnovationType
          );
          setInnovationResources(resources);
          setInnovationError(null);
        } catch (err) {
          console.error("获取创新创业资源出错:", err);
          setInnovationError("加载资源失败，请稍后再试");
        } finally {
          setInnovationLoading(false);
        }
      };

      getInnovationResources();
    }
  }, [showInnovationResources, selectedInnovationType]);

  // 获取教资资源
  useEffect(() => {
    if (showTeacherResources) {
      const fetchTeacherResources = async () => {
        const currentConfig = getTeacherResourceConfig(selectedTeacherType);

        if (!currentConfig?.needsApi || !currentConfig.nestedSections) {
          setTeacherResources([]);
          setTeacherFilterSection(null);
          setTeacherLoading(false);
          return;
        }

        // 如果没有选择完整路径，不获取资源
        if (
          !canFetchResources(currentConfig.nestedSections, teacherSelectionPath)
        ) {
          setTeacherResources([]);
          setTeacherFilterSection(null);
          setTeacherLoading(false);
          return;
        }

        try {
          setTeacherLoading(true);
          const timestamp = new Date().getTime();
          const nestedPathParam = teacherSelectionPath
            .map(encodeURIComponent)
            .join("|");
          const url = `/api/learning-resources?t=${timestamp}&type=${selectedTeacherType}&nestedPath=${nestedPathParam}`;

          const response = await fetch(url, {
            cache: "no-store",
          });

          if (!response.ok) {
            throw new Error(`API请求失败: ${response.status}`);
          }

          const data = await response.json();
          const resources = data.resources || [];
          setTeacherResources(resources);
          setTeacherError(null);

          // 设置默认筛选section为第一个可用的section
          if (resources.length > 0) {
            const availableSections = getTeacherLeafSections(
              currentConfig.nestedSections,
              teacherSelectionPath
            );
            if (availableSections.length > 0) {
              setTeacherFilterSection(availableSections[0]);
            } else {
              setTeacherFilterSection(null);
            }
          } else {
            setTeacherFilterSection(null);
          }
        } catch (err) {
          console.error("获取教资资源出错:", err);
          setTeacherError("加载资源失败，请稍后再试");
          setTeacherFilterSection(null);
        } finally {
          setTeacherLoading(false);
        }
      };

      fetchTeacherResources();
    }
  }, [showTeacherResources, selectedTeacherType, teacherSelectionPath]);

  // 切换资源类型处理函数
  const toggleResourceType = (type: "exam" | "teacher" | "innovation") => {
    setShowInnovationResources(type === "innovation");
    setShowTeacherResources(type === "teacher");

    // 切换到对应默认分类
    if (type === "innovation") {
      setSelectedInnovationType("recommended");
    } else if (type === "teacher") {
      setSelectedTeacherType("teacher_secondary");
      setTeacherFilterSection(null);

      // 获取中学教资的配置并设置默认路径
      const config = getTeacherResourceConfig("teacher_secondary");
      if (config?.nestedSections) {
        // 自动选择默认路径
        const defaultPath = getDefaultPath(config.nestedSections);
        setTeacherSelectionPath(defaultPath);

        const firstLevelOptions = getCurrentOptions(config.nestedSections, []);
        setAvailableOptions(firstLevelOptions);
      } else {
        setTeacherSelectionPath([]);
        setAvailableOptions([]);
      }
    } else {
      setSelectedType("kaoyan1");
      setFilterSection("真题");
    }
  };

  // 处理教资资源类型切换
  const handleTeacherTypeChange = (type: string) => {
    setSelectedTeacherType(type);
    setTeacherFilterSection(null); // 重置筛选

    // 获取新类型的配置并设置默认路径
    const config = getTeacherResourceConfig(type);
    if (config?.nestedSections) {
      // 自动选择默认路径
      const defaultPath = getDefaultPath(config.nestedSections);
      setTeacherSelectionPath(defaultPath);

      // 获取当前层级的选项
      const firstLevelOptions = getCurrentOptions(config.nestedSections, []);
      setAvailableOptions(firstLevelOptions);
    } else {
      setTeacherSelectionPath([]);
      setAvailableOptions([]);
    }
  };

  // 处理嵌套选择
  const handleNestedSelection = (option: string, level: number) => {
    const config = getTeacherResourceConfig(selectedTeacherType);
    if (!config?.nestedSections) return;

    // 更新选择路径
    const newPath = [...teacherSelectionPath.slice(0, level), option];

    // 检查是否还有下一级，如果有则自动选择默认路径
    let finalPath = newPath;
    let current: any = config.nestedSections;

    // 导航到当前路径
    for (const key of newPath) {
      if (current && typeof current === "object" && key in current) {
        current = current[key];
      } else {
        break;
      }
    }

    // 如果当前不是叶子节点，继续自动选择默认路径
    if (
      current &&
      typeof current === "object" &&
      Object.keys(current).length > 0
    ) {
      let tempCurrent = current;
      while (tempCurrent && typeof tempCurrent === "object") {
        const keys = Object.keys(tempCurrent);
        if (keys.length === 0) break;

        const firstKey = keys[0];
        finalPath.push(firstKey);
        tempCurrent = tempCurrent[firstKey];

        // 如果到达叶子节点（值是字符串），停止
        if (typeof tempCurrent === "string") {
          break;
        }
      }
    }

    setTeacherSelectionPath(finalPath);
    setTeacherFilterSection(null); // 重置筛选

    // 获取下一级选项
    const nextLevelOptions = getCurrentOptions(config.nestedSections, newPath);
    setAvailableOptions(nextLevelOptions);
  };

  // 返回上一级
  const goBackLevel = () => {
    if (teacherSelectionPath.length === 0) return;

    const config = getTeacherResourceConfig(selectedTeacherType);
    if (!config?.nestedSections) return;

    const newPath = teacherSelectionPath.slice(0, -1);
    setTeacherSelectionPath(newPath);
    setTeacherFilterSection(null); // 重置筛选

    // 获取当前级的选项
    const currentOptions = getCurrentOptions(config.nestedSections, newPath);
    setAvailableOptions(currentOptions);
  };

  // 获取教资资源类型配置的辅助函数
  const getTeacherResourceConfig = (
    id: string
  ): ResourceTypeConfig | undefined => {
    return TEACHER_RESOURCE_CONFIGS.find((config) => config.id === id);
  };

  // 获取当前教资类型的可用sections
  const getAvailableTeacherSections = (type: string): string[] => {
    const config = getTeacherResourceConfig(type);
    return config?.sections || [];
  };

  // 处理大创资源类型切换
  const handleInnovationTypeChange = (type: string) => {
    setSelectedInnovationType(type);
  };

  // 处理下载
  const handleDownload = async (resource: Resource) => {
    // 使用已缓存的订阅信息，无需重复调用API
    const isSubscribed =
      subscriptionInfo?.isSubscribed ||
      subscriptionInfo?.isLifetimeMember ||
      subscriptionInfo?.isActive ||
      false;

    // 微信浏览器特殊处理
    if (isWeixinBrowser) {
      setDownloadMessage(
        "微信内无法直接下载，请点击右上角菜单，选择在浏览器打开后下载"
      );
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      timeoutRef.current = setTimeout(() => {
        setDownloadMessage(null);
      }, 5000);
      return;
    }

    // 判断是否已登录（通过检查subscriptionInfo是否为null，如果为null表示未登录或获取失败）
    const isLoggedIn = subscriptionInfo !== null;

    // 先检查是否有下载权限 - 非订阅用户需要检查下载次数限制
    if (!isSubscribed) {
      // 如果不是订阅用户，检查是否已达到下载限制
      if (downloadCount >= maxDownloads) {
        // 已达到下载限制，根据登录状态重定向到不同页面
        if (isLoggedIn) {
          // 已登录未订阅，且达到下载限制
          router.push("/pricing");
          return;
        } else {
          // 未登录，且达到下载限制
          router.push("/sign-in?redirectTo=" + encodeURIComponent("/pricing"));
          return;
        }
      }
    }

    // Safari浏览器特殊处理
    if (isSafariBrowser) {
      // 对于Safari，使用新标签页打开
      window.open(resource.url, "_blank");

      setDownloadMessage("已在新标签页打开文件，请检查下载进度");
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      timeoutRef.current = setTimeout(() => {
        setDownloadMessage(null);
      }, 4000);

      // 增加下载计数（仅对非订阅用户）
      if (!isSubscribed) {
        await updateDownloadCount();
      }

      return;
    }

    // 其他浏览器正常下载
    // 允许下载，显示下载消息
    setDownloadMessage("正在准备下载...");
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      setDownloadMessage(null);
    }, 3000);

    try {
      // 尝试多种下载方式
      // 1. 创建并点击临时a标签
      const link = document.createElement("a");
      link.href = resource.url;
      link.setAttribute("download", resource.name); // 设置下载文件名
      link.setAttribute("target", "_blank"); // 设置目标为新窗口
      document.body.appendChild(link);
      link.click();

      // 短暂延迟后移除链接
      setTimeout(() => {
        document.body.removeChild(link);
      }, 100);

      // 2. 如果上面的方法不起作用，作为备份使用iframe
      setTimeout(() => {
        if (downloadFrameRef.current) {
          downloadFrameRef.current.src = resource.url;
        }
      }, 300);
    } catch (error) {
      console.error("下载文件失败:", error);
      // 如果上述方法都失败，最后尝试直接跳转
      window.location.href = resource.url;
    }

    // 增加下载计数（仅对非订阅用户）
    if (!isSubscribed) {
      await updateDownloadCount();
    }
  };

  // 处理资源类型切换
  const handleTypeChange = (type: string) => {
    setSelectedType(type);

    // 根据配置设置默认的section
    const config = getResourceTypeConfig(type);
    if (config?.defaultSection) {
      setFilterSection(config.defaultSection);
    } else {
      setFilterSection(null); // 对于不需要筛选的类型
    }
  };

  // 获取当前资源类型的可用sections
  const getAvailableSections = (type: string): string[] => {
    const config = getResourceTypeConfig(type);
    return config?.sections || [];
  };

  // 过滤资源 - 现在不需要前端过滤，直接返回所有资源
  const getFilteredResources = () => {
    return resources; // 由于API已经按section过滤，这里直接返回
  };

  // 获取当前类型的名称
  const getCurrentTypeName = () => {
    const config = getResourceTypeConfig(selectedType);
    return config ? config.label : "资源";
  };

  // 提取年份从文件名
  const extractYearFromFilename = (filename: string) => {
    const match = filename.match(/(\d{4})/);
    return match ? match[1] : "";
  };

  // 按年份排序资源
  const sortResourcesByYear = (resources: Resource[]) => {
    return [...resources].sort((a, b) => {
      // 特殊处理四六级分类作文模板 - 按数字顺序排列
      if (
        a.section === "四级分类作文模板" ||
        a.section === "六级分类作文模板"
      ) {
        const extractNumber = (filename: string) => {
          const match = filename.match(/^(\d+)、/);
          return match ? parseInt(match[1], 10) : 0;
        };

        const numA = extractNumber(a.name);
        const numB = extractNumber(b.name);

        if (numA !== numB) {
          return numA - numB; // 升序排列：1, 2, 3...
        }
      }

      // 其他资源按年份排序
      const yearA = extractYearFromFilename(a.name);
      const yearB = extractYearFromFilename(b.name);
      return yearB.localeCompare(yearA); // 降序排列
    });
  };

  // 渲染订阅状态信息区块
  const renderSubscriptionBanner = () => {
    if (subscriptionLoading || downloadCountLoading) {
      return (
        <div className="bg-gray-50 p-4 rounded-lg flex items-center justify-center">
          <Loader2 className="w-5 h-5 text-blue-600 animate-spin mr-2" />
          <span className="text-gray-600">正在加载信息...</span>
        </div>
      );
    }

    // 有订阅信息且是订阅用户或终身会员
    if (
      subscriptionInfo?.isSubscribed ||
      subscriptionInfo?.isLifetimeMember ||
      subscriptionInfo?.isActive
    ) {
      const isLifetimeMember =
        subscriptionInfo?.isLifetimeMember ||
        subscriptionInfo?.type === "终身会员" ||
        subscriptionInfo?.endDate === "永久有效";

      return (
        <div className="bg-green-50 p-4 rounded-lg flex items-start gap-3">
          <div className="flex-shrink-0 text-green-600 mt-1">
            <CheckCircle className="w-5 h-5" />
          </div>
          <div>
            <h3 className="font-medium text-green-800">订阅专享内容已解锁</h3>
            <p className="text-sm text-green-700 mt-1">
              {isLifetimeMember
                ? "尊敬的终身订阅用户，您已解锁全部下载权限，可以无限制地下载所有学习资源。"
                : "作为订阅用户，您可以无限制下载所有学习资源。感谢您对我们平台的支持！"}
            </p>
          </div>
        </div>
      );
    }

    // 检查用户是否已登录（通过检查API响应判断）
    const isLoggedIn = subscriptionInfo !== null;

    // 显示下载限制提示（对所有非订阅用户显示，无论是否登录）
    return (
      <div className="bg-blue-50 p-4 rounded-lg flex items-start gap-3">
        <div className="flex-shrink-0 text-blue-600 mt-1">
          <Info className="w-5 h-5" />
        </div>
        <div>
          <h3 className="font-medium text-blue-800">下载限制提示</h3>
          <p className="text-sm text-blue-700 mt-1">
            您当前可以免费下载{maxDownloads}个学习资源，已下载
            {downloadCount}个。 订阅后可无限制下载所有资源。
          </p>
          <div className="mt-3 flex gap-2">
            {isLoggedIn ? (
              <Link
                href="/pricing"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <CheckCircle className="w-4 h-4 mr-1" />
                立即订阅
              </Link>
            ) : (
              <Link
                href="/sign-in"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <LogIn className="w-4 h-4 mr-1" />
                登录账号
              </Link>
            )}
          </div>
        </div>
      </div>
    );
  };

  // 渲染资源列表项（移动端视图）
  const renderMobileResourceItem = (resource: Resource) => {
    return (
      <div
        key={resource.id + resource.name}
        className="bg-white p-4 border-b border-gray-200 last:border-b-0"
      >
        <div className="flex items-start">
          <div className="mr-3">{getFileIcon(resource.type)}</div>
          <div className="flex-1 min-w-0">
            <div className="text-base font-medium text-gray-900 truncate mb-1">
              {resource.name}
            </div>

            <div className="flex flex-wrap gap-2 mb-3">
              {resource.category && (
                <span
                  className={`inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium shadow-sm transition-colors ${getCategoryStyles(resource.category)}`}
                >
                  <span className="mr-1">
                    {getCategoryIcon(resource.category)}
                  </span>{" "}
                  {resource.category}
                </span>
              )}
              {resource.section && (
                <span
                  className={`inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium shadow-sm transition-colors ${getSectionStyles(resource.section)}`}
                >
                  <span className="mr-1">
                    {getSectionIcon(resource.section)}
                  </span>{" "}
                  {resource.section}
                </span>
              )}
              <span className="inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium bg-gray-50 text-gray-600 border border-gray-100 shadow-sm hover:bg-gray-100 transition-colors">
                <span className="mr-1">💾</span> {formatFileSize(resource.size)}
              </span>
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => handleDownload(resource)}
                className="flex-1 inline-flex items-center justify-center px-4 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-lg shadow-md hover:shadow-lg transform hover:scale-[1.02] transition-all duration-200 text-sm font-medium"
                title={`下载${resource.name}`}
              >
                <Download className="w-4 h-4 mr-2" />
                立即下载
              </button>
              <Link
                href={getExamResourceDetailUrl(resource, selectedType)}
                className="inline-flex items-center justify-center px-4 py-2.5 bg-white border-2 border-gray-200 hover:border-blue-400 text-gray-600 hover:text-blue-600 rounded-lg shadow-sm hover:shadow-md transform hover:scale-[1.02] transition-all duration-200 text-sm font-medium"
                title={`查看${resource.name}详情`}
              >
                <Info className="w-4 h-4 mr-1" />
                详情
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // 渲染嵌套选择器
  const renderNestedSelector = () => {
    const config = getTeacherResourceConfig(selectedTeacherType);
    if (!config?.nestedSections) return null;

    const renderLevelSelector = (level: number) => {
      const currentPath = teacherSelectionPath.slice(0, level);
      const currentOptions = getCurrentOptions(
        config.nestedSections!,
        currentPath
      );
      const selectedOption = teacherSelectionPath[level];

      if (currentOptions.length === 0) return null;

      const levelLabels = ["选择资源分类:", "选择子分类:", "选择具体分类:"];
      const label = levelLabels[level] || "选择分类:";

      return (
        <div key={level} className="bg-white rounded-xl shadow-md p-4 mb-4">
          <div className="flex items-center mb-2">
            <span className="text-gray-700 font-medium mr-4">{label}</span>
            <div className="flex flex-wrap gap-2">
              {currentOptions.map((option: string) => (
                <button
                  key={option}
                  onClick={() => handleNestedSelection(option, level)}
                  className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                    selectedOption === option
                      ? "bg-blue-600 text-white"
                      : "bg-gray-100 text-gray-800 hover:bg-gray-200"
                  }`}
                >
                  <span className="mr-1">{getSectionIcon(option)}</span>
                  {option}
                </button>
              ))}
            </div>
          </div>
        </div>
      );
    };

    // 渲染所有层级的选择器
    const selectors = [];
    for (let i = 0; i <= teacherSelectionPath.length; i++) {
      const selector = renderLevelSelector(i);
      if (selector) {
        selectors.push(selector);
      }
    }

    return <>{selectors}</>;
  };

  return (
    <>
      <Head>
        <title>
          学习资源库 - 考研、四六级、专四专八免费资料下载 - 面包资料屋
        </title>
        <meta
          name="description"
          content="提供考研英语、政治、数学、专业课以及四六级、专四专八和大学生创新创业等免费学习资源下载，助力你的考试备考与创业规划。"
        />
        <meta
          name="keywords"
          content="考研资料,四六级资料,真题下载,答案解析,免费学习资源,面包资料屋"
        />

        {/* Open Graph */}
        <meta property="og:title" content="学习资源库 - 面包资料屋" />
        <meta
          property="og:description"
          content="免费提供考研、四六级等优质学习资源下载"
        />
        <meta property="og:type" content="website" />
        <meta
          property="og:url"
          content="https://mbdata.site/learning-resources"
        />

        {/* Twitter Card */}
        <meta name="twitter:card" content="summary" />
        <meta name="twitter:title" content="学习资源库 - 面包资料屋" />
        <meta
          name="twitter:description"
          content="免费提供考研、四六级等优质学习资源下载"
        />
      </Head>

      {/* 添加结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "CollectionPage",
            name: "学习资源库",
            description:
              "提供考研英语、政治、数学、专业课以及四六级、专四专八和大学生创新创业等免费学习资源下载",
            url: "https://mbdata.site/learning-resources",
            provider: {
              "@type": "Organization",
              name: "面包资料屋",
              url: "https://mbdata.site",
            },
            mainEntity: {
              "@type": "ItemList",
              name: "学习资源",
              description: "考研、四六级等学习资源集合",
              numberOfItems: resources.length,
              itemListElement: resources
                .slice(0, 10)
                .map((resource, index) => ({
                  "@type": "EducationalResource",
                  position: index + 1,
                  name: resource.name,
                  description: `${resource.category}${resource.section}学习资料`,
                  url: `https://mbdata.site${getExamResourceDetailUrl(resource, selectedType)}`,
                  educationalUse: resource.section,
                  learningResourceType: getReadableFileType(resource.type),
                  contentSize: formatFileSize(resource.size),
                  encodingFormat: resource.type,
                  about: {
                    "@type": "Course",
                    name: resource.category,
                  },
                })),
            },
            breadcrumb: {
              "@type": "BreadcrumbList",
              itemListElement: [
                {
                  "@type": "ListItem",
                  position: 1,
                  name: "首页",
                  item: "https://mbdata.site",
                },
                {
                  "@type": "ListItem",
                  position: 2,
                  name: "学习资源库",
                  item: "https://mbdata.site/learning-resources",
                },
              ],
            },
            inLanguage: "zh-CN",
            audience: {
              "@type": "EducationalAudience",
              educationalRole: "student",
            },
          }),
        }}
      ></script>

      <div className="bg-slate-50 py-10 min-h-screen">
        <iframe ref={downloadFrameRef} style={{ display: "none" }}></iframe>
        {/* 下载提示信息 */}
        {downloadMessage && (
          <div className="fixed top-5 left-1/2 transform -translate-x-1/2 z-50 bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded shadow-lg">
            <div className="flex items-center">
              <Info className="w-5 h-5 mr-2" />
              <p>{downloadMessage}</p>
            </div>
          </div>
        )}

        <div className="container px-2 sm:px-4 md:px-6 lg:px-8 ">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-800 mb-4">资源库</h1>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              提供考研英语、政治、数学、专业课以及四六级、专四专八、教资笔试面试真题解析及资料包，历年真题等资源及大学生创新创业资源，助力你的考试备考。
            </p>
          </div>

          {/* 订阅状态区块 */}
          {renderSubscriptionBanner()}

          {/* 主要标签页切换 */}
          <div className="bg-white rounded-xl shadow-md mb-8 overflow-hidden">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex" aria-label="Tabs">
                <button
                  onClick={() => toggleResourceType("exam")}
                  className={`flex-1 py-4 px-1 text-center border-b-2 text-lg font-medium ${
                    !showInnovationResources && !showTeacherResources
                      ? "border-blue-600 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  考研，四六级，真题解析等资源
                </button>
                <button
                  onClick={() => toggleResourceType("teacher")}
                  className={`flex-1 py-4 px-1 text-center border-b-2 text-lg font-medium ${
                    showTeacherResources
                      ? "border-blue-600 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  教资笔试面试真题解析及资料包
                </button>
                <button
                  onClick={() => toggleResourceType("innovation")}
                  className={`flex-1 py-4 px-1 text-center border-b-2 text-lg font-medium ${
                    showInnovationResources
                      ? "border-blue-600 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  大学生创新创业合集汇总
                </button>
              </nav>
            </div>

            {/* 子分类选择区域 */}
            <div className="p-4">
              {!showInnovationResources && !showTeacherResources ? (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-3">
                    选择资源类别:
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {resourceTypes.map((type) => (
                      <button
                        key={type.id}
                        onClick={() => handleTypeChange(type.id)}
                        className={`px-4 py-2 rounded-lg transition-colors ${
                          selectedType === type.id
                            ? "bg-blue-600 text-white"
                            : "bg-gray-100 text-gray-800 hover:bg-gray-200"
                        }`}
                      >
                        {type.label}
                      </button>
                    ))}
                  </div>
                </div>
              ) : showTeacherResources ? (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-3">
                    选择教资资源类别:
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {TEACHER_RESOURCE_CONFIGS.map((type) => (
                      <button
                        key={type.id}
                        onClick={() => handleTeacherTypeChange(type.id)}
                        className={`px-4 py-2 rounded-lg transition-colors ${
                          selectedTeacherType === type.id
                            ? "bg-blue-600 text-white"
                            : "bg-gray-100 text-gray-800 hover:bg-gray-200"
                        }`}
                      >
                        {type.label}
                      </button>
                    ))}
                  </div>
                </div>
              ) : (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-3">
                    选择创新创业资源类别:
                  </h3>
                  <div className="flex flex-wrap gap-2 overflow-x-auto pb-2">
                    {innovationResourceTypes.map((type) => (
                      <button
                        key={type.id}
                        onClick={() => handleInnovationTypeChange(type.id)}
                        className={`px-4 py-2 rounded-lg transition-colors whitespace-nowrap ${
                          selectedInnovationType === type.id
                            ? "bg-blue-600 text-white"
                            : "bg-gray-100 text-gray-800 hover:bg-gray-200"
                        }`}
                      >
                        {type.label}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 教资嵌套选择器 */}
          {showTeacherResources && renderNestedSelector()}

          {/* 资源筛选选择 - 仅在显示考试资源且需要API且有sections的类型时显示 */}
          {!showInnovationResources &&
            !showTeacherResources &&
            (() => {
              const config = getResourceTypeConfig(selectedType);
              return (
                config?.needsApi &&
                config?.sections &&
                config.sections.length > 0 &&
                resources.length > 0
              );
            })() && (
              <div className="bg-white rounded-xl shadow-md p-4 mb-8">
                <div className="flex items-center mb-2">
                  <span className="text-gray-700 font-medium mr-4">
                    资源筛选:
                  </span>
                  <div className="flex flex-wrap gap-2">
                    {getAvailableSections(selectedType).map((section) => (
                      <button
                        key={section}
                        onClick={() => setFilterSection(section)}
                        className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                          filterSection === section
                            ? "bg-blue-600 text-white"
                            : "bg-gray-100 text-gray-800 hover:bg-gray-200"
                        }`}
                      >
                        {section}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}

          {/* 特殊组件展示区域 - 根据配置显示 */}
          {!showInnovationResources &&
            !showTeacherResources &&
            (() => {
              const config = getResourceTypeConfig(selectedType);
              if (config?.specialComponent === "print") {
                return (
                  <div className="bg-white rounded-xl shadow-md overflow-hidden">
                    <div className="p-6 text-center">
                      <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                        <p className="text-lg text-orange-800 font-medium">
                          {config.description}
                        </p>
                      </div>
                      <div className="max-w-2xl mx-auto">
                        <img
                          src="/images/print.jpg"
                          alt="打印平台"
                          className="w-full h-auto rounded-lg shadow-lg mb-6"
                        />
                      </div>
                    </div>
                  </div>
                );
              }
              return null;
            })()}

          {/* 资源类型说明 - 根据配置显示 */}
          {!showInnovationResources &&
            !showTeacherResources &&
            (() => {
              const config = getResourceTypeConfig(selectedType);
              if (
                config?.description &&
                config.needsApi &&
                resources.length > 0
              ) {
                const colorClass =
                  selectedType === "kaoyan_answer_sheet"
                    ? "bg-amber-50 border-amber-200 text-amber-600 text-amber-800 text-amber-700"
                    : "bg-green-50 border-green-200 text-green-600 text-green-800 text-green-700";

                return (
                  <div
                    className={`${colorClass.split(" ").slice(0, 2).join(" ")} rounded-xl shadow-md p-4 mb-8`}
                  >
                    <div className="flex items-start gap-3">
                      <div
                        className={`flex-shrink-0 ${colorClass.split(" ")[2]} mt-1`}
                      >
                        <Info className="w-5 h-5" />
                      </div>
                      <div>
                        <h3
                          className={`font-medium ${colorClass.split(" ")[3]}`}
                        >
                          {config.label}使用说明
                        </h3>
                        <p
                          className={`text-sm ${colorClass.split(" ")[4]} mt-1`}
                        >
                          {config.description}
                        </p>
                      </div>
                    </div>
                  </div>
                );
              }
              return null;
            })()}

          {/* 教资资源筛选选择 - 在选择完嵌套路径且有资源时显示 */}
          {showTeacherResources &&
            teacherResources.length > 0 &&
            (() => {
              const config = getTeacherResourceConfig(selectedTeacherType);
              if (!config?.nestedSections) return false;
              const availableSections = getTeacherLeafSections(
                config.nestedSections,
                teacherSelectionPath
              );
              return availableSections.length > 0;
            })() && (
              <div className="bg-white rounded-xl shadow-md p-4 mb-8">
                <div className="flex items-center mb-2">
                  <span className="text-gray-700 font-medium mr-4">
                    资源筛选:
                  </span>
                  <div className="flex flex-wrap gap-2">
                    {(() => {
                      const config =
                        getTeacherResourceConfig(selectedTeacherType);
                      if (!config?.nestedSections) return [];
                      return getTeacherLeafSections(
                        config.nestedSections,
                        teacherSelectionPath
                      );
                    })().map((section) => (
                      <button
                        key={section}
                        onClick={() => setTeacherFilterSection(section)}
                        className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                          teacherFilterSection === section
                            ? "bg-blue-600 text-white"
                            : "bg-gray-100 text-gray-800 hover:bg-gray-200"
                        }`}
                      >
                        <span className="mr-1">{getSectionIcon(section)}</span>
                        {section}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}

          {/* 教资资源显示部分 */}
          {showTeacherResources &&
            (teacherLoading ? (
              <div className="text-center py-20 bg-white rounded-xl shadow-md">
                <div className="text-7xl mb-4">⏳</div>
                <h3 className="text-xl font-medium text-gray-800 mb-2">
                  加载中...
                </h3>
                <p className="text-gray-600">正在获取教资资源，请稍候</p>
              </div>
            ) : teacherError ? (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                {teacherError}
              </div>
            ) : getFilteredTeacherResources().length === 0 ? (
              <div className="text-center py-20 bg-white rounded-xl shadow-md">
                <div className="text-7xl mb-4">📚</div>
                <h3 className="text-xl font-medium text-gray-800 mb-2">
                  {teacherSelectionPath.length === 0
                    ? "请选择资源分类"
                    : teacherResources.length === 0
                      ? "资源准备中"
                      : "当前筛选条件下暂无资源"}
                </h3>
                <p className="text-gray-600">
                  {teacherSelectionPath.length === 0
                    ? "请在上方选择具体的资源分类路径"
                    : teacherResources.length === 0
                      ? "我们正在整理优质的教资学习资源，敬请期待！"
                      : "请尝试选择其他筛选条件"}
                </p>
              </div>
            ) : (
              <>
                {/* 桌面端表格视图 - 大屏幕显示 */}
                <div className="hidden md:block bg-white rounded-xl shadow-md overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th
                            scope="col"
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            文件类型
                          </th>
                          <th
                            scope="col"
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            文件名称
                          </th>
                          <th
                            scope="col"
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            资源分类
                          </th>
                          <th
                            scope="col"
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            文件大小
                          </th>
                          <th
                            scope="col"
                            className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            操作
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {sortResourcesByYear(getFilteredTeacherResources()).map(
                          (resource) => (
                            <tr
                              key={resource.id + resource.name}
                              className="hover:bg-gray-50"
                            >
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span>{getFileIcon(resource.type)}</span>
                              </td>
                              <td className="px-6 py-4">
                                <div className="text-sm font-medium text-gray-900">
                                  {resource.name}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {getReadableFileType(resource.type)}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex flex-wrap gap-2">
                                  {resource.category && (
                                    <span
                                      className={`inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium shadow-sm transition-colors ${getCategoryStyles(resource.category)}`}
                                    >
                                      <span className="mr-1">
                                        {getCategoryIcon(resource.category)}
                                      </span>{" "}
                                      {resource.category}
                                    </span>
                                  )}
                                  {resource.section && (
                                    <span
                                      className={`inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium shadow-sm transition-colors ${getSectionStyles(resource.section)}`}
                                    >
                                      <span className="mr-1">
                                        {getSectionIcon(resource.section)}
                                      </span>{" "}
                                      {resource.section}
                                    </span>
                                  )}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className="inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium bg-gray-50 text-gray-600 border border-gray-100 shadow-sm hover:bg-gray-100 transition-colors">
                                  <span className="mr-1">💾</span>{" "}
                                  {formatFileSize(resource.size)}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div className="flex justify-end space-x-2">
                                  <button
                                    onClick={() => handleDownload(resource)}
                                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                                    title={`下载${resource.name}`}
                                  >
                                    <Download className="w-4 h-4 mr-2" />
                                    立即下载
                                  </button>
                                  <Link
                                    href={getTeacherResourceDetailUrl(
                                      resource,
                                      selectedTeacherType,
                                      teacherSelectionPath
                                    )}
                                    className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-600 rounded-md hover:border-blue-500 hover:text-blue-600 transition-colors text-sm"
                                    title={`查看${resource.name}详情`}
                                  >
                                    <Info className="w-4 h-4 mr-1" />
                                    详情
                                  </Link>
                                </div>
                              </td>
                            </tr>
                          )
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 移动端列表视图 - 小屏幕显示 */}
                <div className="md:hidden bg-white rounded-xl shadow-md overflow-hidden">
                  <div className="divide-y divide-gray-200">
                    {sortResourcesByYear(getFilteredTeacherResources()).map(
                      (resource) => (
                        <div
                          key={resource.id + resource.name}
                          className="bg-white p-4 border-b border-gray-200 last:border-b-0"
                        >
                          <div className="flex items-start">
                            <div className="mr-3">
                              {getFileIcon(resource.type)}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="text-base font-medium text-gray-900 truncate mb-1">
                                {resource.name}
                              </div>

                              <div className="flex flex-wrap gap-2 mb-3">
                                {resource.category && (
                                  <span
                                    className={`inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium shadow-sm transition-colors ${getCategoryStyles(resource.category)}`}
                                  >
                                    <span className="mr-1">
                                      {getCategoryIcon(resource.category)}
                                    </span>{" "}
                                    {resource.category}
                                  </span>
                                )}
                                {resource.section && (
                                  <span
                                    className={`inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium shadow-sm transition-colors ${getSectionStyles(resource.section)}`}
                                  >
                                    <span className="mr-1">
                                      {getSectionIcon(resource.section)}
                                    </span>{" "}
                                    {resource.section}
                                  </span>
                                )}
                                <span className="inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium bg-gray-50 text-gray-600 border border-gray-100 shadow-sm hover:bg-gray-100 transition-colors">
                                  <span className="mr-1">💾</span>{" "}
                                  {formatFileSize(resource.size)}
                                </span>
                              </div>

                              <div className="flex gap-3">
                                <button
                                  onClick={() => handleDownload(resource)}
                                  className="flex-1 inline-flex items-center justify-center px-4 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-lg shadow-md hover:shadow-lg transform hover:scale-[1.02] transition-all duration-200 text-sm font-medium"
                                  title={`下载${resource.name}`}
                                >
                                  <Download className="w-4 h-4 mr-2" />
                                  立即下载
                                </button>
                                <Link
                                  href={getTeacherResourceDetailUrl(
                                    resource,
                                    selectedTeacherType,
                                    teacherSelectionPath
                                  )}
                                  className="inline-flex items-center justify-center px-4 py-2.5 bg-white border-2 border-gray-200 hover:border-blue-400 text-gray-600 hover:text-blue-600 rounded-lg shadow-sm hover:shadow-md transform hover:scale-[1.02] transition-all duration-200 text-sm font-medium"
                                  title={`查看${resource.name}详情`}
                                >
                                  <Info className="w-4 h-4 mr-1" />
                                  详情
                                </Link>
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    )}
                  </div>
                </div>
              </>
            ))}

          {/* 考试资源加载中或错误提示 */}
          {!showInnovationResources &&
            !showTeacherResources &&
            (() => {
              const config = getResourceTypeConfig(selectedType);
              return config?.needsApi;
            })() &&
            (loading ? (
              <div className="text-center py-20 bg-white rounded-xl shadow-md">
                <div className="text-7xl mb-4">⏳</div>
                <h3 className="text-xl font-medium text-gray-800 mb-2">
                  加载中...
                </h3>
                <p className="text-gray-600">正在获取学习资源，请稍候</p>
              </div>
            ) : error ? (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                {error}
              </div>
            ) : getFilteredResources().length === 0 ? (
              <div className="text-center py-20 bg-white rounded-xl shadow-md">
                <div className="text-7xl mb-4">📚</div>
                <h3 className="text-xl font-medium text-gray-800 mb-2">
                  资源准备中
                </h3>
                <p className="text-gray-600">
                  我们正在整理优质的{getCurrentTypeName()}学习资源，敬请期待！
                </p>
              </div>
            ) : (
              <>
                {/* 桌面端表格视图 - 大屏幕显示 */}
                <div className="hidden md:block bg-white rounded-xl shadow-md overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th
                            scope="col"
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            文件类型
                          </th>
                          <th
                            scope="col"
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            文件名称
                          </th>
                          <th
                            scope="col"
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            资源分类
                          </th>
                          <th
                            scope="col"
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            文件大小
                          </th>
                          <th
                            scope="col"
                            className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            操作
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {sortResourcesByYear(getFilteredResources()).map(
                          (resource) => (
                            <tr
                              key={resource.id + resource.name}
                              className="hover:bg-gray-50"
                            >
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span>{getFileIcon(resource.type)}</span>
                              </td>
                              <td className="px-6 py-4">
                                <div className="text-sm font-medium text-gray-900">
                                  {resource.name}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {getReadableFileType(resource.type)}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex flex-wrap gap-2">
                                  {resource.category && (
                                    <span
                                      className={`inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium shadow-sm transition-colors ${getCategoryStyles(resource.category)}`}
                                    >
                                      <span className="mr-1">
                                        {getCategoryIcon(resource.category)}
                                      </span>{" "}
                                      {resource.category}
                                    </span>
                                  )}
                                  {resource.section && (
                                    <span
                                      className={`inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium shadow-sm transition-colors ${getSectionStyles(resource.section)}`}
                                    >
                                      <span className="mr-1">
                                        {getSectionIcon(resource.section)}
                                      </span>{" "}
                                      {resource.section}
                                    </span>
                                  )}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className="inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium bg-gray-50 text-gray-600 border border-gray-100 shadow-sm hover:bg-gray-100 transition-colors">
                                  <span className="mr-1">💾</span>{" "}
                                  {formatFileSize(resource.size)}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div className="flex justify-end space-x-2">
                                  <button
                                    onClick={() => handleDownload(resource)}
                                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                                    title={`下载${resource.name}`}
                                  >
                                    <Download className="w-4 h-4 mr-2" />
                                    立即下载
                                  </button>
                                  <Link
                                    href={getExamResourceDetailUrl(
                                      resource,
                                      selectedType
                                    )}
                                    className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-600 rounded-md hover:border-blue-500 hover:text-blue-600 transition-colors text-sm"
                                    title={`查看${resource.name}详情`}
                                  >
                                    <Info className="w-4 h-4 mr-1" />
                                    详情
                                  </Link>
                                </div>
                              </td>
                            </tr>
                          )
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 移动端列表视图 - 小屏幕显示 */}
                <div className="md:hidden bg-white rounded-xl shadow-md overflow-hidden">
                  <div className="divide-y divide-gray-200">
                    {sortResourcesByYear(getFilteredResources()).map(
                      renderMobileResourceItem
                    )}
                  </div>
                </div>
              </>
            ))}

          {/* 大创资源显示部分 */}
          {showInnovationResources &&
            (innovationLoading ? (
              <div className="text-center py-20 bg-white rounded-xl shadow-md">
                <div className="text-7xl mb-4">⏳</div>
                <h3 className="text-xl font-medium text-gray-800 mb-2">
                  加载中...
                </h3>
                <p className="text-gray-600">正在获取创新创业资源，请稍候</p>
              </div>
            ) : innovationError ? (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                {innovationError}
              </div>
            ) : innovationResources.length === 0 ? (
              <div className="text-center py-20 bg-white rounded-xl shadow-md">
                <div className="text-7xl mb-4">📚</div>
                <h3 className="text-xl font-medium text-gray-800 mb-2">
                  资源准备中
                </h3>
                <p className="text-gray-600">
                  我们正在整理优质的
                  {getInnovationTypeName(selectedInnovationType)}
                  创新创业资源，敬请期待！
                </p>
              </div>
            ) : (
              <>
                {/* 桌面端表格视图 - 大屏幕显示 */}
                <div className="hidden md:block bg-white rounded-xl shadow-md overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th
                            scope="col"
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            文件类型
                          </th>
                          <th
                            scope="col"
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            文件名称
                          </th>
                          <th
                            scope="col"
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            资源分类
                          </th>
                          <th
                            scope="col"
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            文件大小
                          </th>
                          <th
                            scope="col"
                            className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            操作
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {innovationResources.map((resource) => (
                          <tr
                            key={resource.id + resource.name}
                            className="hover:bg-gray-50"
                          >
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span>{getFileIcon(resource.type)}</span>
                            </td>
                            <td className="px-6 py-4">
                              <div className="text-sm font-medium text-gray-900">
                                {resource.name}
                              </div>
                              <div className="text-xs text-gray-500">
                                {getReadableFileType(resource.type)}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex flex-wrap gap-2">
                                {resource.category && (
                                  <span
                                    className={`inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium shadow-sm transition-colors ${getInnovationCategoryStyles(resource.category)}`}
                                  >
                                    {resource.category}
                                  </span>
                                )}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className="inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium bg-gray-50 text-gray-600 border border-gray-100 shadow-sm hover:bg-gray-100 transition-colors">
                                <span className="mr-1">💾</span>{" "}
                                {formatFileSize(resource.size)}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <div className="flex justify-end space-x-2">
                                <button
                                  onClick={() => handleDownload(resource)}
                                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                                  title={`下载${resource.name}`}
                                >
                                  <Download className="w-4 h-4 mr-2" />
                                  立即下载
                                </button>
                                <Link
                                  href={getInnovationResourceDetailUrl(
                                    resource,
                                    selectedInnovationType
                                  )}
                                  className="inline-flex items-center px-4 py-2.5 bg-white border-2 border-gray-200 hover:border-blue-400 text-gray-600 hover:text-blue-600 rounded-lg shadow-sm hover:shadow-md transform hover:scale-[1.02] transition-all duration-200 text-sm font-medium"
                                  title={`查看${resource.name}详情`}
                                >
                                  <Info className="w-4 h-4 mr-1" />
                                  详情
                                </Link>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 移动端列表视图 - 小屏幕显示 */}
                <div className="md:hidden bg-white rounded-xl shadow-md overflow-hidden">
                  <div className="divide-y divide-gray-200">
                    {innovationResources.map((resource) => (
                      <div
                        key={resource.id + resource.name}
                        className="bg-white p-4 border-b border-gray-200 last:border-b-0"
                      >
                        <div className="flex items-start">
                          <div className="text-2xl mr-3">
                            {getFileIcon(resource.type)}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="text-base font-medium text-gray-900 truncate mb-1">
                              {resource.name}
                            </div>
                            <div className="text-xs text-gray-500 mb-2">
                              {getReadableFileType(resource.type)}
                            </div>

                            <div className="flex flex-wrap gap-2 mb-3">
                              {resource.category && (
                                <span
                                  className={`inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium shadow-sm transition-colors ${getInnovationCategoryStyles(resource.category)}`}
                                >
                                  {resource.category}
                                </span>
                              )}
                              <span className="inline-flex items-center justify-center px-3.5 py-1.5 rounded-md text-xs font-medium bg-gray-50 text-gray-600 border border-gray-100 shadow-sm hover:bg-gray-100 transition-colors">
                                <span className="mr-1">💾</span>{" "}
                                {formatFileSize(resource.size)}
                              </span>
                            </div>

                            <div className="flex gap-3">
                              <button
                                onClick={() => handleDownload(resource)}
                                className="flex-1 inline-flex items-center justify-center px-4 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-lg shadow-md hover:shadow-lg transform hover:scale-[1.02] transition-all duration-200 text-sm font-medium"
                                title={`下载${resource.name}`}
                              >
                                <Download className="w-4 h-4 mr-2" />
                                立即下载
                              </button>
                              <Link
                                href={getInnovationResourceDetailUrl(
                                  resource,
                                  selectedInnovationType
                                )}
                                className="inline-flex items-center justify-center px-4 py-2.5 bg-white border-2 border-gray-200 hover:border-blue-400 text-gray-600 hover:text-blue-600 rounded-lg shadow-sm hover:shadow-md transform hover:scale-[1.02] transition-all duration-200 text-sm font-medium"
                                title={`查看${resource.name}详情`}
                              >
                                <Info className="w-4 h-4 mr-1" />
                                详情
                              </Link>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            ))}

          <div className="mt-16 bg-blue-50 border border-blue-100 rounded-xl p-6 sm:p-8 text-center">
            <h3 className="text-2xl font-semibold text-blue-800 mb-4">
              想要更多备考资料？
            </h3>
            <p className="text-blue-700 mb-6">
              关注公众号「面包资料屋」获取更多独家考研、四六级等考试资料和学习方法。
            </p>
            <div className="inline-flex items-center justify-center px-5 py-3 bg-blue-600 text-white rounded-lg text-lg">
              <span className="mr-2">📱</span>
              公众号：面包资料屋
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
