import React from "react";

interface HighlightProps {
  content: string;
  highlights: string[];
  highlightClassName?: string;
}

export const Highlight: React.FC<HighlightProps> = ({
  content,
  highlights,
  highlightClassName = "bg-yellow-200",
}) => {
  if (!content || !highlights || highlights.length === 0) {
    return <>{content}</>;
  }

  // 转义正则表达式特殊字符
  const escapeRegExp = (string: string) => {
    return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  };

  // 创建一个正则表达式，匹配所有高亮文本
  const highlightTexts = highlights.map((text) => escapeRegExp(text)).join("|");

  const regex = new RegExp(`(${highlightTexts})`, "gi");

  // 分割内容并高亮匹配项
  const parts = content.split(regex);

  return (
    <>
      {parts.map((part, i) => {
        const isHighlighted = highlights.some(
          (highlight) => part.toLowerCase() === highlight.toLowerCase()
        );

        return isHighlighted ? (
          <span key={i} className={highlightClassName}>
            {part}
          </span>
        ) : (
          <React.Fragment key={i}>{part}</React.Fragment>
        );
      })}
    </>
  );
};
