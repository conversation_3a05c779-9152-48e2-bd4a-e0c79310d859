"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";

interface Sentence {
  id: string;
  year: string;
  type: string;
  sectionType: string;
  createdAt: string;
  originalContent: string;
  indexNum: number;
  paragraphNum: number;
  explain_md?: string;
}

export default function SentenceEditPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const [sentence, setSentence] = useState<Sentence | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 表单数据
  const [formData, setFormData] = useState<any>({});

  // 获取句子详情
  useEffect(() => {
    const fetchSentence = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/sentences/${params.id}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error("句子不存在");
          }
          throw new Error("获取句子详情失败");
        }

        const data = await response.json();

        if (data.success) {
          setSentence(data.data);
          initializeFormData(data.data);
        } else {
          throw new Error(data.error || "获取数据失败");
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "未知错误");
        console.error("获取句子详情错误:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchSentence();
  }, [params.id]);

  // 这里根据句子数据初始化表单
  const initializeFormData = (sentenceData: Sentence) => {
    // 设置表单初始状态
    setFormData({
      id: sentenceData.id,
      year: sentenceData.year,
      type: sentenceData.type,
      sectionType: sentenceData.sectionType,
      originalContent: sentenceData.originalContent,
      indexNum: sentenceData.indexNum,
      paragraphNum: sentenceData.paragraphNum || 1, // 初始化段落编号
      explain_md: sentenceData.explain_md || "", // 初始化Markdown内容
    });
  };

  // 处理表单字段变化
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  };

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSaving(true);
      setError(null);

      // 准备提交的数据
      const dataToSubmit = {
        year: formData.year,
        type: formData.type,
        sectionType: formData.sectionType,
        originalContent: formData.originalContent,
        paragraphNum: formData.paragraphNum,
        indexNum: formData.indexNum,
        explain_md: formData.explain_md || "",
      };

      // 发送请求更新句子
      const response = await fetch(`/api/sentences/${params.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(dataToSubmit),
      });

      const data = await response.json();

      if (data.success) {
        // 跳转到句子详情页面
        router.push(`/admin/sentences/${params.id}`);
      } else {
        throw new Error(data.error || "更新失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "更新失败");
      console.error("更新句子错误:", err);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
          <p className="text-blue-700 font-medium">正在加载...</p>
        </div>
      </div>
    );
  }

  if (error && !sentence) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="bg-red-100 p-4 rounded-full">
            <svg
              className="h-12 w-12 text-red-500"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <p className="text-red-700 font-medium">{error}</p>
          <Link
            href="/admin/sentences"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            返回列表
          </Link>
        </div>
      </div>
    );
  }

  if (!formData) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <p className="text-gray-500 font-medium">无法加载句子数据</p>
          <Link
            href="/admin/sentences"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            返回列表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-8">
      {/* 顶部标题和操作栏 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
            编辑句子
          </h1>
          <p className="text-gray-500 mt-2 text-lg">
            编辑句子 #{formData.paragraphNum}-{formData.indexNum} 的详细分析
          </p>
        </div>
        <div className="flex space-x-4">
          <Link
            href={`/admin/sentences/${params.id}`}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors"
          >
            取消
          </Link>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-500"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* 表单 */}
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* 基本信息 */}
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <h2 className="text-xl font-bold text-gray-800 mb-6">基本信息</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <label className="text-gray-700 font-medium">年份</label>
              <select
                name="year"
                value={formData.year}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                {Array.from({ length: 16 }, (_, i) => 2010 + i).map((year) => (
                  <option key={year} value={year.toString()}>
                    {year}年
                  </option>
                ))}
              </select>
            </div>

            <div className="space-y-2">
              <label className="text-gray-700 font-medium">类型</label>
              <select
                name="type"
                value={formData.type}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="英语一">英语一</option>
                <option value="英语二">英语二</option>
              </select>
            </div>

            <div className="space-y-2">
              <label className="text-gray-700 font-medium">题型</label>
              <select
                name="sectionType"
                value={formData.sectionType}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="use_of_english">完形填空</option>
                <option value="reading_comprehension">阅读理解</option>
                <option value="translation">翻译</option>
                <option value="writing">写作</option>
              </select>
            </div>
          </div>

          <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <label className="text-gray-700 font-medium">段落编号</label>
              <input
                type="number"
                name="paragraphNum"
                value={formData.paragraphNum}
                onChange={handleChange}
                min="1"
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            <div className="space-y-2">
              <label className="text-gray-700 font-medium">句子编号</label>
              <input
                type="number"
                name="indexNum"
                value={formData.indexNum}
                onChange={handleChange}
                min="1"
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
          </div>

          <div className="mt-6">
            <div className="space-y-2">
              <label className="text-gray-700 font-medium">原文句子</label>
              <textarea
                name="originalContent"
                value={formData.originalContent}
                onChange={handleChange}
                placeholder="输入原文句子..."
                rows={4}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            <div className="space-y-2 mt-6">
              <label className="text-gray-700 font-medium">
                Markdown解析内容
              </label>
              <textarea
                name="explain_md"
                value={formData.explain_md || ""}
                onChange={handleChange}
                rows={10}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono"
                placeholder="请输入Markdown格式的解析内容..."
              ></textarea>
              <p className="text-sm text-gray-500">
                使用Markdown格式编写解析内容，支持标题、列表、加粗、链接等格式
              </p>
            </div>
          </div>
        </div>

        {/* 表单提交按钮 */}
        <div className="flex justify-end space-x-4">
          <Link
            href={`/admin/sentences/${params.id}`}
            className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors"
          >
            取消
          </Link>
          <button
            type="submit"
            disabled={saving}
            className={`px-6 py-3 bg-blue-600 text-white rounded-xl transition-colors ${
              saving ? "opacity-70 cursor-not-allowed" : "hover:bg-blue-700"
            }`}
          >
            {saving ? "保存中..." : "保存句子"}
          </button>
        </div>
      </form>
    </div>
  );
}
