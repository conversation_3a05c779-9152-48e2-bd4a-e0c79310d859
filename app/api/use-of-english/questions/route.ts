import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { Database } from "@/types/supabase";
import { timeStart, timeEnd } from "@/utils/performance";

// 创建Supabase客户端
const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL || "",
  process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || ""
);

// 查询缓存
const cache: Record<string, { data: any; timestamp: number }> = {};
const CACHE_TTL = 60 * 1000; // 缓存有效期1分钟

// GET方法：获取完形填空题目
export async function GET(request: NextRequest) {
  const totalStartTime = timeStart("GET /api/use-of-english/questions 总耗时");
  try {
    // 获取查询参数
    const searchParams = request.nextUrl.searchParams;
    const paperId = searchParams.get("paperId");
    const questionNumber = searchParams.get("questionNumber");

    // 验证必须提供paperId
    if (!paperId) {
      timeEnd("GET /api/use-of-english/questions 总耗时", totalStartTime);
      return NextResponse.json(
        {
          success: false,
          error: "Missing required parameter: paperId",
        },
        { status: 400 }
      );
    }

    // 构建缓存键
    let cacheKey = `use_of_english_questions_${paperId}`;
    if (questionNumber) {
      cacheKey += `_${questionNumber}`;
    }

    // 尝试从缓存中获取数据
    const now = Date.now();

    if (cache[cacheKey] && now - cache[cacheKey].timestamp < CACHE_TTL) {
      timeEnd("GET /api/use-of-english/questions 总耗时", totalStartTime);
      return NextResponse.json({
        success: true,
        data: cache[cacheKey].data,
        fromCache: true,
      });
    }

    // 构建查询
    let query = supabase
      .from("use_of_english_questions")
      .select("*")
      .eq("paper_id", paperId);

    // 如果提供了题号，则按题号筛选
    if (questionNumber) {
      query = query.eq("question_number", questionNumber);
    }

    // 查询数据库
    const queryStartTime = timeStart("查询use_of_english_questions数据");
    const { data, error } = await query.order("question_number", {
      ascending: true,
    });
    timeEnd("查询use_of_english_questions数据", queryStartTime);

    if (error) {
      throw error;
    }

    // 更新缓存
    cache[cacheKey] = {
      data: data || [],
      timestamp: now,
    };

    // 返回成功响应
    timeEnd("GET /api/use-of-english/questions 总耗时", totalStartTime);
    return NextResponse.json({
      success: true,
      data: data || [],
    });
  } catch (err) {
    console.error("获取完形填空题目错误:", err);
    timeEnd("GET /api/use-of-english/questions 总耗时", totalStartTime);
    return NextResponse.json(
      {
        success: false,
        error: err instanceof Error ? err.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// POST方法：保存完形填空题目
export async function POST(request: NextRequest) {
  const totalStartTime = timeStart("POST /api/use-of-english/questions 总耗时");
  try {
    // 获取请求数据
    const bodyStartTime = timeStart("解析请求体");
    const requestData = await request.json();
    timeEnd("解析请求体", bodyStartTime);

    // 支持单个题目或批量导入题目
    const isBulkOperation = Array.isArray(requestData.questions);
    const paperId = isBulkOperation
      ? requestData.paper_id
      : requestData.paper_id ||
        (requestData.questions && requestData.questions[0]?.paper_id);

    // 验证必填字段
    if (!paperId) {
      timeEnd("POST /api/use-of-english/questions 总耗时", totalStartTime);
      return NextResponse.json(
        {
          success: false,
          error: "Missing 'paper_id' field",
        },
        { status: 400 }
      );
    }

    // 处理批量导入题目
    if (isBulkOperation) {
      if (!requestData.questions || requestData.questions.length === 0) {
        timeEnd("POST /api/use-of-english/questions 总耗时", totalStartTime);
        return NextResponse.json(
          {
            success: false,
            error: "Empty 'questions' array",
          },
          { status: 400 }
        );
      }

      // 预处理题目数据
      const questions = requestData.questions.map((q: any) => ({
        ...q,
        paper_id: paperId,
      }));

      // 删除当前试题的所有题目，然后重新插入（根据是全量更新还是增量更新）
      if (requestData.replaceAll) {
        const deleteStartTime = timeStart("删除现有题目");
        const { error: deleteError } = await supabase
          .from("use_of_english_questions")
          .delete()
          .eq("paper_id", paperId);
        timeEnd("删除现有题目", deleteStartTime);

        if (deleteError) {
          throw deleteError;
        }
      }

      // 插入新题目
      const insertStartTime = timeStart("插入新题目");
      const { data, error: insertError } = await supabase
        .from("use_of_english_questions")
        .upsert(questions, { onConflict: "paper_id,question_number" })
        .select();
      timeEnd("插入新题目", insertStartTime);

      if (insertError) {
        throw insertError;
      }

      // 清除缓存
      Object.keys(cache).forEach((key) => {
        if (key.startsWith(`use_of_english_questions_${paperId}`)) {
          delete cache[key];
        }
      });

      // 返回成功响应
      timeEnd("POST /api/use-of-english/questions 总耗时", totalStartTime);
      return NextResponse.json({
        success: true,
        message: "Questions saved successfully",
        data: data,
      });
    }
    // 处理单个题目
    else {
      // 验证必填字段
      if (!requestData.question_number || !requestData.options) {
        timeEnd("POST /api/use-of-english/questions 总耗时", totalStartTime);
        return NextResponse.json(
          {
            success: false,
            error: "Missing required fields: question_number, options",
          },
          { status: 400 }
        );
      }

      // 插入或更新题目
      const upsertStartTime = timeStart("更新题目");
      const { data, error: upsertError } = await supabase
        .from("use_of_english_questions")
        .upsert({
          paper_id: paperId,
          question_number: requestData.question_number,
          paragraph_number: requestData.paragraph_number || 1,
          options: requestData.options,
          correct_answer: requestData.correct_answer || null,
          explanation: requestData.explanation || null,
        })
        .select();
      timeEnd("更新题目", upsertStartTime);

      if (upsertError) {
        throw upsertError;
      }

      // 清除缓存
      Object.keys(cache).forEach((key) => {
        if (key.startsWith(`use_of_english_questions_${paperId}`)) {
          delete cache[key];
        }
      });

      // 返回成功响应
      timeEnd("POST /api/use-of-english/questions 总耗时", totalStartTime);
      return NextResponse.json({
        success: true,
        message: "Question saved successfully",
        data: data && data.length > 0 ? data[0] : null,
      });
    }
  } catch (err) {
    console.error("保存完形填空题目错误:", err);
    timeEnd("POST /api/use-of-english/questions 总耗时", totalStartTime);
    return NextResponse.json(
      {
        success: false,
        error: err instanceof Error ? err.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
