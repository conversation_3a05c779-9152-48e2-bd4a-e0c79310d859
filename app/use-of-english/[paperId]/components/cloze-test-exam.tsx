"use client";

import { useState, useEffect } from "react";
import {
  ChevronRight,
  ChevronLeft,
  Check,
  X,
  Loader2,
  <PERSON><PERSON>eft,
  AlertCircle,
} from "lucide-react";
import Link from "next/link";

// 类型定义
interface Paper {
  id: number;
  year: string;
  type: string;
  section_type: string;
  content?: string;
}

interface UseOfEnglishText {
  id: number;
  paper_id: number;
  original_text: string;
  text_analysis: string;
}

interface UseOfEnglishQuestion {
  id: number;
  paper_id: number;
  question_number: number;
  options: {
    A: { text: string; translation?: string };
    B: { text: string; translation?: string };
    C: { text: string; translation?: string };
    D: { text: string; translation?: string };
  };
  correct_answer: "A" | "B" | "C" | "D" | null;
  explanation: string;
}

interface ExamData {
  paper: Paper;
  text: UseOfEnglishText;
  questions: UseOfEnglishQuestion[];
}

export const UseClozeTestExam = ({ paperId }: { paperId: number }) => {
  const [data, setData] = useState<ExamData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [userAnswers, setUserAnswers] = useState<Record<number, string>>({});
  const [showExplanation, setShowExplanation] = useState<boolean>(false);
  const [submittedAnswers, setSubmittedAnswers] = useState<boolean>(false);
  const [processedText, setProcessedText] = useState<string>("");

  // 获取完形填空数据
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/use-of-english/${paperId}`);

        if (!response.ok) {
          let errorText = "获取数据失败";
          let errorDetails = "";
          try {
            const errorData = await response.json();
            errorText = errorData.error || errorText;
            errorDetails = errorData.details || "";
          } catch (e) {
            console.error("解析错误响应失败", e);
          }

          if (response.status === 404) {
            throw new Error(
              `${errorText}${errorDetails ? `\n详情: ${errorDetails}` : ""}`
            );
          } else {
            throw new Error(errorText);
          }
        }

        const responseData = await response.json();

        if (!responseData.success || !responseData.data) {
          throw new Error(responseData.error || "获取数据失败");
        }

        setData(responseData.data);
      } catch (err) {
        setError(err instanceof Error ? err.message : "未知错误");
        console.error("获取完形填空数据出错:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [paperId]);

  // 处理文本中的空白处
  useEffect(() => {
    if (data?.text?.original_text) {
      let text = data.text.original_text;
      let hasProcessedNumbers = false;

      // 预处理：识别文本中的完形填空空缺
      if (data.questions && data.questions.length > 0) {
        // 根据题目编号处理文本
        const questionNumbers = data.questions.map((q) => q.question_number);

        // 检查文本中是否已经有 __数字__ 格式的标记
        const hasMarkedBlanks = /__\d+__/.test(text);

        // 如果没有预先标记的空白，则尝试识别并标记单个数字
        if (!hasMarkedBlanks) {
          // 特殊处理完形填空格式：将单独的数字替换为更明显的标记
          questionNumbers.forEach((qNum) => {
            // 为每个数字创建一个特定的正则表达式模式
            // 这个模式更严格地匹配独立的数字（确保只匹配空缺数字）

            // 1. 匹配前后都是空格的情况：例如 "word 1 word"
            const spacePattern = new RegExp(`(\\s)(${qNum})(\\s)`, "g");
            text = text.replace(spacePattern, (match, before, num, after) => {
              hasProcessedNumbers = true;
              return `${before}__${num}__${after}`;
            });

            // 2. 匹配前面是标点、后面是空格的情况：例如 "word, 1 word"
            const punctSpacePattern = new RegExp(`([.,:;])(${qNum})(\\s)`, "g");
            text = text.replace(
              punctSpacePattern,
              (match, before, num, after) => {
                hasProcessedNumbers = true;
                return `${before}__${num}__${after}`;
              }
            );

            // 3. 匹配前面是空格、后面是标点的情况：例如 "word 1, word"
            const spacePunctPattern = new RegExp(`(\\s)(${qNum})([.,:;])`, "g");
            text = text.replace(
              spacePunctPattern,
              (match, before, num, after) => {
                hasProcessedNumbers = true;
                return `${before}__${num}__${after}`;
              }
            );

            // 4. 匹配行首的数字：例如 "1 word"
            const startPattern = new RegExp(`(^|\\n)(${qNum})(\\s)`, "g");
            text = text.replace(startPattern, (match, before, num, after) => {
              hasProcessedNumbers = true;
              return `${before}__${num}__${after}`;
            });

            // 5. 匹配行尾的数字：例如 "word 1"（行尾）
            const endPattern = new RegExp(`(\\s)(${qNum})($|\\n)`, "g");
            text = text.replace(endPattern, (match, before, num, after) => {
              hasProcessedNumbers = true;
              return `${before}__${num}__${after}`;
            });
          });

          // 如果上面的正则表达式没有匹配任何数字，使用更激进的方法匹配数字
          if (!hasProcessedNumbers) {
            // 简单地将数字用简单的正则表达式匹配
            questionNumbers.forEach((qNum) => {
              // 直接匹配所有单独的数字，但只匹配20以内的数字以避免匹配年份等
              if (qNum > 0 && qNum <= 20) {
                const simplePattern = new RegExp(`\\b${qNum}\\b`, "g");
                text = text.replace(simplePattern, `__${qNum}__`);
              }
            });
          }
        }
      }

      // 如果已提交答案，使用用户选择的答案替换文本
      if (submittedAnswers) {
        data.questions.forEach((question) => {
          const userAnswer = userAnswers[question.question_number];
          if (userAnswer) {
            // 使用类型断言修复索引问题
            const option = userAnswer as keyof typeof question.options;
            // 检查用户答案是否正确
            const isCorrect = userAnswer === question.correct_answer;
            // 使用问题编号直接替换对应标记
            const pattern = new RegExp(`__${question.question_number}__`, "g");

            // 根据答案的正确性使用不同的样式
            if (isCorrect) {
              // 用户答案正确 - 使用绿色样式
              text = text.replace(
                pattern,
                `<span class="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 border border-green-200 rounded-md font-medium"><svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>${question.options[option].text}</span>`
              );
            } else {
              // 用户答案错误 - 使用红色样式显示用户答案，并添加正确答案
              if (question.correct_answer) {
                const correctOption =
                  question.correct_answer as keyof typeof question.options;
                text = text.replace(
                  pattern,
                  `<span class="inline-flex items-center gap-1 px-2 py-1 bg-red-100 text-red-800 border border-red-200 rounded-md font-medium"><svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>${question.options[option].text}</span><span class="inline-flex items-center ml-1 px-1.5 py-0.5 bg-green-50 text-green-700 text-xs rounded-md"><svg class="w-3 h-3 mr-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>${question.options[correctOption].text}</span>`
                );
              } else {
                // 如果没有正确答案，只显示用户答案
                text = text.replace(
                  pattern,
                  `<span class="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 text-gray-800 border border-gray-200 rounded-md font-medium">${question.options[option].text}</span>`
                );
              }
            }
          } else if (question.correct_answer) {
            // 用户没有选择答案但有正确答案 - 显示为未解答
            const correctOption =
              question.correct_answer as keyof typeof question.options;
            const pattern = new RegExp(`__${question.question_number}__`, "g");
            text = text.replace(
              pattern,
              `<span class="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 text-gray-600 border border-gray-200 rounded-md font-medium italic">未解答</span><span class="inline-flex items-center ml-1 px-1.5 py-0.5 bg-green-50 text-green-700 text-xs rounded-md"><svg class="w-3 h-3 mr-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>${question.options[correctOption].text}</span>`
            );
          }
        });
      } else {
        // 将所有 __数字__ 格式替换为美观的样式
        text = text.replace(/__(\d+)__/g, (match, num) => {
          return `<span class="inline-flex justify-center items-center min-w-8 h-6 px-2 bg-blue-50 text-blue-700 border border-blue-200 rounded-md font-medium">___${num}___</span>`;
        });
      }

      setProcessedText(text);
    }
  }, [data, submittedAnswers, userAnswers]);

  // 处理用户选择答案
  const handleSelectAnswer = (questionNumber: number, option: string) => {
    if (submittedAnswers) return; // 如果已提交，不允许再更改

    setUserAnswers({
      ...userAnswers,
      [questionNumber]: option,
    });
  };

  // 提交答案
  const handleSubmitAnswers = () => {
    setSubmittedAnswers(true);
    setShowExplanation(true);
  };

  // 重置答案
  const handleReset = () => {
    setUserAnswers({});
    setSubmittedAnswers(false);
    setShowExplanation(false);
  };

  // 计算得分
  const calculateScore = () => {
    if (!data?.questions) return 0;

    let correctCount = 0;
    data.questions.forEach((question) => {
      if (
        question.correct_answer &&
        userAnswers[question.question_number] === question.correct_answer
      ) {
        correctCount++;
      }
    });

    return correctCount;
  };

  // 渲染加载状态
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <Loader2 className="h-8 w-8 text-blue-600 animate-spin mb-4" />
        <p className="text-gray-600">正在加载完形填空内容...</p>
      </div>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <div className="bg-white rounded-xl shadow p-8 text-center">
        <p className="text-red-500 mb-4 whitespace-pre-line">{error}</p>
        <div className="flex flex-col sm:flex-row gap-3 justify-center mt-6">
          <Link
            href="/exam-library"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center justify-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            返回真题库
          </Link>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 flex items-center justify-center gap-2"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  // 渲染无数据状态
  if (!data) {
    return (
      <div className="bg-white rounded-xl shadow p-8 text-center">
        <p className="text-gray-500 mb-4">未找到完形填空数据</p>
        <Link
          href="/exam-library"
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2 w-fit mx-auto"
        >
          <ArrowLeft className="w-4 h-4" />
          返回真题库
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 返回链接和标题 */}
      <div className="flex flex-col gap-2">
        <Link
          href="/exam-library"
          className="text-blue-600 hover:text-blue-800 flex items-center gap-1 w-fit"
        >
          <ArrowLeft className="w-4 h-4" />
          返回真题库
        </Link>
        <h1 className="text-2xl font-bold text-gray-800">
          {data.paper.year}年{data.paper.type}真题 - 完形填空
        </h1>
      </div>

      {/* 考试内容区 */}
      <div className="grid md:grid-cols-5 gap-6">
        {/* 左侧：原文 */}
        <div className="md:col-span-3 bg-white rounded-xl shadow p-6">
          <h2 className="text-lg font-semibold mb-4 flex items-center">
            <span className="bg-blue-100 text-blue-800 h-6 w-6 rounded-full inline-flex justify-center items-center mr-2">
              A
            </span>
            完形填空原文
          </h2>
          {!submittedAnswers && (
            <div className="bg-blue-50 text-blue-700 px-4 py-2 rounded-lg mb-4 text-sm flex items-center">
              <span className="mr-2">📝</span>
              <span>
                文中的<span className="font-medium">___数字___</span>
                表示需要填空的位置，请在右侧选择最合适的答案。
              </span>
            </div>
          )}
          {submittedAnswers && (
            <div className="bg-blue-50 text-blue-700 px-4 py-3 rounded-lg mb-4 text-sm">
              <div className="flex items-center mb-2">
                <span className="mr-2">🔍</span>
                <span className="font-medium">答案解析：</span>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-2 pl-6">
                <div className="flex items-center">
                  <span className="inline-flex items-center justify-center h-5 w-5 rounded-full bg-green-100 text-green-700 mr-2">
                    <svg
                      className="h-3 w-3"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M5 13l4 4L19 7"
                      ></path>
                    </svg>
                  </span>
                  <span className="text-gray-700">绿色答案表示回答正确</span>
                </div>
                <div className="flex items-center">
                  <span className="inline-flex items-center justify-center h-5 w-5 rounded-full bg-red-100 text-red-700 mr-2">
                    <svg
                      className="h-3 w-3"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M6 18L18 6M6 6l12 12"
                      ></path>
                    </svg>
                  </span>
                  <span className="text-gray-700">红色答案表示回答错误</span>
                </div>
                <div className="flex items-center">
                  <span className="inline-flex items-center justify-center h-5 w-5 rounded-full bg-gray-100 text-gray-500 mr-2 italic">
                    ?
                  </span>
                  <span className="text-gray-700">灰色表示未作答</span>
                </div>
              </div>
            </div>
          )}
          <div
            className="prose max-w-none text-gray-800 leading-relaxed text-base tracking-wide space-y-4"
            dangerouslySetInnerHTML={{ __html: processedText }}
          />

          {/* 全文解析，仅在提交后显示 */}
          {submittedAnswers && data.text.text_analysis && (
            <div className="mt-8 pt-6 border-t border-gray-200">
              <h3 className="text-lg font-semibold mb-4">全文解析</h3>
              <div
                className="prose max-w-none text-gray-700"
                dangerouslySetInnerHTML={{
                  __html: data.text.text_analysis.replace(/\n/g, "<br/>"),
                }}
              />
            </div>
          )}
        </div>

        {/* 右侧：题目 */}
        <div className="md:col-span-2 bg-white rounded-xl shadow p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">选项与答案</h2>
            {submittedAnswers ? (
              <div className="flex items-center gap-2">
                <span className="text-blue-700 font-medium">
                  得分: {calculateScore()} / {data.questions.length}
                </span>
                <button
                  onClick={handleReset}
                  className="px-3 py-1.5 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 text-sm"
                >
                  重做
                </button>
              </div>
            ) : (
              <button
                onClick={handleSubmitAnswers}
                disabled={
                  Object.keys(userAnswers).length <
                  (data.questions?.length || 0)
                }
                className={`px-3 py-1.5 rounded text-sm ${
                  Object.keys(userAnswers).length <
                  (data.questions?.length || 0)
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-blue-600 text-white hover:bg-blue-700"
                }`}
              >
                提交答案
              </button>
            )}
          </div>

          {/* 选择题列表 */}
          <div className="space-y-6 max-h-[calc(100vh-260px)] overflow-y-auto pr-2">
            {data.questions.map((question) => {
              const userAnswer = userAnswers[question.question_number];
              const isCorrect =
                submittedAnswers &&
                question.correct_answer &&
                userAnswer === question.correct_answer;
              const isWrong =
                submittedAnswers &&
                userAnswer &&
                question.correct_answer &&
                userAnswer !== question.correct_answer;

              return (
                <div
                  key={question.id}
                  className="border border-gray-200 rounded-lg p-4"
                >
                  <div className="flex justify-between items-center mb-3">
                    <h3 className="font-medium text-gray-800">
                      {question.question_number}.
                      {submittedAnswers && question.correct_answer && (
                        <span
                          className={`ml-2 ${isCorrect ? "text-green-600" : "text-red-500"}`}
                        >
                          {isCorrect ? (
                            <Check className="inline w-4 h-4" />
                          ) : (
                            <X className="inline w-4 h-4" />
                          )}
                        </span>
                      )}
                    </h3>
                    {submittedAnswers &&
                      question.correct_answer &&
                      !isCorrect && (
                        <span className="text-sm text-green-600">
                          正确答案: {question.correct_answer}
                        </span>
                      )}
                  </div>

                  {/* 选项 */}
                  <div className="grid grid-cols-2 gap-2">
                    {Object.entries(question.options).map(([option, value]) => {
                      const isUserSelected = userAnswer === option;
                      const isCorrectOption =
                        submittedAnswers && question.correct_answer === option;

                      let optionClass =
                        "border text-sm rounded-md px-3 py-2 flex items-center gap-2 cursor-pointer";

                      if (submittedAnswers) {
                        if (isCorrectOption) {
                          optionClass +=
                            " bg-green-50 border-green-200 text-green-800";
                        } else if (isUserSelected) {
                          optionClass +=
                            " bg-red-50 border-red-200 text-red-800";
                        } else {
                          optionClass +=
                            " bg-gray-50 border-gray-200 text-gray-600";
                        }
                      } else {
                        optionClass += isUserSelected
                          ? " bg-blue-50 border-blue-300 text-blue-800"
                          : " hover:bg-gray-50 border-gray-200 text-gray-700";
                      }

                      return (
                        <div
                          key={option}
                          className={optionClass}
                          onClick={() =>
                            handleSelectAnswer(question.question_number, option)
                          }
                        >
                          <span
                            className={`inline-flex justify-center items-center rounded-full h-5 w-5 text-xs font-medium ${
                              isUserSelected
                                ? submittedAnswers
                                  ? isCorrectOption
                                    ? "bg-green-600 text-white"
                                    : "bg-red-600 text-white"
                                  : "bg-blue-600 text-white"
                                : "bg-gray-200 text-gray-700"
                            }`}
                          >
                            {option}
                          </span>
                          <span>
                            {value.text}
                            {value.translation && (
                              <span className="text-xs text-gray-500 ml-1">
                                ({value.translation})
                              </span>
                            )}
                          </span>
                        </div>
                      );
                    })}
                  </div>

                  {/* 解析，仅在提交后显示 */}
                  {submittedAnswers && question.explanation && (
                    <div className="mt-3 pt-3 border-t border-gray-100">
                      <div className="text-sm text-gray-700">
                        <span className="font-medium text-gray-900">
                          解析:{" "}
                        </span>
                        {question.explanation}
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};
