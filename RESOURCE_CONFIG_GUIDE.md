# 学习资源配置维护指南

本指南详细说明如何维护和增加 `learning-resources-config.ts` 中的配置内容。

## 📋 配置结构说明

### 1. ResourceTypeConfig 接口

```typescript
export interface ResourceTypeConfig {
  id: string; // 唯一标识符
  label: string; // 显示名称
  icon: string; // 图标（emoji）
  needsApi: boolean; // 是否需要调用API获取资源
  defaultSection?: string; // 默认选择的section
  description?: string; // 描述信息
  specialComponent?: string; // 特殊组件类型
  sections?: string[]; // 可用的sections列表
}
```

### 2. 图标和样式配置

配置文件中还包含以下样式和图标配置，实现了UI展示的统一管理：

```typescript
// 分类图标配置 - 为每个资源分类定义图标
export const CATEGORY_ICONS: Record<string, string> = {
  考研英语一: "🎯",
  考研英语二: "📚",
  考研政治: "📜",
  // ... 更多配置
};

// Section图标配置 - 为每个section定义图标
export const SECTION_ICONS: Record<string, string> = {
  真题: "📄",
  答案解析: "✏️",
  音频: "🎵",
  // ... 更多配置
};

// 分类样式配置 - 为每个资源分类定义Tailwind CSS样式
export const CATEGORY_STYLES: Record<string, string> = {
  考研英语一:
    "bg-blue-50 text-blue-600 border border-blue-100 hover:bg-blue-100",
  考研英语二:
    "bg-indigo-50 text-indigo-600 border border-indigo-100 hover:bg-indigo-100",
  // ... 更多配置
};

// Section样式配置 - 为每个section定义Tailwind CSS样式
export const SECTION_STYLES: Record<string, string> = {
  真题: "bg-green-50 text-green-600 border border-green-100 hover:bg-green-100",
  答案解析:
    "bg-yellow-50 text-yellow-700 border border-yellow-100 hover:bg-yellow-100",
  // ... 更多配置
};
```

## 🔧 维护操作示例

### 1. 为现有资源类型添加新的 Section

**场景：** 为考研英语一添加"视频教程"和"模拟试卷"

```typescript
// 步骤1：在 RESOURCE_PATHS 中添加路径映射
kaoyan1: {
  // ... 现有配置
  视频教程: "kaoyan/English1/videoTutorials",    // 新增
  模拟试卷: "kaoyan/English1/mockExams",        // 新增
},

// 步骤2：在 RESOURCE_TYPE_CONFIGS 中更新 sections
{
  id: "kaoyan1",
  // ... 其他配置
  sections: [
    "真题", "答案解析", "手译本",
    "高频词汇与短语搭配", "核心词汇大纲",
    "写作模板与句型表达", "专项训练资料",
    "视频教程", "模拟试卷"  // 新增的sections
  ]
}
```

### 2. 添加全新的资源类型

**场景：** 添加雅思考试资源

```typescript
// 步骤1：在 RESOURCE_PATHS 中添加完整配置
ielts: {
  真题: "ielts/pastExamPapers",
  答案解析: "ielts/Explanation",
  听力材料: "ielts/listeningMaterials",
  口语话题: "ielts/speakingTopics",
  写作范文: "ielts/writingSamples",
  词汇手册: "ielts/vocabulary",
},

// 步骤2：在 RESOURCE_TYPE_CONFIGS 中添加配置
{
  id: "ielts",
  label: "雅思考试",
  icon: "🌍",
  needsApi: true,
  defaultSection: "真题",
  category: "exam",
  isNew: true,  // 标记为新增
  description: "提供雅思考试全套备考资料...",
  sections: ["真题", "答案解析", "听力材料", "口语话题", "写作范文", "词汇手册"]
},

// 步骤3：在 TYPE_CATEGORY_MAP 中添加映射
ielts: "雅思考试",
```

### 3. 添加特殊组件类型

**场景：** 添加在线学习工具（不需要API调用）

```typescript
{
  id: "online_tools",
  label: "在线学习工具",
  icon: "🛠️",
  needsApi: false,              // 不需要API
  specialComponent: "custom",   // 特殊组件类型
  description: "提供各种在线学习工具：单词记忆、公式计算器..."
}
```

### 4. 添加新的图标和样式配置

**场景：** 为新增的资源类型添加图标和样式

```typescript
// 在 CATEGORY_ICONS 中添加
"雅思考试": "🌍",
"托福考试": "🇺🇸",

// 在 SECTION_ICONS 中添加
"口语话题": "🗣️",
"写作范文": "📝",

// 在 CATEGORY_STYLES 中添加
"雅思考试": "bg-green-50 text-green-600 border border-green-100 hover:bg-green-100",
"托福考试": "bg-blue-50 text-blue-600 border border-blue-100 hover:bg-blue-100",

// 在 SECTION_STYLES 中添加
"口语话题": "bg-orange-50 text-orange-600 border border-orange-100 hover:bg-orange-100",
"写作范文": "bg-purple-50 text-purple-600 border border-purple-100 hover:bg-purple-100",
```

### 5. 修改现有资源的描述信息

**场景：** 更新考研答题卡的描述

```typescript
{
  id: "kaoyan_answer_sheet",
  // ... 其他配置保持不变
  description: "更新后的描述信息：提供各科目考研机读答题卡PDF打印模板...",
}
```

## 🎯 specialComponent 类型说明

### 可用的特殊组件类型：

- `"print"`: 打印服务组件
- `"custom"`: 自定义组件

## ⚡ 快速维护清单

### ✅ 添加新资源类型的完整步骤：

1. **添加路径映射** - 在 `RESOURCE_PATHS` 中定义存储路径
2. **添加类型配置** - 在 `RESOURCE_TYPE_CONFIGS` 中添加完整配置
3. **添加类型映射** - 在 `TYPE_CATEGORY_MAP` 中添加向后兼容映射
4. **测试验证** - 确保新配置在页面中正常显示

### ✅ 修改现有资源的步骤：

1. **修改路径映射** - 如需新增section，在对应资源的 `RESOURCE_PATHS` 中添加
2. **更新配置** - 在 `RESOURCE_TYPE_CONFIGS` 中更新对应的配置项
3. **验证修改** - 确保修改后的配置正常工作

## 🔍 辅助函数使用

配置文件提供了多个辅助函数来方便获取和筛选资源：

```typescript
// 根据ID获取资源配置
const config = getResourceTypeConfig("ielts");

// 获取所有需要API的资源类型
const apiTypes = getApiResourceTypes();

// 获取特殊组件类型
const specialTypes = getSpecialResourceTypes();

// 获取图标和样式
const categoryIcon = getCategoryIcon("考研英语一");
const sectionIcon = getSectionIcon("真题");
const categoryStyle = getCategoryStyles("考研英语一");
const sectionStyle = getSectionStyles("真题");
```

## 🚨 注意事项

1. **路径一致性**: 确保 `RESOURCE_PATHS` 中的路径与实际存储路径一致
2. **ID唯一性**: 每个资源类型的 `id` 必须唯一
3. **向后兼容**: 修改现有配置时要考虑向后兼容性
4. **描述信息**: 为用户友好的资源类型添加详细的 `description`
5. **图标样式**: 新增分类或section时，记得在对应的图标和样式配置中添加相应条目
6. **样式一致性**: 使用统一的Tailwind CSS类名规范，保持UI风格一致

## 📝 示例：完整添加一个新资源类型

```typescript
// 1. 添加路径映射
civil_service: {
  行测真题: "civilService/aptitudeTest",
  申论真题: "civilService/essay",
  面试指导: "civilService/interviewGuidance",
  时政资料: "civilService/currentAffairs",
},

// 2. 添加类型配置
{
  id: "civil_service",
  label: "公务员考试",
  icon: "🏛️",
  needsApi: true,
  defaultSection: "行测真题",
  description: "公务员考试备考资料大全，包含行测、申论真题及解析，面试指导和时政资料。",
  sections: ["行测真题", "申论真题", "面试指导", "时政资料"]
},

// 3. 添加类型映射
civil_service: "公务员考试",

// 4. 添加图标和样式配置
// 在 CATEGORY_ICONS 中添加
"公务员考试": "🏛️",

// 在 SECTION_ICONS 中添加
"行测真题": "📊",
"申论真题": "📝",
"面试指导": "🎤",
"时政资料": "📰",

// 在 CATEGORY_STYLES 中添加
"公务员考试": "bg-slate-50 text-slate-600 border border-slate-100 hover:bg-slate-100",

// 在 SECTION_STYLES 中添加
"行测真题": "bg-blue-50 text-blue-600 border border-blue-100 hover:bg-blue-100",
"申论真题": "bg-green-50 text-green-600 border border-green-100 hover:bg-green-100",
"面试指导": "bg-purple-50 text-purple-600 border border-purple-100 hover:bg-purple-100",
"时政资料": "bg-orange-50 text-orange-600 border border-orange-100 hover:bg-orange-100",
```

通过以上配置，系统会自动：

- 在页面上显示新的资源类型选项
- 根据 `needsApi` 决定是否调用API
- 根据 `sections` 显示筛选选项
- 根据 `description` 显示说明信息
- 根据 `isNew` 标记新增资源

这样的配置化设计让资源管理变得非常灵活和易于维护！
