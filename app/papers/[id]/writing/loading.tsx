import { Skeleton } from "@/app/components/ui/skeleton";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";

export default function Loading() {
  return (
    <div className="container mx-auto py-10 px-4 sm:px-6 max-w-5xl">
      {/* 标题骨架 */}
      <div className="mb-10">
        {/* 返回链接骨架 */}
        <Skeleton className="h-4 w-24 mb-6" />

        {/* 头部信息卡片骨架 */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl shadow-sm mb-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="w-full md:w-3/4">
              <div className="flex gap-2 mb-2">
                <Skeleton className="h-6 w-16" />
                <Skeleton className="h-6 w-16" />
              </div>
              <Skeleton className="h-10 w-48 mb-3" />
              <Skeleton className="h-4 w-full max-w-2xl" />
              <Skeleton className="h-4 w-full max-w-2xl mt-2" />
            </div>
            <div className="hidden md:block">
              <Skeleton className="h-16 w-16 rounded-full" />
            </div>
          </div>
        </div>

        {/* 大小作文选项卡骨架 */}
        <div className="bg-white rounded-xl shadow-sm p-1 mb-8">
          <Skeleton className="h-14 w-full rounded-lg" />
        </div>

        {/* 内容选项卡骨架 */}
        <div className="bg-white rounded-xl p-1 shadow-sm mb-6">
          <Skeleton className="h-12 w-full rounded-lg" />
        </div>

        {/* 内容骨架 */}
        <Card className="border-0 shadow-md overflow-hidden">
          <CardHeader className="bg-blue-50 border-b border-blue-100 pb-3">
            <Skeleton className="h-7 w-32" />
            <Skeleton className="h-4 w-48 mt-1" />
          </CardHeader>
          <CardContent className="p-6 space-y-6">
            <Skeleton className="h-40 w-full rounded-lg" />
            <Skeleton className="h-96 w-full rounded-lg" />
            <Skeleton className="h-16 w-full rounded-lg" />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
