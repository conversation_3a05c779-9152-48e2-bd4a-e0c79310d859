import { createClient } from "@supabase/supabase-js";
import { NextResponse } from "next/server";

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// GET请求处理函数 - 获取指定路径下的文件和文件夹
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const path = searchParams.get("path") || "";

    // 获取文件列表
    const { data: files, error: filesError } = await supabase.storage
      .from("mbdata")
      .list(path, {
        limit: 1000,
        offset: 0,
        sortBy: { column: "name", order: "asc" },
      });

    if (filesError) {
      console.error("获取文件列表失败:", filesError);
      return NextResponse.json(
        { error: "获取文件列表失败", details: filesError.message },
        { status: 500 }
      );
    }

    // 分离文件和文件夹
    const folders: string[] = [];
    const fileList = [];

    if (files) {
      for (const item of files) {
        if (item.id === null) {
          // 这是一个文件夹
          folders.push(item.name);
        } else {
          // 这是一个文件
          // 获取文件的公开URL
          const { data: urlData } = supabase.storage
            .from("mbdata")
            .getPublicUrl(path ? `${path}/${item.name}` : item.name);

          fileList.push({
            id: item.id,
            name: item.name,
            size: item.metadata?.size || 0,
            type: item.metadata?.mimetype || "application/octet-stream",
            created_at: item.created_at,
            updated_at: item.updated_at,
            url: urlData.publicUrl,
          });
        }
      }
    }

    return NextResponse.json({
      files: fileList,
      folders: folders.sort(),
      path: path,
    });
  } catch (error) {
    console.error("获取资源列表出错:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
