import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { Database } from "@/types/supabase";
import { timeStart, timeEnd } from "@/utils/performance";

// 创建Supabase客户端
const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL || "",
  process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || ""
);

// 查询缓存
const cache: Record<string, { data: any; timestamp: number }> = {};
const CACHE_TTL = 60 * 1000; // 缓存有效期1分钟

// GET方法：获取完形填空文本
export async function GET(request: NextRequest) {
  const totalStartTime = timeStart("GET /api/use-of-english/texts 总耗时");
  try {
    // 获取查询参数
    const searchParams = request.nextUrl.searchParams;
    const paperId = searchParams.get("paperId");

    // 验证必须提供paperId
    if (!paperId) {
      timeEnd("GET /api/use-of-english/texts 总耗时", totalStartTime);
      return NextResponse.json(
        {
          success: false,
          error: "Missing required parameter: paperId",
        },
        { status: 400 }
      );
    }

    // 尝试从缓存中获取数据
    const cacheKey = `use_of_english_texts_${paperId}`;
    const now = Date.now();

    if (cache[cacheKey] && now - cache[cacheKey].timestamp < CACHE_TTL) {
      timeEnd("GET /api/use-of-english/texts 总耗时", totalStartTime);
      return NextResponse.json({
        success: true,
        data: cache[cacheKey].data,
        fromCache: true,
      });
    }

    // 查询数据库
    const queryStartTime = timeStart("查询use_of_english_texts数据");
    const { data, error } = await supabase
      .from("use_of_english_texts")
      .select("*")
      .eq("paper_id", paperId)
      .order("paragraph_number", { ascending: true });
    timeEnd("查询use_of_english_texts数据", queryStartTime);

    if (error) {
      throw error;
    }

    // 更新缓存
    cache[cacheKey] = {
      data: data || [],
      timestamp: now,
    };

    // 返回成功响应
    timeEnd("GET /api/use-of-english/texts 总耗时", totalStartTime);
    return NextResponse.json({
      success: true,
      data: data || [],
    });
  } catch (err) {
    console.error("获取完形填空文本错误:", err);
    timeEnd("GET /api/use-of-english/texts 总耗时", totalStartTime);
    return NextResponse.json(
      {
        success: false,
        error: err instanceof Error ? err.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// POST方法：保存完形填空文本
export async function POST(request: NextRequest) {
  const totalStartTime = timeStart("POST /api/use-of-english/texts 总耗时");
  try {
    // 获取请求数据
    const bodyStartTime = timeStart("解析请求体");
    const requestData = await request.json();
    timeEnd("解析请求体", bodyStartTime);

    // 验证必填字段
    if (!requestData.paper_id || !requestData.original_text) {
      timeEnd("POST /api/use-of-english/texts 总耗时", totalStartTime);
      return NextResponse.json(
        {
          success: false,
          error: "Missing required fields: paper_id or original_text",
        },
        { status: 400 }
      );
    }

    const paperId = requestData.paper_id;
    const originalText = requestData.original_text;

    // 删除当前试题的所有段落，然后重新插入
    const deleteStartTime = timeStart("删除现有段落");
    const { error: deleteError } = await supabase
      .from("use_of_english_texts")
      .delete()
      .eq("paper_id", paperId);
    timeEnd("删除现有段落", deleteStartTime);

    if (deleteError) {
      throw deleteError;
    }

    // 插入新文本
    const insertStartTime = timeStart("插入新文本");
    const { data, error: insertError } = await supabase
      .from("use_of_english_texts")
      .insert({
        paper_id: paperId,
        original_text: originalText,
        paragraph_number: 1, // 默认为第一段
      })
      .select();
    timeEnd("插入新文本", insertStartTime);

    if (insertError) {
      throw insertError;
    }

    // 清除缓存
    const cacheKey = `use_of_english_texts_${paperId}`;
    delete cache[cacheKey];

    // 返回成功响应
    timeEnd("POST /api/use-of-english/texts 总耗时", totalStartTime);
    return NextResponse.json({
      success: true,
      message: "Text saved successfully",
      data: data,
    });
  } catch (err) {
    console.error("保存完形填空文本错误:", err);
    timeEnd("POST /api/use-of-english/texts 总耗时", totalStartTime);
    return NextResponse.json(
      {
        success: false,
        error: err instanceof Error ? err.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
