import { ArrowUpRight } from "lucide-react";
import { RiInformationLine } from "react-icons/ri";
import Link from "next/link";

export function SmtpMessage() {
  return (
    <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border rounded-md flex gap-4 mt-4 shadow-sm">
      <RiInformationLine size={18} className="text-blue-500 mt-0.5" />
      <div className="flex flex-col gap-1">
        <small className="text-sm text-gray-600 dark:text-gray-300">
          <strong>注意：</strong> 邮件发送受限制。启用自定义SMTP可提高发送频率。
        </small>
        <div>
          <Link
            href="https://supabase.com/docs/guides/auth/auth-smtp"
            target="_blank"
            className="text-blue-500 hover:text-blue-600 flex items-center text-sm gap-1"
          >
            了解更多 <ArrowUpRight size={14} />
          </Link>
        </div>
      </div>
    </div>
  );
}
