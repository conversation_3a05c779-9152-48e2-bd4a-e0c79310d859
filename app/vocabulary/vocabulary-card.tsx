"use client";

import { Vocabulary } from "@/types/vocabulary";
import { useState } from "react";
import { ChevronRight, BookO<PERSON>, Lightbulb } from "lucide-react";
import Link from "next/link";
import { Markdown } from "@/app/components/markdown";

export default function VocabularyCard({ word }: { word: Vocabulary }) {
  const [showDetails, setShowDetails] = useState(false);

  return (
    <div className="border rounded-lg overflow-hidden bg-white shadow-sm hover:shadow-md transition-shadow">
      <div className="p-3">
        {/* 单词标题区域 */}
        <div className="flex justify-between items-start">
          <div className="flex-grow">
            <div className="flex items-center">
              <h3 className="text-lg font-bold text-gray-800">{word.word}</h3>
              {word.pronunciation && (
                <span className="ml-1.5 text-xs text-gray-500">
                  /{word.pronunciation}/
                </span>
              )}
            </div>
            <p className="text-sm text-gray-600 mt-0.5 line-clamp-1">
              {word.simple_translation}
            </p>
          </div>
          <Link
            href={`/vocabulary/${word.word}`}
            className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-50 flex items-center justify-center hover:bg-blue-100 transition-colors ml-1"
          >
            <ChevronRight className="h-3.5 w-3.5 text-blue-600" />
          </Link>
        </div>

        {/* 切换详情区域 */}
        <div
          className={`pt-2 pb-1 border-t border-gray-100 cursor-pointer flex justify-between items-center text-sm text-gray-500 hover:text-blue-600 transition-colors ${showDetails ? "text-blue-600" : ""}`}
          onClick={() => setShowDetails(!showDetails)}
        >
          <div className="flex items-center">
            <BookOpen className="h-4 w-4 mr-1.5" />
            <span>{showDetails ? "收起详情" : "查看详情"}</span>
          </div>
        </div>

        {/* 详情区域 - 使用Markdown渲染 */}
        {showDetails && (
          <div className="mt-3 text-sm space-y-3">
            {word.analysis && (
              <div>
                <div className="flex items-center text-gray-600 mb-1.5">
                  <Lightbulb className="h-4 w-4 mr-1.5 text-amber-500" />
                  <span className="font-medium">单词分析</span>
                </div>
                <div className="ml-6 prose-sm prose-headings:my-1 prose-p:my-1 prose-p:text-gray-600 prose-hr:my-2">
                  <Markdown>{word.analysis}</Markdown>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
