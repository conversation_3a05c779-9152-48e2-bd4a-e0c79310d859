import { signUpAction } from "@/app/actions";
import { FormMessage, Message } from "@/components/form-message";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import Link from "next/link";
import { SmtpMessage } from "../smtp-message";
import { RiMailLine, RiLockPasswordLine, RiUserAddLine } from "react-icons/ri";

export default async function Signup(props: {
  searchParams: Promise<Message>;
}) {
  const searchParams = await props.searchParams;
  if ("message" in searchParams) {
    return (
      <div className="w-full flex-1 flex items-center justify-center gap-2 p-4">
        <FormMessage message={searchParams} />
      </div>
    );
  }

  return (
    <>
      <Card className="shadow-lg transition-all duration-300 hover:shadow-xl">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-2">
            <div className="rounded-full bg-green-100 p-3 text-green-600 dark:bg-green-900 dark:text-green-300">
              <RiUserAddLine size={24} />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-center">注册</CardTitle>
          <CardDescription className="text-center">
            已有账号？{" "}
            <Link
              className="text-blue-600 hover:underline dark:text-blue-400 font-medium"
              href="/sign-in"
            >
              登录
            </Link>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form className="flex flex-col gap-4">
            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium">
                邮箱
              </Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
                  <RiMailLine />
                </div>
                <Input
                  name="email"
                  id="email"
                  placeholder="<EMAIL>"
                  required
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="password" className="text-sm font-medium">
                密码
              </Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
                  <RiLockPasswordLine />
                </div>
                <Input
                  type="password"
                  name="password"
                  id="password"
                  placeholder="密码（至少6位）"
                  minLength={6}
                  required
                  className="pl-10"
                />
              </div>
            </div>

            <SubmitButton
              formAction={signUpAction}
              pendingText="注册中..."
              className="mt-2 w-full bg-blue-600 hover:bg-blue-700 text-white"
            >
              注册
            </SubmitButton>
            <FormMessage message={searchParams} />
          </form>
        </CardContent>
        <CardFooter className="flex justify-center border-t pt-4">
          <p className="text-xs text-gray-500">
            注册即表示您同意我们的服务条款和隐私政策
          </p>
        </CardFooter>
      </Card>
      {/* <SmtpMessage /> */}
    </>
  );
}
