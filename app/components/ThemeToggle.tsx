"use client";

import { useTheme } from "next-themes";
import { BsMoonStarsFill, BsSunFill } from "react-icons/bs";

const ThemeToggle = () => {
  const { theme, setTheme } = useTheme();

  return (
    <button
      onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
      className="hidden md:flex items-center justify-center h-8 w-8 rounded-md bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
      aria-label="切换主题"
    >
      {theme === "dark" ? (
        <BsSunFill className="h-4 w-4 text-yellow-500" />
      ) : (
        <BsMoonStarsFill className="h-4 w-4 text-gray-700" />
      )}
    </button>
  );
};

export default ThemeToggle;
