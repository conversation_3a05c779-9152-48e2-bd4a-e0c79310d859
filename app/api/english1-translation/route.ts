import { NextResponse } from "next/server";
import { DeepSeek } from "@/app/services/deepseek";
import { supabase } from "@/lib/db";

interface TranslationResult {
  reference_translation: string;
  difficulty_analysis: string | string[];
  translation_skills: string | string[];
  user_evaluation: string;
  user_score: number;
}

export async function POST(request: Request) {
  try {
    const { content, user_translation, sentence_id } = await request.json();

    if (!content || !sentence_id) {
      return NextResponse.json(
        { error: "内容和句子ID是必需的" },
        { status: 400 }
      );
    }

    // 调用AI进行翻译分析
    const prompt = `
你是一位精通英汉翻译的考研英语专家。请对以下英语句子进行翻译分析。

原句：
${content}

用户翻译：
${user_translation || "用户未提供翻译"}

请提供以下内容：
1. 准确、地道的中文翻译
2. 翻译难点分析（分点列出）
3. 翻译技巧（分点列出）
4. 对用户翻译的评价（准确性、表达方面的优缺点）
5. 对用户翻译的评分（1-100分）

**非常重要，请严格按照以下JSON格式返回，不要添加任何其他内容：**

{
  "reference_translation": "这里是你的参考翻译",
  "difficulty_analysis": [
    "难点1：具体分析...",
    "难点2：具体分析...",
    "难点3：具体分析..."
  ],
  "translation_skills": [
    "技巧1：具体建议...",
    "技巧2：具体建议...",
    "技巧3：具体建议..."
  ],
  "user_evaluation": "这里是对用户翻译的完整评价",
  "user_score": 80
}

请注意：
1. 必须使用上面展示的标准JSON格式，difficulty_analysis和translation_skills必须是数组格式
2. 所有文本必须使用双引号，不要使用单引号
3. 如果文本中本身包含双引号，需使用反斜杠转义(\\")
4. 不要在JSON前后添加任何解释文字或代码块标记
5. 数组中每个项目必须完整保留，不要省略任何内容
`;

    const deepseek = new DeepSeek();
    const response = await deepseek.chat({
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
    });

    try {
      // 解析AI返回的JSON
      const resultContent = response.choices[0].message.content.trim();
      console.log("AI返回的原始内容:", resultContent);

      // 处理可能被包含在Markdown代码块中的JSON
      let jsonContent = resultContent;

      // 检查是否是Markdown代码块格式(```json ... ```)
      const codeBlockMatch = resultContent.match(
        /```(?:json)?\s*([\s\S]*?)\s*```/
      );
      if (codeBlockMatch && codeBlockMatch[1]) {
        jsonContent = codeBlockMatch[1].trim();
      }

      // 尝试提取有效的JSON - 找到第一个{和最后一个}之间的内容
      const jsonMatch = jsonContent.match(/{[\s\S]*}/);
      if (jsonMatch) {
        jsonContent = jsonMatch[0];
      }

      // 替换可能存在的中文引号和其他问题字符
      jsonContent = jsonContent.replace(/[""]|['']|[「」]/g, '"');

      // 预处理JSON字符串，处理引号问题
      jsonContent = jsonContent
        // 将所有转义的双引号临时替换为特殊标记
        .replace(/\\"/g, "##ESCAPED_QUOTE##")
        // 将所有单引号替换为双引号
        .replace(/'/g, '"')
        // 确保属性名使用双引号
        .replace(/(\w+):/g, '"$1":')
        // 恢复转义的双引号
        .replace(/##ESCAPED_QUOTE##/g, '\\"');

      console.log("预处理后的JSON内容:", jsonContent);

      try {
        // 尝试使用标准JSON解析
        const analysisResult = JSON.parse(jsonContent) as TranslationResult;

        // 验证结果格式并处理可能的数组格式
        if (
          !analysisResult.reference_translation ||
          (!analysisResult.difficulty_analysis &&
            analysisResult.difficulty_analysis !== "") ||
          (!analysisResult.translation_skills &&
            analysisResult.translation_skills !== "")
        ) {
          console.warn("缺少必要字段:", analysisResult);
          throw new Error("无效的响应格式 - 缺少必要字段");
        }

        // 处理difficulty_analysis和translation_skills可能是数组的情况
        const processField = (field: string | string[]): string => {
          if (Array.isArray(field)) {
            return field.join("\n");
          }
          return field;
        };

        return NextResponse.json({
          success: true,
          data: {
            reference_translation: analysisResult.reference_translation,
            difficulty_analysis: processField(
              analysisResult.difficulty_analysis
            ),
            translation_skills: processField(analysisResult.translation_skills),
            user_evaluation: analysisResult.user_evaluation || "",
            user_score: analysisResult.user_score || 0,
          },
        });
      } catch (innerParseError) {
        console.error("第一次JSON解析错误，尝试手动修复:", innerParseError);

        // 最后的手动解析尝试：使用更强大的正则表达式直接提取各个字段
        try {
          // 创建一个完整的结果对象
          const result = {
            reference_translation: "",
            difficulty_analysis: "",
            translation_skills: "",
            user_evaluation: "",
            user_score: 0,
          };

          // 参考翻译 - 使用非贪婪匹配避免捕获太多内容
          const refMatch = jsonContent.match(
            /"reference_translation"\s*:\s*"((?:\\"|[^"])*)"/
          );
          if (refMatch && refMatch[1]) {
            result.reference_translation = refMatch[1].replace(/\\"/g, '"');
          } else {
            result.reference_translation = "无法解析参考翻译";
          }

          // 提取完整数组内容的辅助函数
          const extractArrayContent = (field: string): string => {
            // 首先尝试找到数组的开始和结束位置
            const arrayRegex = new RegExp(
              `"${field}"\\s*:\\s*(\\[\\s*[\\s\\S]*?\\])`
            );
            const arrayMatch = jsonContent.match(arrayRegex);

            if (arrayMatch && arrayMatch[1]) {
              const arrayContent = arrayMatch[1];
              // 提取数组中的每个元素
              const items: string[] = [];
              let inString = false;
              let currentItem = "";
              let escaping = false;

              // 手动解析数组内容，处理可能的嵌套引号和转义字符
              for (let i = 1; i < arrayContent.length - 1; i++) {
                const char = arrayContent[i];

                if (escaping) {
                  // 处理转义字符
                  currentItem += char;
                  escaping = false;
                  continue;
                }

                if (char === "\\") {
                  // 开始转义
                  escaping = true;
                  currentItem += char;
                  continue;
                }

                if (char === '"' && !inString) {
                  // 开始一个字符串
                  inString = true;
                  continue;
                }

                if (char === '"' && inString) {
                  // 结束一个字符串
                  inString = false;
                  if (currentItem.trim()) {
                    items.push(currentItem.trim());
                  }
                  currentItem = "";
                  continue;
                }

                if (inString) {
                  // 在字符串内部，添加字符
                  currentItem += char;
                }
              }

              // 如果仍在处理一个项，添加它
              if (currentItem.trim()) {
                items.push(currentItem.trim());
              }

              return items.length > 0 ? items.join("\n") : `无法解析${field}`;
            }

            // 尝试作为字符串提取
            const stringRegex = new RegExp(
              `"${field}"\\s*:\\s*"((?:\\\\"|[^"])*)"`
            );
            const stringMatch = jsonContent.match(stringRegex);
            if (stringMatch && stringMatch[1]) {
              return stringMatch[1].replace(/\\"/g, '"');
            }

            return `无法解析${field}`;
          };

          // 处理数组字段
          result.difficulty_analysis = extractArrayContent(
            "difficulty_analysis"
          );
          result.translation_skills = extractArrayContent("translation_skills");

          // 用户评价 - 使用非贪婪匹配避免截断
          const evalMatch = jsonContent.match(
            /"user_evaluation"\s*:\s*"((?:\\"|[^"])*)"/
          );
          if (evalMatch && evalMatch[1]) {
            result.user_evaluation = evalMatch[1].replace(/\\"/g, '"');
          }

          // 评分 - 查找分数
          const scoreMatch = jsonContent.match(/"user_score"\s*:\s*(\d+)/);
          if (scoreMatch && scoreMatch[1]) {
            result.user_score = parseInt(scoreMatch[1]);
          }

          return NextResponse.json({
            success: true,
            data: result,
          });
        } catch (finalError) {
          console.error("最终解析尝试也失败:", finalError);

          // 尝试最简单的正则提取方法
          try {
            // 直接提取完整的字段内容
            const extractField = (fieldName: string): string => {
              // 先尝试提取字段值部分的完整内容（数组或字符串）
              const regex = new RegExp(
                `"${fieldName}"\\s*:\\s*(?:(\\[\\s*[\\s\\S]*?\\])|(\\s*"[\\s\\S]*?"))(?:,|\\s*})`
              );
              const match = jsonContent.match(regex);

              if (!match) return `未找到${fieldName}字段`;

              // 如果是数组，尝试直接拼接所有内容
              if (match[1]) {
                // 移除数组括号，保留核心内容
                let content = match[1].replace(/^\[\s*|\s*\]$/g, "");
                // 移除引号和逗号，保留纯文本
                content = content
                  .replace(/"\s*,\s*"|"/g, "\n")
                  .replace(/^"|"$/g, "");
                return content.trim();
              }

              // 如果是字符串，直接返回内容
              if (match[2]) {
                return match[2].replace(/^"\s*|\s*"$/g, "");
              }

              return `${fieldName}格式不正确`;
            };

            const result = {
              reference_translation: extractField("reference_translation"),
              difficulty_analysis: extractField("difficulty_analysis"),
              translation_skills: extractField("translation_skills"),
              user_evaluation: extractField("user_evaluation"),
              user_score: 0,
            };

            // 单独处理评分
            const scoreMatch = jsonContent.match(/"user_score"\s*:\s*(\d+)/);
            if (scoreMatch && scoreMatch[1]) {
              result.user_score = parseInt(scoreMatch[1]);
            }

            return NextResponse.json({
              success: true,
              data: result,
            });
          } catch (ultimateError) {
            console.error("最终简化提取也失败:", ultimateError);
            throw innerParseError; // 将内部错误向上传递
          }
        }
      }
    } catch (parseError) {
      console.error("JSON解析错误:", parseError);
      console.error(
        "尝试解析的JSON字符串:",
        response.choices[0].message.content
      );

      // 构建一个替代响应
      return NextResponse.json({
        success: true,
        data: {
          reference_translation: "AI生成的翻译格式有误，请重新提交",
          difficulty_analysis: "无法解析AI返回的分析，请稍后再试",
          translation_skills: "无法获取翻译技巧，请稍后再试",
          user_evaluation: "",
          user_score: 0,
        },
      });
    }
  } catch (error) {
    console.error("翻译分析错误:", error);
    return NextResponse.json(
      {
        error: "翻译分析失败",
        details: error instanceof Error ? error.message : "未知错误",
      },
      { status: 500 }
    );
  }
}
