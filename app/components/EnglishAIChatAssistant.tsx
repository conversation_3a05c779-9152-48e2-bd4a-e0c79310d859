"use client";

import { useState, FormEvent, useEffect, useRef } from "react";
import {
  Send,
  BookOpen,
  Paperclip,
  Mic,
  Trash2,
  <PERSON>freshCw,
  BookA,
  BookText,
} from "lucide-react";
import {
  RiTranslate,
  RiPenNibLine,
  RiBookReadLine,
  RiDeleteBin4Line,
} from "react-icons/ri";
import { Button } from "@/app/components/ui/button";
import {
  ChatBubble,
  ChatBubbleAvatar,
  ChatBubbleMessage,
} from "@/app/components/ui/chat-bubble";
import { ChatInput } from "@/app/components/ui/chat-input";
import {
  ExpandableChat,
  ExpandableChatHeader,
  ExpandableChatBody,
  ExpandableChatFooter,
} from "@/app/components/ui/expandable-chat";
import { ChatMessageList } from "@/app/components/ui/chat-message-list";
import { ChatCompletionMessageParam } from "openai/resources/chat/completions";
import ReactMarkdown from "react-markdown";

// 头像和图标配置
const AI_AVATAR =
  "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=64&h=64&q=80&crop=faces&fit=crop"; // 使用Unsplash图片

type Message = {
  id: string;
  content: string;
  role: "user" | "assistant" | "system";
  isLoading?: boolean; // 新增加载状态标记
};

export function EnglishAIChatAssistant() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "initial",
      content: "你好！我是你的英语学习助手。请问有什么问题需要我解答？",
      role: "assistant",
    },
  ]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [mode, setMode] = useState<"chat" | "translate" | "writing">("chat");
  const currentResponseIdRef = useRef<string | null>(null);

  // 辅助功能标签
  const assistModes = [
    {
      id: "chat",
      label: "问答",
      icon: <RiBookReadLine className="h-4 w-4" />,
      description: "解答英语学习问题",
    },
    {
      id: "translate",
      label: "翻译",
      icon: <RiTranslate className="h-4 w-4" />,
      description: "中英互译和解析",
    },
    {
      id: "writing",
      label: "写作",
      icon: <RiPenNibLine className="h-4 w-4" />,
      description: "写作指导和修改",
    },
  ];

  // 处理消息发送
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    // 创建一个新的用户消息
    const userMessage: Message = {
      id: Date.now().toString(),
      content: input,
      role: "user",
    };

    // 更新消息列表并清空输入框
    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setIsLoading(true);

    try {
      // 生成唯一响应ID用于追踪
      const responseId = (Date.now() + 1).toString();
      currentResponseIdRef.current = responseId;

      // 准备发送给API的消息
      const apiMessages: ChatCompletionMessageParam[] = messages
        .filter((msg) => msg.role !== "system")
        .map(({ content, role }) => ({
          content,
          role,
        }));

      // 添加用户最新消息
      apiMessages.push({
        content: userMessage.content,
        role: "user",
      });

      // 立即添加AI消息，但内容为空，显示loading状态
      setMessages((prev) => [
        ...prev,
        {
          id: responseId,
          content: "",
          role: "assistant",
          isLoading: true, // 标记为加载中
        },
      ]);

      try {
        // 调用流式API
        const response = await fetch("/api/chat", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ messages: apiMessages }),
        });

        if (!response.ok) {
          let errorMsg = `API回应错误: ${response.status}`;
          try {
            const errorData = await response.json();
            if (errorData && errorData.error) {
              errorMsg = errorData.error;
            }
          } catch (jsonError) {
            // 解析错误响应失败，使用默认错误消息
          }
          throw new Error(errorMsg);
        }

        // 处理流式响应
        if (response.body) {
          const reader = response.body.getReader();
          const decoder = new TextDecoder();
          let done = false;
          let aiMessage = "";
          let hasContent = false;

          while (!done) {
            const { value, done: readerDone } = await reader.read();
            done = readerDone;

            // 如果当前响应已不是我们正在等待的响应，中断处理
            if (currentResponseIdRef.current !== responseId) {
              break;
            }

            if (value) {
              const text = decoder.decode(value);
              const lines = text
                .split("\n")
                .filter(
                  (line) => line.trim() !== "" && line.startsWith("data: ")
                );

              for (const line of lines) {
                const message = line.replace(/^data: /, "");
                if (message === "[DONE]") continue;

                try {
                  const parsed = JSON.parse(message);
                  const content = parsed.choices[0]?.delta?.content || "";
                  if (content) {
                    hasContent = true;
                    aiMessage += content;
                    // 更新消息，移除loading状态
                    setMessages((prevMessages) =>
                      prevMessages.map((msg) =>
                        msg.id === responseId
                          ? { ...msg, content: aiMessage, isLoading: false }
                          : msg
                      )
                    );
                  }
                } catch (error) {
                  console.error("解析流数据错误:", error);
                }
              }
            }
          }

          // 完成加载
          if (currentResponseIdRef.current === responseId) {
            setIsLoading(false);
          }
        }
      } catch (apiError) {
        console.error("发送消息错误:", apiError);

        // 确保这是当前的响应
        if (currentResponseIdRef.current === responseId) {
          // 使用演示模式的回退响应
          // 延迟执行以模拟网络请求
          setTimeout(() => {
            let aiResponse = "";

            switch (mode) {
              case "chat":
                if (input.includes("词汇") || input.includes("单词")) {
                  aiResponse =
                    "提高英语词汇量的关键是系统学习和定期复习。我建议：\n\n1. 制定每日学习计划，如每天学习30-50个单词\n2. 结合真实语境记忆单词\n3. 使用间隔重复法进行复习\n4. 阅读原版英文材料，在实际语境中巩固词汇";
                } else if (input.includes("阅读") || input.includes("理解")) {
                  aiResponse =
                    "提高英语阅读理解能力的方法：\n\n1. 扩大词汇量，特别是常用词汇和学术词汇\n2. 熟悉各类文章结构和常见题型\n3. 练习快速阅读和把握主旨大意的能力\n4. 定期阅读英文文章，总结阅读策略\n5. 学习从上下文推断词义的技巧\n6. 注意长难句分析，理清句子结构";
                } else {
                  aiResponse =
                    "作为英语学习助手，我可以帮你解答词汇、语法、阅读理解、翻译和写作方面的问题。你也可以向我请教学习策略或请我分析特定的英语句子或段落。\n\n[注：当前使用本地演示模式，API连接失败]";
                }
                break;

              case "translate":
                if (/[a-zA-Z]/.test(input)) {
                  // 输入包含英文，进行英译中
                  aiResponse = `原文：${input}\n\n翻译：${input.length > 30 ? "这是一段英文的中文翻译示例。翻译时需要注意语境和表达习惯，避免直译导致的不通顺。" : "这是翻译示例。"}\n\n要点解析：\n1. 句式结构分析\n2. 重点词汇讲解\n3. 翻译技巧说明\n\n[注：当前使用本地演示模式，API连接失败]`;
                } else {
                  // 中译英
                  aiResponse = `原文：${input}\n\n翻译：${input.length > 30 ? "This is an example of Chinese to English translation. When translating, it's important to pay attention to context and expression habits to avoid awkward direct translations." : "This is a translation example."}\n\n要点解析：\n1. 表达习惯差异\n2. 重点词汇选择\n3. 地道表达建议\n\n[注：当前使用本地演示模式，API连接失败]`;
                }
                break;

              case "writing":
                aiResponse = `我已收到你的写作内容，以下是我的修改建议：\n\n1. 内容组织：文章结构清晰，但过渡部分可以增强\n2. 语言表达：部分句子可以更简洁，避免冗余\n3. 词汇使用：可以使用更多高级词汇，如将"very important"改为"crucial"\n4. 语法：注意时态一致性，特别是第二段的时态混用\n\n修改后的版本：\n[这里是修改后的示例文本]\n\n[注：当前使用本地演示模式，API连接失败]`;
                break;
            }

            // 添加AI回复并移除loading状态
            setMessages((prev) =>
              prev.map((msg) =>
                msg.id === responseId
                  ? { ...msg, content: aiResponse, isLoading: false }
                  : msg
              )
            );
            setIsLoading(false);
          }, 1000);
        }
      }
    } catch (error) {
      console.error("处理消息错误:", error);
      // 添加错误消息
      setMessages((prev) => {
        // 查找并移除正在加载的消息
        const filteredMessages = prev.filter(
          (msg) => !(msg.id === currentResponseIdRef.current && msg.isLoading)
        );

        return [
          ...filteredMessages,
          {
            id: (Date.now() + 1).toString(),
            content:
              "抱歉，处理消息时出现了错误。请稍后再试。\n\n技术原因：" +
              (error instanceof Error ? error.message : String(error)),
            role: "assistant",
          },
        ];
      });
      setIsLoading(false);
    }
  };

  // 清空对话历史
  const clearConversation = () => {
    // 如果当前正在加载，取消加载
    if (isLoading) {
      currentResponseIdRef.current = null;
      setIsLoading(false);
    }

    // 重置消息列表为初始状态
    setMessages([
      {
        id: Date.now().toString(),
        content: "你好！我是你的英语学习助手。请问有什么问题需要我解答？",
        role: "assistant",
      },
    ]);
  };

  // 切换模式处理
  const handleModeChange = (newMode: "chat" | "translate" | "writing") => {
    // 如果点击当前已选择的模式，不做任何操作
    if (mode === newMode) return;

    // 如果当前正在加载，取消加载
    if (isLoading) {
      currentResponseIdRef.current = null;
      setIsLoading(false);
    }

    // 更新模式
    setMode(newMode);

    // 仅在切换模式时提示，不自动添加系统消息
    const welcomeMessages = {
      chat: "切换到问答模式。你可以问我任何关于英语的问题。",
      translate: "切换到翻译模式。你可以输入中文或英文进行翻译。",
      writing: "切换到写作模式。你可以粘贴英语文章获取修改建议。",
    };

    // 提示模式变更，但保留历史消息
    setMessages((prev) => [
      ...prev,
      {
        id: Date.now().toString(),
        content: welcomeMessages[newMode],
        role: "system",
      },
    ]);
  };

  // 根据选择的模式更改输入提示
  const getPlaceholderByMode = () => {
    switch (mode) {
      case "chat":
        return "输入你的英语学习问题...";
      case "translate":
        return "输入要翻译的文本...";
      case "writing":
        return "输入要修改或点评的英语写作...";
      default:
        return "输入消息...";
    }
  };

  // 渲染消息内容，支持Markdown
  const renderMessageContent = (content: string) => {
    return (
      <div className="prose prose-sm dark:prose-invert max-w-none break-words whitespace-pre-wrap">
        <ReactMarkdown>{content}</ReactMarkdown>
      </div>
    );
  };

  return (
    <ExpandableChat
      size="lg"
      position="bottom-right"
      icon={<BookOpen className="h-6 w-6" />}
    >
      <ExpandableChatHeader className="flex-col text-center justify-center bg-blue-50 dark:bg-gray-800">
        <div className="flex justify-between items-center w-full px-2 pr-8 sm:pr-2">
          <h1 className="text-xl font-semibold flex items-center gap-2">
            <BookText className="h-5 w-5 text-blue-500" />
            英语学习助手
          </h1>

          <Button
            variant="destructive"
            size="sm"
            onClick={clearConversation}
            title="清空对话历史"
            className="flex items-center gap-1 text-xs"
          >
            <RiDeleteBin4Line className="h-3.5 w-3.5" />
            <span className="sm:inline hidden">清空对话</span>
          </Button>
        </div>

        <p className="text-sm text-muted-foreground mb-1">
          专业解答英语学习问题，提供翻译和写作指导
        </p>

        {/* 模式选择器 */}
        <div className="flex space-x-2 mt-1">
          {assistModes.map((item) => (
            <Button
              key={item.id}
              variant={mode === item.id ? "default" : "outline"}
              size="sm"
              onClick={() =>
                handleModeChange(item.id as "chat" | "translate" | "writing")
              }
              className="flex items-center gap-1"
            >
              {item.icon}
              <span>{item.label}</span>
            </Button>
          ))}
        </div>
      </ExpandableChatHeader>

      <ExpandableChatBody>
        <ChatMessageList>
          {messages.map((message) =>
            message.role === "system" ? (
              <div
                key={message.id}
                className="py-2 px-4 text-xs text-center text-gray-500 bg-gray-100 dark:bg-gray-800 dark:text-gray-400 rounded-md mx-auto my-1"
              >
                {message.content}
              </div>
            ) : (
              <ChatBubble
                key={message.id}
                variant={message.role === "user" ? "sent" : "received"}
              >
                {message.role === "assistant" && (
                  <ChatBubbleAvatar
                    className="h-8 w-8 shrink-0"
                    src={AI_AVATAR}
                    fallback="AI"
                  />
                )}
                <ChatBubbleMessage
                  variant={message.role === "user" ? "sent" : "received"}
                  isLoading={message.role === "assistant" && message.isLoading}
                >
                  {message.isLoading
                    ? null
                    : renderMessageContent(message.content)}
                </ChatBubbleMessage>
              </ChatBubble>
            )
          )}
        </ChatMessageList>
      </ExpandableChatBody>

      <ExpandableChatFooter>
        <form
          onSubmit={handleSubmit}
          className="relative rounded-lg border bg-background focus-within:ring-1 focus-within:ring-ring p-1"
        >
          <ChatInput
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder={getPlaceholderByMode()}
            className="min-h-12 resize-none rounded-lg bg-background border-0 p-3 shadow-none focus-visible:ring-0"
            disabled={isLoading}
          />
          <div className="flex items-center p-3 pt-0 justify-between">
            <div className="flex">
              {/* 附件上传 */}
              <Button
                variant="ghost"
                size="icon"
                type="button"
                title="上传附件"
                disabled={isLoading}
              >
                <Paperclip className="size-4" />
              </Button>

              {/* 语音输入 */}
              <Button
                variant="ghost"
                size="icon"
                type="button"
                title="语音输入"
                disabled={isLoading}
              >
                <Mic className="size-4" />
              </Button>

              {/* 清空对话（仅在移动设备显示） */}
              <Button
                variant="ghost"
                size="icon"
                type="button"
                title="清空对话历史"
                onClick={clearConversation}
                disabled={isLoading}
                className="sm:hidden"
              >
                <Trash2 className="size-4 text-red-500" />
              </Button>
            </div>
            <Button
              type="submit"
              size="sm"
              className="ml-auto gap-1.5"
              disabled={isLoading || !input.trim()}
            >
              {isLoading ? (
                <>
                  <RefreshCw className="size-3.5 animate-spin" />
                  处理中...
                </>
              ) : (
                <>
                  发送
                  <Send className="size-3.5" />
                </>
              )}
            </Button>
          </div>
        </form>
      </ExpandableChatFooter>
    </ExpandableChat>
  );
}
