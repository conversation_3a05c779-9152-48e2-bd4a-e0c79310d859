"use client";

import React from "react";
import { useState, useRef, useEffect } from "react";
import { Share2, Clipboard, Check, Download, X } from "lucide-react";
import { toPng } from "html-to-image";
import ReactMarkdown from "react-markdown";
import type { Components } from "react-markdown";

interface ShareCardProps {
  title: string;
  content: string;
  explanation?: string;
  isOpen: boolean;
  onClose: () => void;
  type?: "sentence" | "translation" | "writing";
  sourceText?: string; // 可选的来源文本
}

// 代码块组件
const CodeBlockComponent = ({
  className,
  children,
}: {
  className?: string;
  children: React.ReactNode;
}) => {
  // 处理语言标识
  const language = className ? className.replace("language-", "") : "";
  const codeContent = children?.toString() || "";

  return (
    <div className="relative group">
      <div className="absolute right-0 top-0 px-2 py-1 rounded-bl text-xs text-slate-400 bg-slate-800 font-mono">
        {language}
      </div>
      <pre className="bg-slate-900 text-white p-4 pt-8 rounded-lg overflow-x-auto my-4 text-sm leading-relaxed">
        <code className={className}>{codeContent}</code>
      </pre>
    </div>
  );
};

export const ShareCard = ({
  title,
  content,
  explanation,
  isOpen,
  onClose,
  type = "sentence",
  sourceText,
}: ShareCardProps) => {
  const [copied, setCopied] = useState(false);
  const [downloading, setDownloading] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  // 关闭时重置状态
  useEffect(() => {
    if (!isOpen) {
      setCopied(false);
      setDownloading(false);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  // 预处理Markdown内容
  const processMarkdownContent = (content: string) => {
    if (!content) return "";
    // 移除markdown语言标记
    return content.replace(/```markdown/g, "```").replace(/```\s*$/g, "```");
  };

  // 自定义Markdown组件
  const markdownComponents: Components = {
    // 处理代码块
    code({ className, children, node, ...props }) {
      // 检查是否是代码块，而不是内联代码
      // @ts-ignore - 没有inline属性，但我们可以检查父节点
      const isCodeBlock = node?.parent?.tagName === "pre";

      if (isCodeBlock) {
        return (
          <CodeBlockComponent className={className}>
            {children}
          </CodeBlockComponent>
        );
      }

      // 内联代码
      return (
        <code
          className="bg-slate-100 text-slate-800 px-1 py-0.5 rounded text-sm"
          {...props}
        >
          {children}
        </code>
      );
    },
    // 重写h2标签，使标题更明显
    h2({ children }) {
      return (
        <h2 className="text-xl font-bold text-blue-700 my-4 border-b pb-2 border-blue-200">
          {children}
        </h2>
      );
    },
    // 重写h3标签
    h3({ children }) {
      return (
        <h3 className="text-lg font-semibold text-blue-600 mt-5 mb-3">
          {children}
        </h3>
      );
    },
    // 处理段落
    p({ children }) {
      return <p className="my-3 leading-relaxed">{children}</p>;
    },
    // 处理列表项
    li({ children }) {
      return <li className="mb-2">{children}</li>;
    },
    // 处理引用块
    blockquote({ children }) {
      return (
        <blockquote className="border-l-4 border-blue-300 bg-blue-50 p-3 my-4 rounded-r text-gray-700 italic">
          {children}
        </blockquote>
      );
    },
    // 处理加粗文本
    strong({ children }) {
      return <strong className="font-bold text-gray-900">{children}</strong>;
    },
    // 处理pre标签
    pre({ children }) {
      // 直接返回子元素，让code组件处理样式
      return <>{children}</>;
    },
  };

  // 复制图片链接到剪贴板
  const copyToClipboard = async () => {
    if (!cardRef.current) return;

    try {
      setDownloading(true);
      const dataUrl = await toPng(cardRef.current, {
        quality: 0.95,
        cacheBust: true,
        canvasWidth: cardRef.current.offsetWidth * 2,
        canvasHeight: cardRef.current.offsetHeight * 2,
        pixelRatio: 2,
        skipAutoScale: true,
        filter: (node) => {
          // 避免某些元素导致截图失败
          const exclusionClasses = ["lucide"];
          if (node instanceof HTMLElement) {
            // 检查元素是否有排除的类名
            return !exclusionClasses.some((className) =>
              node.classList?.contains(className)
            );
          }
          return true;
        },
      });

      // 创建一个blob
      const blob = await fetch(dataUrl).then((res) => res.blob());

      // 使用Clipboard API复制
      const data = new ClipboardItem({ "image/png": blob });
      await navigator.clipboard.write([data]);

      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error("复制到剪贴板失败:", error);
      alert("复制失败，请尝试刷新页面后再试");
    } finally {
      setDownloading(false);
    }
  };

  // 下载分享卡片
  const downloadImage = async () => {
    if (!cardRef.current) return;

    try {
      setDownloading(true);
      const dataUrl = await toPng(cardRef.current, {
        quality: 0.95,
        cacheBust: true,
        canvasWidth: cardRef.current.offsetWidth * 2,
        canvasHeight: cardRef.current.offsetHeight * 2,
        pixelRatio: 2,
        skipAutoScale: true,
        filter: (node) => {
          // 避免某些元素导致截图失败
          const exclusionClasses = ["lucide"];
          if (node instanceof HTMLElement) {
            // 检查元素是否有排除的类名
            return !exclusionClasses.some((className) =>
              node.classList?.contains(className)
            );
          }
          return true;
        },
      });

      // 创建一个blob
      const blob = await fetch(dataUrl).then((res) => res.blob());
      const blobUrl = URL.createObjectURL(blob);

      try {
        // 优先使用下载API (更可靠)
        if ("showSaveFilePicker" in window) {
          // @ts-ignore - 由于TypeScript定义可能缺失showSaveFilePicker API
          const fileHandle = await window.showSaveFilePicker({
            suggestedName: `${title.slice(0, 20)}_mbdata分享.png`,
            types: [
              {
                description: "PNG图片",
                accept: { "image/png": [".png"] },
              },
            ],
          });
          const writable = await fileHandle.createWritable();
          await writable.write(blob);
          await writable.close();
        } else {
          // 备用方法1: 创建下载链接
          const link = document.createElement("a");
          link.download = `${title.slice(0, 20)}_mbdata分享.png`;
          link.href = blobUrl;
          link.style.display = "none";
          document.body.appendChild(link);
          link.click();

          // 清理DOM
          setTimeout(() => {
            document.body.removeChild(link);
          }, 100);
        }
      } catch (downloadError) {
        console.error("直接下载失败，尝试备用方法:", downloadError);

        // 备用方法2: 在新窗口中打开图片，提示用户右键保存
        const newWindow = window.open();
        if (newWindow) {
          newWindow.document.write(`
            <html>
              <head><title>保存图片 - ${title}</title></head>
              <body style="display:flex; justify-content:center; align-items:center; flex-direction:column; padding:20px; font-family:system-ui;">
                <h2 style="margin-bottom:20px;">请右键点击图片，选择"图片另存为"保存</h2>
                <img src="${blobUrl}" style="max-width:100%; border:1px solid #eee; border-radius:8px; box-shadow:0 4px 6px rgba(0,0,0,0.1);" />
              </body>
            </html>
          `);
        } else {
          // 如果新窗口被阻止，提示用户
          alert("下载失败，请尝试使用复制图片功能后粘贴保存");
        }
      }

      // 释放blob URL
      setTimeout(() => {
        URL.revokeObjectURL(blobUrl);
      }, 60000); // 延长blobUrl存活时间，以便备用方法使用
    } catch (error) {
      console.error("下载图片失败:", error);
      alert("下载失败，请尝试使用复制图片功能后粘贴保存");
    } finally {
      setDownloading(false);
    }
  };

  // 分享到社交媒体
  const shareToSocial = async (
    platform: "wechat" | "weibo" | "xiaohongshu"
  ) => {
    if (!cardRef.current) return;

    try {
      setDownloading(true);
      const dataUrl = await toPng(cardRef.current, {
        quality: 0.95,
        cacheBust: true,
        canvasWidth: cardRef.current.offsetWidth * 2,
        canvasHeight: cardRef.current.offsetHeight * 2,
        pixelRatio: 2,
        skipAutoScale: true,
        filter: (node) => {
          // 避免某些元素导致截图失败
          const exclusionClasses = ["lucide"];
          if (node instanceof HTMLElement) {
            // 检查元素是否有排除的类名
            return !exclusionClasses.some((className) =>
              node.classList?.contains(className)
            );
          }
          return true;
        },
      });

      // 创建一个临时blob URL
      const blob = await fetch(dataUrl).then((res) => res.blob());
      const blobUrl = URL.createObjectURL(blob);

      // 根据平台决定分享方式
      switch (platform) {
        case "wechat":
          alert("请长按图片保存后，在微信中分享");
          break;
        case "weibo":
          window.open(
            `http://service.weibo.com/share/share.php?url=${encodeURIComponent(window.location.href)}&title=${encodeURIComponent(`${title} - 考研英语学习笔记分享`)}&pic=${encodeURIComponent(blobUrl)}`
          );
          break;
        case "xiaohongshu":
          alert("请保存图片后，在小红书App中分享");
          break;
      }

      // 释放blob URL
      setTimeout(() => URL.revokeObjectURL(blobUrl), 60000);
    } catch (error) {
      console.error("分享失败:", error);
    } finally {
      setDownloading(false);
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-xl shadow-2xl max-w-xl w-full max-h-[90vh] overflow-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-bold text-gray-800">分享内容</h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-4">
          {/* 分享卡片预览 */}
          <div
            ref={cardRef}
            className="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-8 rounded-lg border border-blue-100 shadow-sm mb-4"
          >
            {/* Logo 和品牌标识 */}
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center gap-2">
                {/* 品牌名称，使用渐变效果提升质感 */}
                <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-transparent bg-clip-text font-bold text-xl">
                  mbdata.site考研英语
                </div>
              </div>
              <div className="text-indigo-500 text-sm font-medium">
                考研英语学习助手
              </div>
            </div>

            {/* 内容区域 */}
            <div className="bg-white rounded-xl p-6 shadow-md mb-5 border border-blue-50">
              <h4 className="font-semibold text-gray-900 mb-3 text-lg">
                {title}
              </h4>
              <p className="text-gray-800 text-lg leading-relaxed mb-3">
                {content}
              </p>
              {sourceText && (
                <div className="text-xs text-gray-500 mt-2 italic">
                  来源: {sourceText}
                </div>
              )}
            </div>

            {/* 解析区域 - 使用Markdown渲染 */}
            {explanation && (
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border-l-4 border-indigo-500 mb-4">
                <h4 className="font-medium text-indigo-800 mb-3 text-lg flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 mr-2"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clipRule="evenodd"
                    />
                  </svg>
                  {type === "sentence"
                    ? "句子解析"
                    : type === "translation"
                      ? "翻译要点"
                      : "写作指导"}
                </h4>
                <div className="markdown-body">
                  <ReactMarkdown components={markdownComponents}>
                    {processMarkdownContent(explanation)}
                  </ReactMarkdown>
                </div>
              </div>
            )}

            {/* 底部信息和装饰 */}
            <div className="flex items-center justify-between mt-6">
              <div className="text-xs text-indigo-600 font-medium">
                关注「mbdata.site」获取更多考研英语学习资料
              </div>
              {/* <div className="text-xs bg-gradient-to-r from-indigo-500 to-purple-500 text-white px-3 py-1 rounded-full">
                小红书值得分享
              </div> */}
            </div>

            {/* 底部装饰 */}
            <div className="mt-4 border-t border-blue-100 pt-4 flex justify-between items-center">
              <div className="flex space-x-2">
                {[...Array(5)].map((_, i) => (
                  <div
                    key={i}
                    className="w-1 h-1 rounded-full bg-indigo-400"
                  ></div>
                ))}
              </div>
              <div className="text-xs text-gray-400">#考研英语 #英语学习</div>
            </div>
          </div>

          {/* 分享选项 */}
          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-3">
              <button
                onClick={() => copyToClipboard()}
                disabled={downloading}
                className="flex flex-col items-center justify-center py-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg hover:shadow-md transition-all"
              >
                {copied ? (
                  <Check className="w-5 h-5 text-green-600" />
                ) : (
                  <Clipboard className="w-5 h-5 text-indigo-600" />
                )}
                <span className="mt-1 text-xs font-medium">
                  {copied ? "已复制" : "复制图片"}
                </span>
              </button>

              <button
                onClick={downloadImage}
                disabled={downloading}
                className="flex flex-col items-center justify-center py-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg hover:shadow-md transition-all"
              >
                <Download className="w-5 h-5 text-indigo-600" />
                <span className="mt-1 text-xs font-medium">保存图片</span>
              </button>

              <button
                onClick={() => shareToSocial("wechat")}
                disabled={downloading}
                className="flex flex-col items-center justify-center py-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg hover:shadow-md transition-all"
              >
                <div className="w-5 h-5 flex items-center justify-center">
                  <span className="text-lg">📱</span>
                </div>
                <span className="mt-1 text-xs font-medium">分享到微信</span>
              </button>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={() => shareToSocial("weibo")}
                disabled={downloading}
                className="flex flex-col items-center justify-center py-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg hover:shadow-md transition-all"
              >
                <div className="w-5 h-5 flex items-center justify-center">
                  <span className="text-lg">🔖</span>
                </div>
                <span className="mt-1 text-xs font-medium">分享到微博</span>
              </button>

              <button
                onClick={() => shareToSocial("xiaohongshu")}
                disabled={downloading}
                className="flex flex-col items-center justify-center py-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg hover:shadow-md transition-all group"
              >
                <div className="w-5 h-5 flex items-center justify-center">
                  <span className="text-lg">📕</span>
                </div>
                <span className="mt-1 text-xs font-medium">分享到小红书</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
