"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import { Loader2 } from "lucide-react";
import { Markdown } from "@/app/components/markdown";
import { WritingAnalysisPreview } from "@/app/components/writing-analysis-preview";
import ImageUploader from "@/app/components/ImageUploader";

interface WritingTask {
  id: number;
  year: string;
  type: string;
  task_type: "small_composition" | "large_composition";
  content: string;
  ai_reference: string | null;
  writing_analysis: string | null;
  image_url: string | null;
  image_description: string | null;
  created_at: string;
  updated_at: string;
}

export default function EditWritingTaskPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const { id } = params;
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [task, setTask] = useState<WritingTask | null>(null);

  // 表单数据
  const [year, setYear] = useState("");
  const [type, setType] = useState("");
  const [taskType, setTaskType] = useState<
    "small_composition" | "large_composition"
  >("small_composition");
  const [content, setContent] = useState("");
  const [aiReference, setAiReference] = useState("");
  const [writingAnalysis, setWritingAnalysis] = useState("");
  const [imageUrl, setImageUrl] = useState("");
  const [imageDescription, setImageDescription] = useState("");

  // 处理图片上传结果
  const handleImageUploaded = (url: string, description: string) => {
    setImageUrl(url || "");
    setImageDescription(description || "");
  };

  // 加载写作任务数据
  useEffect(() => {
    const fetchTask = async () => {
      setIsLoading(true);
      try {
        const res = await fetch(`/api/writing-tasks?id=${id}`);
        const data = await res.json();
        if (data.success && data.data) {
          setTask(data.data);
          setYear(data.data.year || "");
          setType(data.data.type || "");
          setTaskType(data.data.task_type || "small_composition");
          setContent(data.data.content || "");
          setAiReference(data.data.ai_reference || "");
          setWritingAnalysis(data.data.writing_analysis || "");
          setImageUrl(data.data.image_url || "");
          setImageDescription(data.data.image_description || "");
        } else {
          throw new Error("获取写作任务失败");
        }
      } catch (error) {
        console.error("加载写作任务失败:", error);
        alert("加载写作任务失败");
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchTask();
    }
  }, [id]);

  // 保存写作任务
  const handleSave = async () => {
    if (!year || !type || !taskType || !content) {
      alert("请填写必要字段");
      return;
    }

    setIsSaving(true);
    try {
      const res = await fetch("/api/writing-tasks", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id,
          year,
          type,
          task_type: taskType,
          content,
          ai_reference: aiReference,
          writing_analysis: writingAnalysis,
          image_url: imageUrl,
          image_description: imageDescription,
        }),
      });

      const data = await res.json();
      if (data.success) {
        alert("保存成功");
        router.push("/admin/writing-tasks");
      } else {
        throw new Error(data.error || "保存失败");
      }
    } catch (error) {
      console.error("保存写作任务失败:", error);
      alert("保存写作任务失败");
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto flex justify-center items-center h-[70vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">加载中...</span>
      </div>
    );
  }

  if (!task) {
    return (
      <div className="container mx-auto mt-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">未找到写作任务</div>
            <div className="flex justify-center">
              <Button onClick={() => router.push("/admin/writing-tasks")}>
                返回列表
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto my-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">编辑写作任务</h1>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => router.push("/admin/writing-tasks")}
          >
            返回列表
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                保存中...
              </>
            ) : (
              "保存"
            )}
          </Button>
        </div>
      </div>

      <Card>
        <CardContent className="pt-6">
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="year">年份</Label>
                <Input
                  id="year"
                  value={year}
                  onChange={(e) => setYear(e.target.value)}
                  placeholder="例如：2023"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">考试类型</Label>
                <Select value={type} onValueChange={setType} required>
                  <SelectTrigger>
                    <SelectValue placeholder="选择考试类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="英语一">英语一</SelectItem>
                    <SelectItem value="英语二">英语二</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="taskType">作文类型</Label>
              <Select
                value={taskType}
                onValueChange={setTaskType as any}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择作文类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="small_composition">小作文</SelectItem>
                  <SelectItem value="large_composition">大作文</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-gray-500">
                {taskType === "small_composition"
                  ? "小作文通常是应用文，如书信、通知、申请等"
                  : "大作文通常是议论文，需要论述特定的话题或现象"}
              </p>
            </div>

            {taskType === "large_composition" && (
              <div className="mt-4">
                <ImageUploader
                  onImageUploaded={handleImageUploaded}
                  initialImageUrl={imageUrl}
                  initialImageDescription={imageDescription}
                />
                {imageUrl && !imageDescription && (
                  <div className="mt-2 text-sm text-amber-600">
                    已上传图片，但未进行AI分析，建议使用AI分析功能解析图片内容。
                  </div>
                )}
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="content">题目内容</Label>
              <Textarea
                id="content"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder="请输入题目内容..."
                rows={6}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="aiReference">AI参考范文</Label>
              <Textarea
                id="aiReference"
                value={aiReference}
                onChange={(e) => setAiReference(e.target.value)}
                placeholder="AI参考范文..."
                rows={10}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="writingAnalysis">写作分析</Label>
              <Textarea
                id="writingAnalysis"
                value={writingAnalysis}
                onChange={(e) => setWritingAnalysis(e.target.value)}
                placeholder="写作分析..."
                rows={6}
              />
              <p className="text-sm text-gray-500">
                JSON格式，包含写作思路、重点难点、高分要素等信息
              </p>
              {writingAnalysis && (
                <div className="mt-4">
                  <WritingAnalysisPreview content={writingAnalysis} />
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
