"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";

interface Sentence {
  id: string;
  paragraphNum: number;
  indexNum: number;
  originalContent: string;
  explain_md: string;
  createdAt: string;
}

interface Paper {
  id: string;
  title: string;
  year: string;
  type: string;
  sectionType: string;
  createdAt: string;
  updatedAt: string;
  sentences: Sentence[];
}

export default function PaperDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const [paper, setPaper] = useState<Paper | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // 获取真题详情
  useEffect(() => {
    const fetchPaper = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/papers/${params.id}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error("真题不存在");
          }
          throw new Error("获取真题详情失败");
        }

        const data = await response.json();

        if (data.success) {
          setPaper(data.data);
        } else {
          throw new Error(data.error || "获取数据失败");
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "未知错误");
        console.error("获取真题详情错误:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchPaper();
  }, [params.id]);

  // 删除真题
  const deletePaper = async () => {
    try {
      setLoading(true);

      const response = await fetch(`/api/papers/${params.id}`, {
        method: "DELETE",
      });

      const data = await response.json();

      if (data.success) {
        // 跳转到真题列表页面
        router.push("/admin/papers");
      } else {
        throw new Error(data.error || "删除失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "删除失败");
      console.error("删除真题错误:", err);
    } finally {
      setLoading(false);
      setShowDeleteConfirm(false);
    }
  };

  if (loading) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
          <p className="text-blue-700 font-medium">正在加载...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="bg-red-100 p-4 rounded-full">
            <svg
              className="h-12 w-12 text-red-500"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <p className="text-red-700 font-medium">{error}</p>
          <Link
            href="/admin/papers"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            返回列表
          </Link>
        </div>
      </div>
    );
  }

  if (!paper) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <p className="text-gray-500 font-medium">找不到真题</p>
          <Link
            href="/admin/papers"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            返回列表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-8">
      {/* 顶部标题和操作栏 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
            真题详情
          </h1>
          <p className="text-gray-500 mt-2 text-lg">{paper.title}</p>
        </div>
        <div className="flex space-x-4">
          <Link
            href="/admin/papers"
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors"
          >
            返回列表
          </Link>
          <Link
            href={`/admin/sentences?year=${paper.year}&type=${paper.type}&sectionType=${paper.sectionType}`}
            className="px-4 py-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors"
          >
            管理句子
          </Link>
          <button
            onClick={() => setShowDeleteConfirm(true)}
            className="px-4 py-2 bg-red-600 text-white rounded-xl hover:bg-red-700 transition-colors"
          >
            删除真题
          </button>
        </div>
      </div>

      {/* 基本信息 */}
      <div className="bg-white rounded-2xl shadow-lg p-6">
        <h2 className="text-xl font-bold text-gray-800 mb-6">基本信息</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
          <div>
            <p className="text-sm text-gray-500">年份</p>
            <p className="text-lg font-medium text-gray-800">{paper.year}年</p>
          </div>

          <div>
            <p className="text-sm text-gray-500">类型</p>
            <p className="text-lg font-medium text-gray-800">{paper.type}</p>
          </div>

          <div>
            <p className="text-sm text-gray-500">题型</p>
            <p className="text-lg font-medium text-gray-800">
              {paper.sectionType === "use_of_english"
                ? "完形填空"
                : paper.sectionType === "reading_comprehension"
                  ? "阅读理解"
                  : paper.sectionType === "translation"
                    ? "翻译"
                    : paper.sectionType === "writing"
                      ? "写作"
                      : paper.sectionType}
            </p>
          </div>

          <div>
            <p className="text-sm text-gray-500">创建时间</p>
            <p className="text-lg font-medium text-gray-800">
              {new Date(paper.createdAt).toLocaleDateString()}
            </p>
          </div>
        </div>
      </div>

      {/* 句子列表 */}
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
        <div className="p-6 border-b border-gray-100">
          <h2 className="text-2xl font-bold text-gray-800">句子列表</h2>
          <p className="text-sm text-gray-500 mt-1">
            共 {paper.sentences.length} 条记录
          </p>
        </div>

        {paper.sentences.length === 0 ? (
          <div className="py-16 text-center">
            <p className="text-gray-500 text-lg">暂无句子数据</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    段落/序号
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    原文句子
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    解析状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paper.sentences.map((sentence) => (
                  <tr key={sentence.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {sentence.paragraphNum || 1}/{sentence.indexNum || "N/A"}
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 line-clamp-2">
                        {sentence.originalContent}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {sentence.explain_md ? (
                        <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          已解析
                        </span>
                      ) : (
                        <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                          未解析
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-4">
                        <Link
                          href={`/admin/sentences/${sentence.id}`}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          查看
                        </Link>
                        <Link
                          href={`/admin/sentences/${sentence.id}/edit`}
                          className="text-indigo-600 hover:text-indigo-900"
                        >
                          编辑
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* 删除确认对话框 */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full">
            <h3 className="text-xl font-bold text-gray-900 mb-4">确认删除</h3>
            <p className="text-gray-600 mb-6">
              你确定要删除这个真题吗？此操作将同时删除所有关联的句子，且不可撤销。
            </p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={deletePaper}
                disabled={loading}
                className={`px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 ${
                  loading ? "opacity-70 cursor-not-allowed" : ""
                }`}
              >
                {loading ? "处理中..." : "确认删除"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
