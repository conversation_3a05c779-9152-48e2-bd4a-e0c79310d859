import { NextRequest, NextResponse } from "next/server";
import {
  createServerAdminClient,
  createServerSupabaseClient,
} from "@/utils/supabase/server";
import {
  ZPAY_CONFIG,
  generateOrderNumber,
  generatePaymentUrl,
} from "@/utils/zpay";
import { addMonths, addYears, format } from "date-fns";

// Request body type
interface CheckoutUrlRequest {
  productId: string;
  productName: string;
  amount: string;
  paymentMethod: "alipay" | "wxpay" | "qqpay" | "tenpay";
  isSubscription: boolean;
  subscriptionPeriod?: "monthly" | "yearly";
  out_trade_no?: string;
  useExistingTransaction?: boolean;
}

export async function POST(request: NextRequest) {
  try {
    // Get the Supabase client for auth
    const supabase = await createServerSupabaseClient();

    // 使用getUser()代替getSession()获取用户信息
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    // 检查是否有错误
    if (userError) {
      console.error("Error fetching user:", userError);
      return NextResponse.json(
        { error: "Authentication error" },
        { status: 401 }
      );
    }

    // Check if the user is authenticated
    if (!user) {
      console.log("User not authenticated");
      return NextResponse.json(
        { error: "Unauthorized. Please login to proceed." },
        { status: 401 }
      );
    }

    // Get the user ID
    const userId = user.id;
    console.log("Authenticated user ID:", userId);

    // Get the admin client for database operations
    const adminClient = createServerAdminClient();

    // Parse the request body
    const {
      productId,
      productName,
      amount,
      paymentMethod = "wxpay",
      isSubscription = false,
      subscriptionPeriod,
      out_trade_no: existingOrderNumber,
      useExistingTransaction = false,
    } = (await request.json()) as CheckoutUrlRequest;

    // Validate the request
    if (!productId || !productName || !amount) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // For subscriptions, validate subscription period
    if (
      isSubscription &&
      (!subscriptionPeriod ||
        !["monthly", "yearly"].includes(subscriptionPeriod))
    ) {
      return NextResponse.json(
        { error: "Invalid subscription period" },
        { status: 400 }
      );
    }

    let out_trade_no;
    let transaction;

    // 检查是否使用现有的待支付订单
    if (useExistingTransaction && existingOrderNumber) {
      console.log(`使用现有订单: ${existingOrderNumber}`);

      // 验证订单存在且属于当前用户
      const { data: existingTransaction, error: lookupError } =
        await adminClient
          .from("zpay_transactions")
          .select("*")
          .eq("user_id", userId)
          .eq("out_trade_no", existingOrderNumber)
          .eq("status", "pending")
          .single();

      if (lookupError || !existingTransaction) {
        console.error("查找现有订单失败:", lookupError);
        return NextResponse.json(
          { error: "未找到有效的待支付订单" },
          { status: 404 }
        );
      }

      // 使用现有订单
      transaction = existingTransaction;
      out_trade_no = existingOrderNumber;
    } else {
      // 生成新的订单号
      out_trade_no = generateOrderNumber();

      // Create a new transaction record
      const now = new Date();
      let subscriptionStart: Date | null = null;
      let subscriptionEnd: Date | null = null;

      // For subscriptions, check if user already has an active subscription
      if (isSubscription) {
        // Check if there's an active subscription
        const { data: activeSubscription } = await adminClient
          .from("zpay_transactions")
          .select("subscription_end")
          .eq("user_id", userId)
          .eq("product_id", productId)
          .eq("status", "success")
          .eq("is_subscription", true)
          .lt("subscription_start", now)
          .gt("subscription_end", now)
          .order("subscription_end", { ascending: false })
          .limit(1)
          .single();

        // 查询未来到期的订阅（用户可能已经订阅了多次）
        const { data: futureSubscriptions } = await adminClient
          .from("zpay_transactions")
          .select("*")
          .eq("user_id", userId)
          .eq("product_id", productId)
          .eq("status", "success")
          .eq("is_subscription", true)
          .gt("subscription_end", now.toISOString())
          .order("subscription_end", { ascending: false })
          .limit(1);

        // 如果有未来到期的订阅，使用它的结束日期作为新订阅的开始日期
        if (futureSubscriptions && futureSubscriptions.length > 0) {
          subscriptionStart = new Date(futureSubscriptions[0].subscription_end);
          console.log(
            `Using future subscription end date as start: ${subscriptionStart.toISOString()}`
          );
        }
        // 如果有当前活跃的订阅，使用它的结束日期作为新订阅的开始日期
        else if (activeSubscription && activeSubscription.subscription_end) {
          subscriptionStart = new Date(activeSubscription.subscription_end);
          console.log(
            `Using active subscription end date as start: ${subscriptionStart.toISOString()}`
          );
        } else {
          // 否则，从现在开始
          subscriptionStart = now;
          console.log(
            `No existing subscription found, starting now: ${subscriptionStart.toISOString()}`
          );
        }

        // Calculate subscription end date based on period
        if (subscriptionPeriod === "monthly") {
          subscriptionEnd = addMonths(subscriptionStart, 1);
        } else if (subscriptionPeriod === "yearly") {
          subscriptionEnd = addYears(subscriptionStart, 1);
        }

        console.log(`Calculated subscription period: ${subscriptionPeriod}`);
        console.log(
          `Subscription dates: Start=${subscriptionStart.toISOString()}, End=${subscriptionEnd?.toISOString()}`
        );
      }

      // 只有在需要创建新交易记录时才执行插入操作
      if (!transaction) {
        // Create the transaction record using admin client for database operations
        const { data: newTransaction, error } = await adminClient
          .from("zpay_transactions")
          .insert({
            user_id: userId,
            product_id: productId,
            amount,
            payment_method: paymentMethod,
            out_trade_no,
            status: "pending",
            is_subscription: isSubscription,
            subscription_start: subscriptionStart
              ? subscriptionStart.toISOString()
              : null,
            subscription_end: subscriptionEnd
              ? subscriptionEnd.toISOString()
              : null,
            metadata: {
              product_name: productName,
              subscription_period: subscriptionPeriod,
            },
          })
          .select()
          .single();

        if (error) {
          console.error("Failed to create transaction:", error);
          return NextResponse.json(
            { error: "Failed to create transaction" },
            { status: 500 }
          );
        }

        transaction = newTransaction;
      }
    }

    // Generate the payment URL
    const baseUrl = ZPAY_CONFIG.BASE_URL;
    const paymentParams = {
      pid: ZPAY_CONFIG.PID,
      money: amount,
      name: productName,
      out_trade_no,
      notify_url: `${baseUrl}/api/checkout/providers/zpay/webhook`,
      return_url: `${baseUrl}/api/checkout/providers/zpay/webhook?orderId=${out_trade_no}`,
      type: paymentMethod,
      param: isSubscription ? `subscription_${subscriptionPeriod}` : "onetime",
    };

    const paymentUrl = generatePaymentUrl(paymentParams, ZPAY_CONFIG.KEY);

    return NextResponse.json({ url: paymentUrl, orderId: out_trade_no });
  } catch (error) {
    console.error("Payment URL generation error:", error);
    return NextResponse.json(
      { error: "Failed to generate payment URL" },
      { status: 500 }
    );
  }
}
