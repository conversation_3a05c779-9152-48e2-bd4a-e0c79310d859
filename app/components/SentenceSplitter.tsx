"use client";

import { useState } from "react";

interface Sentence {
  id: number;
  content: string;
  startIndex: number;
  endIndex: number;
}

interface SentenceSplitterProps {
  content: string;
  sentences: Sentence[];
  onSplitAll: () => void;
  onMerge: (sentenceIds: number[]) => void;
  onSplitSentence: (sentenceId: number, splitIndex: number) => void;
}

const SentenceSplitter: React.FC<SentenceSplitterProps> = ({
  content,
  sentences,
  onSplitAll,
  onMerge,
  onSplitSentence,
}) => {
  const [selectedSentences, setSelectedSentences] = useState<number[]>([]);
  const [splitIndex, setSplitIndex] = useState<number | null>(null);

  const handleSentenceClick = (sentenceId: number) => {
    setSelectedSentences((prev) =>
      prev.includes(sentenceId)
        ? prev.filter((id) => id !== sentenceId)
        : [...prev, sentenceId]
    );
  };

  const handleMerge = () => {
    if (selectedSentences.length > 1) {
      onMerge(selectedSentences);
      setSelectedSentences([]);
    }
  };

  const handleSplit = (sentenceId: number) => {
    if (splitIndex !== null) {
      onSplitSentence(sentenceId, splitIndex);
      setSplitIndex(null);
    }
  };

  return (
    <div className="space-y-6">
      {/* 原文内容 */}
      <div className="bg-white rounded-lg shadow p-4">
        <h2 className="text-lg font-medium mb-4">原文内容</h2>
        <div className="relative">
          <div className="whitespace-pre-wrap text-gray-700">{content}</div>
          {sentences.map((sentence) => (
            <div
              key={sentence.id}
              className={`absolute border-2 rounded-md ${
                selectedSentences.includes(sentence.id)
                  ? "border-blue-500 bg-blue-50"
                  : "border-transparent hover:border-gray-300"
              }`}
              style={{
                top: `${(sentence.startIndex / content.length) * 100}%`,
                height: `${((sentence.endIndex - sentence.startIndex) / content.length) * 100}%`,
              }}
            />
          ))}
        </div>
      </div>

      {/* 句子列表 */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium">句子列表</h2>
          <div className="space-x-2">
            {selectedSentences.length > 1 && (
              <button
                onClick={handleMerge}
                className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                合并选中句子
              </button>
            )}
            <button
              onClick={onSplitAll}
              className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              重新拆分
            </button>
          </div>
        </div>
        <div className="space-y-2">
          {sentences.map((sentence) => (
            <div
              key={sentence.id}
              className={`p-4 border rounded-md cursor-pointer ${
                selectedSentences.includes(sentence.id)
                  ? "border-blue-500 bg-blue-50"
                  : "hover:bg-gray-50"
              }`}
              onClick={() => handleSentenceClick(sentence.id)}
            >
              <div className="flex justify-between items-start">
                <p className="text-sm">{sentence.content}</p>
                <div className="flex space-x-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setSplitIndex(
                        sentence.startIndex +
                          Math.floor(sentence.content.length / 2)
                      );
                    }}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    拆分
                  </button>
                  {splitIndex ===
                    sentence.startIndex +
                      Math.floor(sentence.content.length / 2) && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSplit(sentence.id);
                      }}
                      className="text-sm text-green-600 hover:text-green-800"
                    >
                      确认
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 操作提示 */}
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
        <h3 className="text-sm font-medium text-blue-800 mb-2">操作说明</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• 点击句子可以选中/取消选中</li>
          <li>• 选中多个句子后可以合并</li>
          <li>• 点击"拆分"按钮可以在句子中间添加拆分点</li>
          <li>• 点击"重新拆分"可以重新进行AI拆分</li>
        </ul>
      </div>
    </div>
  );
};

export default SentenceSplitter;
