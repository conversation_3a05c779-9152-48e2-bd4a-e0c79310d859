"use client";

import React from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { PrismLight as Syntax<PERSON><PERSON>light<PERSON> } from "react-syntax-highlighter";
import { tomorrow } from "react-syntax-highlighter/dist/cjs/styles/prism";

interface MarkdownProps {
  children: string;
}

export const Markdown: React.FC<MarkdownProps> = ({ children }) => {
  return (
    <ReactMarkdown
      remarkPlugins={[remarkGfm]}
      components={{
        code({ className, children, ...props }: any) {
          const match = /language-(\w+)/.exec(className || "");
          return !className?.includes("inline") && match ? (
            <SyntaxHighlighter
              // @ts-ignore
              style={tomorrow}
              language={match[1]}
              PreTag="div"
              {...props}
            >
              {String(children).replace(/\n$/, "")}
            </SyntaxHighlighter>
          ) : (
            <code className={className} {...props}>
              {children}
            </code>
          );
        },
        // 自定义链接样式
        a: ({ node, ...props }: any) => (
          <a
            className="text-blue-600 hover:text-blue-800 underline"
            target="_blank"
            rel="noopener noreferrer"
            {...props}
          />
        ),
        // 自定义标题样式
        h1: ({ node, ...props }: any) => (
          <h1 className="text-2xl font-bold my-4" {...props} />
        ),
        h2: ({ node, ...props }: any) => (
          <h2 className="text-xl font-bold my-3" {...props} />
        ),
        h3: ({ node, ...props }: any) => (
          <h3 className="text-lg font-bold my-2" {...props} />
        ),
        // 自定义列表样式
        ul: ({ node, ...props }: any) => (
          <ul className="list-disc pl-6 my-2" {...props} />
        ),
        ol: ({ node, ...props }: any) => (
          <ol className="list-decimal pl-6 my-2" {...props} />
        ),
        // 自定义段落样式
        p: ({ node, ...props }: any) => <p className="my-2" {...props} />,
        // 自定义引用样式
        blockquote: ({ node, ...props }: any) => (
          <blockquote
            className="border-l-4 border-gray-300 pl-4 py-2 my-2 text-gray-700 italic"
            {...props}
          />
        ),
      }}
    >
      {children || ""}
    </ReactMarkdown>
  );
};
