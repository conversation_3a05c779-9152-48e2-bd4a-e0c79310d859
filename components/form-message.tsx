import {
  RiCheckboxCircleLine,
  RiErrorWarningLine,
  RiInformationLine,
} from "react-icons/ri";

export type Message =
  | { success: string }
  | { error: string }
  | { message: string };

export function FormMessage({ message }: { message: Message }) {
  return (
    <div className="flex flex-col gap-2 w-full max-w-md">
      {"success" in message && (
        <div className="flex items-center gap-2 bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-400 p-3 rounded-md border border-green-200 dark:border-green-800">
          <RiCheckboxCircleLine className="flex-shrink-0 h-5 w-5" />
          <span>{message.success}</span>
        </div>
      )}
      {"error" in message && (
        <div className="flex items-center gap-2 bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400 p-3 rounded-md border border-red-200 dark:border-red-800">
          <RiErrorWarningLine className="flex-shrink-0 h-5 w-5" />
          <span>{message.error}</span>
        </div>
      )}
      {"message" in message && (
        <div className="flex items-center gap-2 bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 p-3 rounded-md border border-blue-200 dark:border-blue-800">
          <RiInformationLine className="flex-shrink-0 h-5 w-5" />
          <span>{message.message}</span>
        </div>
      )}
    </div>
  );
}
