import { createClient } from "@/lib/supabase/server";
import { NextRequest, NextResponse } from "next/server";
import { Vocabulary } from "@/types/vocabulary";

export async function GET(request: NextRequest) {
  try {
    // 获取URL参数
    const searchParams = request.nextUrl.searchParams;
    const letter = searchParams.get("letter") || "a";
    const search = searchParams.get("search") || "";

    if (!letter) {
      return NextResponse.json({ error: "字母参数不能为空" }, { status: 400 });
    }

    const supabase = createClient();
    let query = supabase.from("vocabulary").select("*");

    // 按字母筛选
    query = query.eq("first_letter", letter);

    // 搜索条件
    if (search && search.trim() !== "") {
      query = query.ilike("word", `%${search}%`);
    }

    const { data, error } = await query.order("word");

    if (error) {
      console.error("Error fetching vocabulary:", error);
      return NextResponse.json({ error: "获取词汇失败" }, { status: 500 });
    }

    // 添加缓存控制头，防止客户端过度缓存
    return NextResponse.json(
      { vocabulary: data || [] },
      {
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      }
    );
  } catch (error) {
    console.error("获取词汇出错:", error);
    return NextResponse.json({ error: "获取词汇出错" }, { status: 500 });
  }
}
