import { forgotPasswordAction } from "@/app/actions";
import { FormMessage, Message } from "@/components/form-message";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import Link from "next/link";
import { SmtpMessage } from "../smtp-message";
import { RiMailLine, RiKeyLine } from "react-icons/ri";

export default async function ForgotPassword(props: {
  searchParams: Promise<Message>;
}) {
  const searchParams = await props.searchParams;
  return (
    <>
      <Card className="shadow-lg transition-all duration-300 hover:shadow-xl">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-2">
            <div className="rounded-full bg-amber-100 p-3 text-amber-600 dark:bg-amber-900 dark:text-amber-300">
              <RiKeyLine size={24} />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-center">
            重置密码
          </CardTitle>
          <CardDescription className="text-center">
            已记起密码？{" "}
            <Link
              className="text-blue-600 hover:underline dark:text-blue-400 font-medium"
              href="/sign-in"
            >
              返回登录
            </Link>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form className="flex flex-col gap-4">
            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium">
                邮箱
              </Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
                  <RiMailLine />
                </div>
                <Input
                  name="email"
                  id="email"
                  placeholder="<EMAIL>"
                  required
                  className="pl-10"
                />
              </div>
            </div>

            <SubmitButton
              formAction={forgotPasswordAction}
              pendingText="发送中..."
              className="mt-2 w-full bg-blue-600 hover:bg-blue-700 text-white"
            >
              发送重置链接
            </SubmitButton>
            <FormMessage message={searchParams} />
          </form>
        </CardContent>
        <CardFooter className="flex justify-center border-t pt-4">
          <p className="text-xs text-gray-500">
            重置链接将发送到您的邮箱，请注意查收
          </p>
        </CardFooter>
      </Card>
      {/* <SmtpMessage /> */}
    </>
  );
}
