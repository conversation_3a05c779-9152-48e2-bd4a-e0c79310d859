"use client";

import { useState } from "react";
import { Share2 } from "lucide-react";
import { ShareCard } from "./share-card";

interface ShareButtonProps {
  title: string;
  content: string;
  explanation?: string;
  buttonClassName?: string;
  type?: "sentence" | "translation" | "writing";
  sourceText?: string;
  buttonText?: string;
  iconOnly?: boolean;
}

export const ShareButton = ({
  title,
  content,
  explanation,
  buttonClassName = "px-2 py-1 text-sm bg-blue-50 text-blue-700 rounded hover:bg-blue-100 transition-colors flex items-center gap-1",
  type = "sentence",
  sourceText,
  buttonText = "分享",
  iconOnly = false,
}: ShareButtonProps) => {
  const [isShareOpen, setIsShareOpen] = useState(false);

  const handleOpenShare = () => {
    setIsShareOpen(true);
  };

  const handleCloseShare = () => {
    setIsShareOpen(false);
  };

  return (
    <>
      <button
        onClick={handleOpenShare}
        className={buttonClassName}
        title="分享内容"
      >
        <Share2 className="w-5 h-5" />
        {!iconOnly && <span>{buttonText}</span>}
      </button>

      <ShareCard
        title={title}
        content={content}
        explanation={explanation}
        isOpen={isShareOpen}
        onClose={handleCloseShare}
        type={type}
        sourceText={sourceText}
      />
    </>
  );
};
