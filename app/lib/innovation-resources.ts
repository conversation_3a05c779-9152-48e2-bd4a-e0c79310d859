import { Resource } from "../api/innovation-entrepreneurship/route";

// 资源类型定义
export const innovationResourceTypes = [
  { id: "recommended", label: "精品案例(推荐)" },
  { id: "app", label: "APP移动应用" },
  { id: "ecommerce", label: "电子商务" },
  { id: "internet", label: "互联网项目" },
  { id: "tech", label: "科技项目" },
  { id: "fashion", label: "服装服饰箱包" },
  { id: "health", label: "大健康项目" },
  { id: "ai", label: "人工智能" },
  { id: "culture", label: "文化传媒" },
  { id: "food", label: "食品饮料酒类" },
  { id: "homestay", label: "民宿项目" },
  { id: "cafe", label: "奶茶咖啡甜品" },
  { id: "hotel", label: "酒店旅游" },
  { id: "housekeeping", label: "家政服务" },
  { id: "logistics", label: "仓储物流" },
  { id: "food_service", label: "餐饮美食" },
  { id: "supermarket", label: "超市便利水果店" },
  { id: "pets", label: "宠物项目" },
  { id: "toys", label: "儿童玩具" },
  { id: "fitness", label: "健身运动体育用品" },
  { id: "beauty", label: "美容美发化妆品美甲" },
  { id: "agriculture", label: "农业项目" },
  { id: "automotive", label: "汽车类" },
  { id: "furniture", label: "家具家纺" },
  { id: "elderly", label: "养老项目" },
  { id: "outdoor", label: "户外项目" },
  { id: "new_energy", label: "新能源项目" },
  { id: "photography", label: "摄影摄像婚庆" },
  { id: "advertising", label: "广告营销策划" },
  { id: "education", label: "教育培训" },
  { id: "mobile_comm", label: "移动通信" },
  { id: "environment", label: "环保项目" },
  { id: "charity", label: "公益项目" },
  { id: "construction", label: "工程建筑" },
  { id: "trade", label: "进出口贸易" },
  { id: "entertainment", label: "娱乐休闲" },
  { id: "hr", label: "人力资源项目" },
  { id: "fmcg", label: "快消品项目" },
  { id: "software", label: "软件开发" },
  { id: "electronics", label: "电子数码" },
  { id: "design", label: "方案设计" },
  { id: "kindergarten", label: "幼儿园项目" },
  { id: "home_building", label: "家居建材" },
  { id: "mother_baby", label: "母婴用品" },
  { id: "iot", label: "物联网项目" },
  { id: "sharing", label: "共享经济" },
  { id: "consulting", label: "管理咨询" },
  { id: "machinery", label: "机械制造业" },
  { id: "finance", label: "金融行业" },
  { id: "big_data", label: "大数据" },
  { id: "printing", label: "打字复印" },
  { id: "umbrella", label: "雨伞类" },
  { id: "jewelry", label: "珠宝行业" },
  { id: "livestream", label: "直播和电竞" },
  { id: "sanitary", label: "卫生用品" },
  { id: "watches", label: "手表项目" },
  { id: "flowers", label: "花卉鲜花" },
  { id: "legal", label: "法律服务" },
  { id: "tea", label: "茶叶茶馆" },
  { id: "campus", label: "校园项目" },
  { id: "transport", label: "运输工具" },
  { id: "media", label: "自媒体" },
  { id: "pottery", label: "陶瓷陶艺" },
  { id: "stationery", label: "文具类" },
  { id: "bookstore", label: "书屋书店" },
  { id: "gifts", label: "礼品饰品类" },
  { id: "applications", label: "创新创业相关申请书" },
];

// 获取分类样式
export function getInnovationCategoryStyles(category: string) {
  switch (category) {
    case "精品案例(推荐)":
      return "bg-blue-50 text-blue-600 border border-blue-100 hover:bg-blue-100";
    case "APP移动应用":
      return "bg-indigo-50 text-indigo-600 border border-indigo-100 hover:bg-indigo-100";
    case "电子商务":
      return "bg-purple-50 text-purple-600 border border-purple-100 hover:bg-purple-100";
    case "互联网项目":
      return "bg-fuchsia-50 text-fuchsia-600 border border-fuchsia-100 hover:bg-fuchsia-100";
    case "科技项目":
      return "bg-violet-50 text-violet-600 border border-violet-100 hover:bg-violet-100";
    case "服装服饰箱包":
      return "bg-orange-50 text-orange-600 border border-orange-100 hover:bg-orange-100";
    case "大健康项目(医疗、医药、保健）":
      return "bg-pink-50 text-pink-600 border border-pink-100 hover:bg-pink-100";
    case "人工智能":
      return "bg-green-50 text-green-600 border border-green-100 hover:bg-green-100";
    case "文化传媒":
      return "bg-cyan-50 text-cyan-600 border border-cyan-100 hover:bg-cyan-100";
    case "食品饮料酒类":
      return "bg-amber-50 text-amber-600 border border-amber-100 hover:bg-amber-100";
    case "民宿项目":
      return "bg-teal-50 text-teal-600 border border-teal-100 hover:bg-teal-100";
    case "奶茶咖啡甜品蛋糕面包":
      return "bg-rose-50 text-rose-600 border border-rose-100 hover:bg-rose-100";
    case "酒店旅游":
      return "bg-emerald-50 text-emerald-600 border border-emerald-100 hover:bg-emerald-100";
    case "家政服务":
      return "bg-sky-50 text-sky-600 border border-sky-100 hover:bg-sky-100";
    case "仓储物流":
      return "bg-lime-50 text-lime-600 border border-lime-100 hover:bg-lime-100";
    case "餐饮美食":
      return "bg-red-50 text-red-600 border border-red-100 hover:bg-red-100";
    case "超市便利水果店":
      return "bg-yellow-50 text-yellow-600 border border-yellow-100 hover:bg-yellow-100";
    case "宠物项目":
      return "bg-blue-50 text-blue-600 border border-blue-100 hover:bg-blue-100";
    case "儿童玩具":
      return "bg-pink-50 text-pink-600 border border-pink-100 hover:bg-pink-100";
    case "健身运动体育用品":
      return "bg-indigo-50 text-indigo-600 border border-indigo-100 hover:bg-indigo-100";
    case "美容美发化妆品美甲":
      return "bg-fuchsia-50 text-fuchsia-600 border border-fuchsia-100 hover:bg-fuchsia-100";
    case "农业项目":
      return "bg-green-50 text-green-600 border border-green-100 hover:bg-green-100";
    case "汽车类":
      return "bg-gray-50 text-gray-600 border border-gray-100 hover:bg-gray-100";
    case "家具家纺":
      return "bg-amber-50 text-amber-600 border border-amber-100 hover:bg-amber-100";
    case "养老项目":
      return "bg-purple-50 text-purple-600 border border-purple-100 hover:bg-purple-100";
    case "户外项目":
      return "bg-emerald-50 text-emerald-600 border border-emerald-100 hover:bg-emerald-100";
    case "新能源项目":
      return "bg-lime-50 text-lime-600 border border-lime-100 hover:bg-lime-100";
    case "摄影摄像婚庆":
      return "bg-rose-50 text-rose-600 border border-rose-100 hover:bg-rose-100";
    case "广告营销策划":
      return "bg-orange-50 text-orange-600 border border-orange-100 hover:bg-orange-100";
    case "教育培训":
      return "bg-cyan-50 text-cyan-600 border border-cyan-100 hover:bg-cyan-100";
    case "移动通信":
      return "bg-violet-50 text-violet-600 border border-violet-100 hover:bg-violet-100";
    case "环保项目":
      return "bg-teal-50 text-teal-600 border border-teal-100 hover:bg-teal-100";
    case "公益项目":
      return "bg-blue-50 text-blue-600 border border-blue-100 hover:bg-blue-100";
    case "工程建筑":
      return "bg-gray-50 text-gray-600 border border-gray-100 hover:bg-gray-100";
    case "进出口贸易":
      return "bg-amber-50 text-amber-600 border border-amber-100 hover:bg-amber-100";
    case "娱乐休闲":
      return "bg-purple-50 text-purple-600 border border-purple-100 hover:bg-purple-100";
    case "人力资源项目":
      return "bg-pink-50 text-pink-600 border border-pink-100 hover:bg-pink-100";
    case "快消品项目":
      return "bg-red-50 text-red-600 border border-red-100 hover:bg-red-100";
    case "软件开发":
      return "bg-sky-50 text-sky-600 border border-sky-100 hover:bg-sky-100";
    case "电子数码":
      return "bg-blue-50 text-blue-600 border border-blue-100 hover:bg-blue-100";
    case "方案设计":
      return "bg-indigo-50 text-indigo-600 border border-indigo-100 hover:bg-indigo-100";
    case "幼儿园项目":
      return "bg-yellow-50 text-yellow-600 border border-yellow-100 hover:bg-yellow-100";
    case "家居建材":
      return "bg-lime-50 text-lime-600 border border-lime-100 hover:bg-lime-100";
    case "母婴用品":
      return "bg-pink-50 text-pink-600 border border-pink-100 hover:bg-pink-100";
    case "物联网项目":
      return "bg-green-50 text-green-600 border border-green-100 hover:bg-green-100";
    case "共享经济":
      return "bg-violet-50 text-violet-600 border border-violet-100 hover:bg-violet-100";
    case "管理咨询":
      return "bg-cyan-50 text-cyan-600 border border-cyan-100 hover:bg-cyan-100";
    case "机械制造业":
      return "bg-gray-50 text-gray-600 border border-gray-100 hover:bg-gray-100";
    case "金融行业":
      return "bg-amber-50 text-amber-600 border border-amber-100 hover:bg-amber-100";
    case "大数据":
      return "bg-blue-50 text-blue-600 border border-blue-100 hover:bg-blue-100";
    case "打字复印":
      return "bg-purple-50 text-purple-600 border border-purple-100 hover:bg-purple-100";
    case "雨伞类":
      return "bg-sky-50 text-sky-600 border border-sky-100 hover:bg-sky-100";
    case "珠宝行业":
      return "bg-rose-50 text-rose-600 border border-rose-100 hover:bg-rose-100";
    case "直播和电竞":
      return "bg-fuchsia-50 text-fuchsia-600 border border-fuchsia-100 hover:bg-fuchsia-100";
    case "卫生用品":
      return "bg-teal-50 text-teal-600 border border-teal-100 hover:bg-teal-100";
    case "手表项目":
      return "bg-orange-50 text-orange-600 border border-orange-100 hover:bg-orange-100";
    case "花卉鲜花":
      return "bg-emerald-50 text-emerald-600 border border-emerald-100 hover:bg-emerald-100";
    case "法律服务":
      return "bg-indigo-50 text-indigo-600 border border-indigo-100 hover:bg-indigo-100";
    case "茶叶茶馆":
      return "bg-green-50 text-green-600 border border-green-100 hover:bg-green-100";
    case "校园项目":
      return "bg-blue-50 text-blue-600 border border-blue-100 hover:bg-blue-100";
    case "运输工具":
      return "bg-gray-50 text-gray-600 border border-gray-100 hover:bg-gray-100";
    case "自媒体":
      return "bg-purple-50 text-purple-600 border border-purple-100 hover:bg-purple-100";
    case "陶瓷陶艺":
      return "bg-amber-50 text-amber-600 border border-amber-100 hover:bg-amber-100";
    case "文具类":
      return "bg-lime-50 text-lime-600 border border-lime-100 hover:bg-lime-100";
    case "书屋书店":
      return "bg-cyan-50 text-cyan-600 border border-cyan-100 hover:bg-cyan-100";
    case "礼品饰品类":
      return "bg-pink-50 text-pink-600 border border-pink-100 hover:bg-pink-100";
    case "创新创业相关申请书":
      return "bg-blue-50 text-blue-600 border border-blue-100 hover:bg-blue-100";
    default:
      return "bg-gray-50 text-gray-600 border border-gray-100 hover:bg-gray-100";
  }
}

// 获取当前选择的资源类型名称
export function getInnovationTypeName(selectedType: string) {
  const type = innovationResourceTypes.find((t) => t.id === selectedType);
  return type ? type.label : "资源";
}

// 获取创新创业资源
export async function fetchInnovationResources(
  selectedType: string
): Promise<Resource[]> {
  try {
    const timestamp = new Date().getTime();
    const response = await fetch(
      `/api/innovation-entrepreneurship?t=${timestamp}&type=${selectedType}`,
      {
        cache: "no-store",
      }
    );

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`);
    }

    const data = await response.json();
    return data.resources || [];
  } catch (error) {
    console.error("获取创新创业资源出错:", error);
    throw error;
  }
}
