import { Metadata } from "next";
import Link from "next/link";
import Image from "next/image";
import { AnimatedHero } from "./components/ui/animated-hero";
import { ExamLibrary } from "./components/ui/exam-library";
import { RiFileDownloadLine } from "react-icons/ri";
import LearningResources from "./learning-resources/page";
export const metadata: Metadata = {
  title: "大学生考试真题资源网 | 面包资料屋",
  description:
    "提供考研英语、政治、数学、专业课以及四六级、专四专八等历年真题与解析，关注公众号「面包资料屋」获取更多独家备考资料",
};

export default function Home() {
  return (
    <main className="min-h-screen bg-gray-50">
      {/* 动画Hero区域 */}
      <AnimatedHero />

      <div className="container mx-auto px-4 py-8 md:py-12">
        {/* <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">
            考试题型学习
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link
              href="/"
              className="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow group"
            >
              <div className="w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center mb-4 group-hover:bg-blue-200 transition-colors">
                <svg
                  className="w-8 h-8 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors">
                完形填空
              </h3>
              <p className="text-gray-600">
                练习词汇和语法知识，提高上下文理解能力
              </p>
            </Link>

            <Link
              href="/"
              className="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow group"
            >
              <div className="w-14 h-14 bg-green-100 rounded-full flex items-center justify-center mb-4 group-hover:bg-green-200 transition-colors">
                <svg
                  className="w-8 h-8 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2 group-hover:text-green-600 transition-colors">
                阅读理解
              </h3>
              <p className="text-gray-600">
                提高阅读速度和理解能力，掌握解题技巧
              </p>
            </Link>

            <Link
              href="/"
              className="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow group"
            >
              <div className="w-14 h-14 bg-purple-100 rounded-full flex items-center justify-center mb-4 group-hover:bg-purple-200 transition-colors">
                <svg
                  className="w-8 h-8 text-purple-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2 group-hover:text-purple-600 transition-colors">
                翻译
              </h3>
              <p className="text-gray-600">学习翻译技巧，提高中英文转换能力</p>
            </Link>

            <Link
              href="/"
              className="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow group"
            >
              <div className="w-14 h-14 bg-orange-100 rounded-full flex items-center justify-center mb-4 group-hover:bg-orange-200 transition-colors">
                <svg
                  className="w-8 h-8 text-orange-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2 group-hover:text-orange-600 transition-colors">
                写作
              </h3>
              <p className="text-gray-600">
                掌握写作结构和表达，提高英文写作水平
              </p>
            </Link>
          </div>
        </div> */}

        {/* 学习资源模块 */}
        <div className="mb-12">
          <div className="bg-white rounded-xl shadow-md overflow-hidden">
            <div className="md:flex">
              <div className="md:w-2/3 p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-3">
                  高质量真题与解析
                </h3>
                <p className="text-gray-600 mb-4">
                  我们精心整理了一系列高质量的考试真题资源，包括考研英语、政治、数学、专业课以及四六级、专四专八等历年真题与解析，帮助你系统高效地备考。
                </p>
                <ul className="space-y-3 mb-6">
                  <li className="flex items-center text-gray-700">
                    <svg
                      className="w-5 h-5 text-green-500 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    历年考研英语真题与详细解析
                  </li>
                  <li className="flex items-center text-gray-700">
                    <svg
                      className="w-5 h-5 text-green-500 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    考研政治、数学、专业课真题资料
                  </li>
                  <li className="flex items-center text-gray-700">
                    <svg
                      className="w-5 h-5 text-green-500 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    四六级、专四专八、英语竞赛真题
                  </li>
                </ul>
                <Link
                  href="/learning-resources"
                  className="inline-flex items-center px-5 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <RiFileDownloadLine className="mr-2" />
                  浏览全部真题
                </Link>
              </div>
              <div className="md:w-1/3 bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center p-6">
                <div className="text-center">
                  <div className="text-5xl text-white mb-3">📚</div>
                  <h3 className="text-xl font-bold text-white mb-2">
                    免费下载
                  </h3>
                  <p className="text-blue-100">精选历年考试真题资料</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 公众号引流模块 */}
        <div className="mb-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 shadow-sm">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <div>
              <h2 className="text-xl font-bold text-blue-800 mb-2">
                面包资料屋
              </h2>
              <p className="text-gray-700">
                关注我们的公众号，获取更多独家考研、四六级等历年真题资料和备考技巧，助你考试顺利！
              </p>
            </div>
            <div className="bg-white p-3 rounded-lg shadow-md">
              <div className="text-center">
                <div className="relative w-32 h-32 mx-auto">
                  <Image
                    src="/images/wechat-qrcode.jpg"
                    alt="面包资料屋公众号二维码"
                    fill
                    className="object-cover rounded-md"
                    sizes="(max-width: 768px) 100vw, 128px"
                  />
                </div>
                <p className="mt-2 text-sm font-medium text-blue-700">
                  扫码关注获取更多资料
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* {真题资源下载} */}
        <LearningResources />
        {/* 真题列表 */}
        <ExamLibrary />
      </div>
    </main>
  );
}
