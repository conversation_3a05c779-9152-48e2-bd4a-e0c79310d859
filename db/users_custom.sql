-- 创建用户自定义信息表
CREATE TABLE IF NOT EXISTS "public"."users_custom" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  "email" text,
  "is_subscriptionnew" boolean NOT NULL DEFAULT false,
  "created_at" timestamp with time zone NOT NULL DEFAULT now(),
  "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
  PRIMARY KEY ("id")
);

-- 添加注释
COMMENT ON TABLE "public"."users_custom" IS '存储用户的自定义信息和订阅状态';

-- 创建索引以加快查询速度
CREATE INDEX IF NOT EXISTS idx_users_custom_user_id ON public.users_custom (user_id);
CREATE INDEX IF NOT EXISTS idx_users_custom_email ON public.users_custom (email);
CREATE INDEX IF NOT EXISTS idx_users_custom_is_subscriptionnew ON public.users_custom (is_subscriptionnew);

-- 启用行级安全
ALTER TABLE "public"."users_custom" ENABLE ROW LEVEL SECURITY;

-- 创建安全策略
CREATE POLICY "Users can view their own information" 
  ON "public"."users_custom" 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all user information" 
  ON "public"."users_custom" 
  USING (
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.uid() = auth.users.id AND auth.users.is_super_admin = true
    )
  );

-- 创建自动更新 updated_at 的触发器
CREATE TRIGGER update_users_custom_updated_at
BEFORE UPDATE ON public.users_custom
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- 创建在用户注册时自动创建用户自定义信息记录的函数
CREATE OR REPLACE FUNCTION public.handle_new_user() 
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users_custom (user_id, email)
  VALUES (NEW.id, NEW.email);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建在用户注册后触发的触发器
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_new_user();

-- 如果需要处理已有用户，可以运行以下命令添加现有用户数据
INSERT INTO public.users_custom (user_id, email)
SELECT id, email FROM auth.users
WHERE NOT EXISTS (
  SELECT 1 FROM public.users_custom WHERE public.users_custom.user_id = auth.users.id
); 