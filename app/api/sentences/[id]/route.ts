import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// GET 方法：获取单个句子详情
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    if (!id) {
      return NextResponse.json(
        { error: "Missing required field: id" },
        { status: 400 }
      );
    }

    // 查询句子数据及其关联的论文数据
    const { data, error } = await supabase
      .from("sentences")
      .select("*, papers(*)")
      .eq("id", id)
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      return NextResponse.json(
        { error: "Sentence not found" },
        { status: 404 }
      );
    }

    // 格式化返回数据以保持与原API兼容
    const formattedData = {
      id: data.id,
      year: data.papers.year,
      type: data.papers.type,
      sectionType: data.papers.section_type,
      title: `${data.papers.year}年全国硕士研究生招生考试${data.papers.type}试题`,
      originalContent: data.content,
      paragraphNum: data.paragraph_num,
      indexNum: data.sequence,
      explain_md: data.explain_md,
      createdAt: data.created_at,
    };

    // 尝试解析旧版内容格式（JSON字符串）
    let content = {};

    // 如果有explain_md，尝试从中提取结构化内容
    if (formattedData.explain_md) {
      // 从Markdown中提取结构化内容
      try {
        // 这里可以实现一个简单的Markdown解析逻辑，将Markdown内容转换为结构化的JSON对象
        // 这个例子中只是一个简单的实现，实际应用中可能需要更复杂的解析逻辑
        content = extractContentFromMarkdown(formattedData.explain_md);
      } catch (e) {
        console.error("Error parsing Markdown content:", e);
        // 如果解析失败，使用空内容对象
        content = {};
      }
    }

    // 返回句子数据
    return NextResponse.json({
      success: true,
      data: {
        ...formattedData,
        content,
      },
    });
  } catch (error) {
    console.error("Error fetching sentence:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch sentence",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// PUT 方法：更新句子
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    const sentenceData = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: "Missing required field: id" },
        { status: 400 }
      );
    }

    // 验证必要字段
    if (!sentenceData.originalContent) {
      return NextResponse.json(
        { error: "Missing required field: originalContent" },
        { status: 400 }
      );
    }

    // 首先获取当前句子信息，以获取article_id
    const { data: sentenceInfo, error: fetchError } = await supabase
      .from("sentences")
      .select("article_id")
      .eq("id", id)
      .single();

    if (fetchError) {
      throw fetchError;
    }

    const articleId = sentenceInfo?.article_id;

    // 使用事务性操作
    // 虽然Supabase不支持传统意义上的事务，但可以在服务端逻辑中处理

    // 首先更新相关的paper记录（如果提供了相关字段）
    if (
      sentenceData.year &&
      sentenceData.type &&
      sentenceData.sectionType &&
      articleId
    ) {
      const { error: paperUpdateError } = await supabase
        .from("papers")
        .update({
          year: sentenceData.year,
          type: sentenceData.type,
          section_type: sentenceData.sectionType,
        })
        .eq("id", articleId);

      if (paperUpdateError) {
        throw paperUpdateError;
      }
    }

    // 更新句子记录
    const { error: sentenceUpdateError } = await supabase
      .from("sentences")
      .update({
        content: sentenceData.originalContent,
        paragraph_num: sentenceData.paragraphNum || 1,
        sequence: sentenceData.indexNum,
        explain_md: sentenceData.explain_md || "",
      })
      .eq("id", id);

    if (sentenceUpdateError) {
      throw sentenceUpdateError;
    }

    return NextResponse.json({
      success: true,
      message: "Sentence updated successfully",
    });
  } catch (error) {
    console.error("Error updating sentence:", error);
    return NextResponse.json(
      {
        error: "Failed to update sentence",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// 辅助函数：从Markdown中提取结构化内容
function extractContentFromMarkdown(markdown: string): any {
  // 这里实现一个简单的Markdown解析逻辑
  // 实际应用中可能需要更复杂的解析逻辑

  const content: any = {
    translation: { main: "", notes: [] },
    vocabulary: { words: [] },
    sentenceStructure: { description: "" },
    grammar: { description: "", keyPoints: [] },
    knowledgePoints: {
      summary: "",
      practice: { sentence: "", translation: "" },
      tips: [],
    },
  };

  // 尝试提取翻译
  const translationMatch = markdown.match(/##\s*翻译\s*\n([\s\S]*?)(?=##|$)/i);
  if (translationMatch && translationMatch[1]) {
    content.translation.main = translationMatch[1].trim();
  }

  // 尝试提取词汇
  const vocabMatch = markdown.match(/##\s*词汇\s*\n([\s\S]*?)(?=##|$)/i);
  if (vocabMatch && vocabMatch[1]) {
    // 简单处理：假设每行是一个词汇条目 "词汇: 释义"
    const vocabLines = vocabMatch[1].trim().split("\n");
    for (const line of vocabLines) {
      if (line.includes(":")) {
        const [phrase, translation] = line.split(":").map((s) => s.trim());
        if (phrase && translation) {
          content.vocabulary.words.push({ phrase, translation });
        }
      }
    }
  }

  // 尝试提取句子结构
  const structureMatch = markdown.match(
    /##\s*句子结构\s*\n([\s\S]*?)(?=##|$)/i
  );
  if (structureMatch && structureMatch[1]) {
    content.sentenceStructure.description = structureMatch[1].trim();
  }

  // 尝试提取语法
  const grammarMatch = markdown.match(/##\s*语法\s*\n([\s\S]*?)(?=##|$)/i);
  if (grammarMatch && grammarMatch[1]) {
    content.grammar.description = grammarMatch[1].trim();
  }

  // 尝试提取知识点
  const knowledgeMatch = markdown.match(/##\s*知识点\s*\n([\s\S]*?)(?=##|$)/i);
  if (knowledgeMatch && knowledgeMatch[1]) {
    content.knowledgePoints.summary = knowledgeMatch[1].trim();
  }

  return content;
}
