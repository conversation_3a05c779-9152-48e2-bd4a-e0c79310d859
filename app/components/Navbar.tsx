import Link from "next/link";
import {
  RiBookReadLine,
  RiTranslate,
  RiPenNibLine,
  RiFileDownloadLine,
  RiBook2Line,
} from "react-icons/ri";
import { createClient } from "@/utils/supabase/server";
import MobileMenu from "./MobileMenu";
import ThemeToggle from "./ThemeToggle";
import { UserNavigation } from "./UserNavigation";

const Navbar = async () => {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  const navLinks = [
    {
      href: "/learning-resources",
      label: "资源下载",
      icon: <RiFileDownloadLine className="mr-2 text-green-500" />,
    },
    {
      href: "/exam-library",
      label: "考研英语真题逐句详解",
      icon: <RiBookReadLine className="mr-2 text-blue-500" />,
    },
  ];

  return (
    <header className="sticky top-0 z-40 bg-white border-b border-gray-200 dark:bg-gray-900 dark:border-gray-800">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link
            href="/"
            className="flex items-center text-xl font-bold text-blue-600 dark:text-blue-400"
          >
            <RiBookReadLine className="mr-2 h-6 w-6" />
            <span>考试真题资源(mbdata.site)</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-6">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="flex items-center text-sm font-medium transition-colors hover:text-blue-600 text-gray-700 dark:text-gray-300"
              >
                {link.icon}
                {link.label}
              </Link>
            ))}
          </nav>

          <div className="flex items-center space-x-4">
            {/* Auth Buttons or User Profile */}
            <UserNavigation user={user} />
            {/* Mobile Menu */}
            <MobileMenu navLinks={navLinks} user={user} />
          </div>
        </div>
      </div>
    </header>
  );
};

export default Navbar;
