import { signInAction } from "@/app/actions";
import { FormMessage, Message } from "@/components/form-message";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import Link from "next/link";
import {
  RiMailLine,
  RiLockPasswordLine,
  RiUserAddLine,
  RiCheckLine,
} from "react-icons/ri";

export default async function Login(props: {
  searchParams: Promise<Message> & { verified?: string; redirectTo?: string };
}) {
  const searchParams = await props.searchParams;
  const isVerified = props.searchParams.verified === "true";
  const redirectTo = props.searchParams.redirectTo || "";

  return (
    <>
      {isVerified && (
        <div className="mb-4 w-full p-3 bg-green-100 text-green-800 rounded-md flex items-center gap-2 dark:bg-green-900 dark:text-green-200">
          <RiCheckLine className="flex-shrink-0" />
          <span>账号验证成功，请登录您的账号</span>
        </div>
      )}

      <Card className="shadow-lg transition-all duration-300 hover:shadow-xl">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-2">
            <div className="rounded-full bg-blue-100 p-3 text-blue-600 dark:bg-blue-900 dark:text-blue-300">
              <RiLockPasswordLine size={24} />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-center">登录</CardTitle>
          <CardDescription className="text-center">
            没有账号？{" "}
            <Link
              className="text-blue-600 hover:underline dark:text-blue-400 font-medium"
              href="/sign-up"
            >
              注册
            </Link>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form className="flex flex-col gap-4">
            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium">
                邮箱
              </Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
                  <RiMailLine />
                </div>
                <Input
                  name="email"
                  id="email"
                  placeholder="<EMAIL>"
                  required
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="password" className="text-sm font-medium">
                  密码
                </Label>
                <Link
                  className="text-xs text-blue-600 hover:underline dark:text-blue-400"
                  href="/forgot-password"
                >
                  忘记密码?
                </Link>
              </div>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
                  <RiLockPasswordLine />
                </div>
                <Input
                  type="password"
                  name="password"
                  id="password"
                  placeholder="密码"
                  required
                  className="pl-10"
                />
              </div>
            </div>

            {redirectTo && (
              <Input type="hidden" name="redirectTo" value={redirectTo} />
            )}

            <SubmitButton
              pendingText="登录中..."
              formAction={signInAction}
              className="mt-2 w-full bg-blue-600 hover:bg-blue-700 text-white"
            >
              登录
            </SubmitButton>
            <FormMessage message={searchParams} />
          </form>
        </CardContent>
        <CardFooter className="flex justify-center border-t pt-4">
          <p className="text-xs text-gray-500">
            登录即表示您同意我们的服务条款和隐私政策
          </p>
        </CardFooter>
      </Card>
    </>
  );
}
