"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import TextAnalyzer from "@/app/components/TextAnalyzer";
import { analyzeText, AnalysisResult } from "@/app/services/deepseek";

interface Paper {
  id: string;
  title: string;
  year: number;
  type: string;
  sectionType: string;
  status?: string;
  createdAt: string;
  updatedAt: string;
  content?: string;
}

interface Translation {
  main: string;
  notes?: string[];
}

interface VocabularyItem {
  phrase: string;
  translation: string;
  pos?: string;
  frequency?: string;
  synonyms?: string[];
  note?: string;
  alternatives?: string[];
  ellipsis?: string;
}

interface Vocabulary {
  words: VocabularyItem[];
}

interface SentenceStructure {
  description: string;
  diagram?: string;
}

interface Grammar {
  description: string;
  keyPoints?: string[];
}

interface Practice {
  sentence: string;
  translation: string;
}

interface KnowledgePoints {
  summary: string;
  practice: Practice;
  tips?: string[];
}

interface SplitResult {
  sentences: Array<{
    id: string;
    content: string;
    paragraph_num: number;
    sequence: number;
    isAnalyzed: boolean;
    isAnalyzing?: boolean;
    explain_md?: string;
    is_marked?: boolean;
  }>;
}

const PapersPage = () => {
  const [papers, setPapers] = useState<Paper[]>([]);
  const [isAnalyzerOpen, setIsAnalyzerOpen] = useState(false);
  const [currentAnalysis, setCurrentAnalysis] = useState<SplitResult | null>(
    null
  );
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [selectedSentence, setSelectedSentence] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<"table" | "detail">("table");
  const [selectedYear, setSelectedYear] = useState("");
  const [selectedType, setSelectedType] = useState("");
  const [selectedSectionType, setSelectedSectionType] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 分页状态
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    pageSize: 10,
    totalPages: 0,
  });

  // 从数据库加载papers列表
  const fetchPapers = async () => {
    try {
      setLoading(true);
      setError(null);

      // 构建查询参数
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        pageSize: pagination.pageSize.toString(),
      });

      if (selectedYear) params.append("year", selectedYear);
      if (selectedType) params.append("type", selectedType);
      if (selectedSectionType)
        params.append("sectionType", selectedSectionType);

      const response = await fetch(`/api/papers?${params.toString()}`);

      if (!response.ok) {
        throw new Error("获取真题列表失败");
      }

      const data = await response.json();

      if (data.success) {
        setPapers(data.data);
        setPagination(data.pagination);
      } else {
        throw new Error(data.error || "获取数据失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "未知错误");
      console.error("获取真题列表错误:", err);
    } finally {
      setLoading(false);
    }
  };

  // 仅在首次加载和分页变化时获取数据
  useEffect(() => {
    fetchPapers();
  }, [pagination.page, pagination.pageSize]);

  // 处理页码变化
  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({
      ...prev,
      page: newPage,
    }));
  };

  // 处理筛选变化 - 只更新状态，不调用接口
  const handleFilterChange = (name: string, value: string) => {
    if (name === "year") {
      setSelectedYear(value);
    } else if (name === "type") {
      setSelectedType(value);
    } else if (name === "sectionType") {
      setSelectedSectionType(value);
    }
  };

  // 执行查询
  const handleSearch = () => {
    // 重置到第一页
    setPagination((prev) => ({
      ...prev,
      page: 1,
    }));

    // 调用接口获取数据
    fetchPapers();
  };

  // 清除筛选
  const clearFilters = () => {
    // 使用一个变量暂存清空后的状态
    const clearedYear = "";
    const clearedType = "";
    const clearedSectionType = "";

    // 更新状态
    setSelectedYear(clearedYear);
    setSelectedType(clearedType);
    setSelectedSectionType(clearedSectionType);
    setPagination((prev) => ({
      ...prev,
      page: 1,
    }));

    // 重置分析结果，返回真题列表
    setCurrentAnalysis(null);
    setSelectedSentence(null);
    setViewMode("table");

    // 直接构建清空后的参数调用API，而不使用状态值
    setTimeout(() => {
      // 手动构建清空后的参数
      const params = new URLSearchParams({
        page: "1",
        pageSize: pagination.pageSize.toString(),
      });

      // 调用接口，使用准备好的参数
      fetchPapersWithParams(params);
    }, 0);
  };

  // 添加一个辅助函数，使用指定的参数获取数据
  const fetchPapersWithParams = async (params: URLSearchParams) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/papers?${params.toString()}`);

      if (!response.ok) {
        throw new Error("获取真题列表失败");
      }

      const data = await response.json();

      if (data.success) {
        setPapers(data.data);
        setPagination(data.pagination);
      } else {
        throw new Error(data.error || "获取数据失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "未知错误");
      console.error("获取真题列表错误:", err);
    } finally {
      setLoading(false);
    }
  };

  // 查看试题详情
  const viewPaperDetail = async (paperId: string) => {
    try {
      setLoading(true);

      const response = await fetch(`/api/papers/${paperId}`);

      if (!response.ok) {
        throw new Error("获取试题详情失败");
      }

      const data = await response.json();

      if (data.success) {
        // 创建类似currentAnalysis的数据结构
        setCurrentAnalysis({
          sentences: data.data.sentences.map((s: any) => ({
            id: s.id,
            content: s.originalContent,
            paragraph_num: s.paragraphNum,
            sequence: s.indexNum,
            explain_md: s.explain_md,
            isAnalyzed: !!s.explain_md, // 如果有explain_md，则已分析
            isAnalyzing: false,
            is_marked: s.is_marked || false, // 读取划线句子标记
          })),
        });

        // 设置年份、类型等信息
        setSelectedYear(data.data.year);
        setSelectedType(data.data.type);
        setSelectedSectionType(data.data.sectionType);
      } else {
        throw new Error(data.error || "获取数据失败");
      }
    } catch (error) {
      console.error("获取试题详情失败:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAnalyze = async (data: {
    content: string;
    year: string;
    type: string;
    sectionType: string;
  }) => {
    try {
      setIsAnalyzing(true);
      // 第一步：调用 API 进行文本分割
      const splitResult = await fetch("/api/split", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ content: data.content }),
      }).then((res) => res.json());

      if (splitResult.error) {
        throw new Error(splitResult.error);
      }

      // 设置初始分析状态
      setCurrentAnalysis({
        sentences: splitResult.sentences.map((s: any, index: number) => ({
          id: String(index),
          content: typeof s === "string" ? s : s.content || "",
          paragraph_num: typeof s === "object" ? s.paragraph_num || 1 : 1,
          sequence: typeof s === "object" ? s.sequence || index + 1 : index + 1,
          isAnalyzed: false,
          is_marked: false, // 添加默认的is_marked字段
        })),
      });

      // 设置类型和题型信息
      setSelectedYear(data.year);
      setSelectedType(data.type);
      setSelectedSectionType(data.sectionType);

      // 创建新的试题记录
      const newPaper: Paper = {
        id: String(Date.now()),
        title: `${data.year}年全国硕士研究生招生考试${data.type}试题`,
        year: Number(data.year),
        type: data.type,
        sectionType: data.sectionType,
        status: "已完成",
        createdAt: new Date().toISOString().split("T")[0],
        updatedAt: new Date().toISOString().split("T")[0],
        content: data.content,
      };

      setPapers((prev) => [...prev, newPaper]);
      setIsAnalyzerOpen(false);
    } catch (error) {
      console.error("Analysis failed:", error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const processApiResponse = (apiResponse: any) => {
    // 处理翻译notes，确保兼容不同格式
    if (apiResponse.translation && apiResponse.translation.notes) {
      if (
        typeof apiResponse.translation.notes === "object" &&
        !Array.isArray(apiResponse.translation.notes) &&
        !apiResponse.translation.notes[0]?.original
      ) {
        // 只处理非数组对象且不是已经是数组对象的情况
        // 如果notes是对象而非数组，将其转换为字符串数组
        const notesObj = apiResponse.translation.notes;
        const notesArray: string[] = [];

        // 处理difficult_points
        if (notesObj.difficult_points) {
          notesObj.difficult_points.forEach((point: any) => {
            if (point.phrase) {
              let noteStr = `${point.phrase}`;
              if (point.alternatives && Array.isArray(point.alternatives)) {
                noteStr += `可译为：${point.alternatives.join("、")}`;
              }
              if (point.technique) {
                noteStr += `；${point.technique}`;
              }
              notesArray.push(noteStr);
            }
          });
        }

        // 替换原始notes对象
        apiResponse.translation.notes = notesArray;
      }
    }

    // 处理vocabulary中的词性字段（可能有不同的名称）
    if (apiResponse.vocabulary && apiResponse.vocabulary.words) {
      apiResponse.vocabulary.words = apiResponse.vocabulary.words.map(
        (word: any) => {
          // 统一词性字段名称
          if (word.part_of_speech && !word.pos) {
            word.pos = word.part_of_speech;
          }
          return word;
        }
      );
    }

    // 处理grammar中的关键点字段（可能有不同的名称）
    if (
      apiResponse.grammar &&
      apiResponse.grammar.key_points &&
      !apiResponse.grammar.keyPoints
    ) {
      apiResponse.grammar.keyPoints = apiResponse.grammar.key_points;
    }

    // 处理knowledgePoints中的提示字段（可能有不同的名称）
    if (apiResponse.knowledgePoints) {
      // 初始化tips为空数组，确保它始终是数组类型
      if (!apiResponse.knowledgePoints.tips) {
        apiResponse.knowledgePoints.tips = [];
      } else if (!Array.isArray(apiResponse.knowledgePoints.tips)) {
        // 如果tips不是数组，将其转换为数组
        apiResponse.knowledgePoints.tips = [apiResponse.knowledgePoints.tips];
      }

      // 处理exam_tips
      if (apiResponse.knowledgePoints.exam_tips) {
        const examTips = Array.isArray(apiResponse.knowledgePoints.exam_tips)
          ? apiResponse.knowledgePoints.exam_tips
          : [apiResponse.knowledgePoints.exam_tips];
        apiResponse.knowledgePoints.tips = [
          ...apiResponse.knowledgePoints.tips,
          ...examTips,
        ];
      }

      // 处理common_errors
      if (apiResponse.knowledgePoints.common_errors) {
        const commonErrors = Array.isArray(
          apiResponse.knowledgePoints.common_errors
        )
          ? apiResponse.knowledgePoints.common_errors
          : [apiResponse.knowledgePoints.common_errors];
        apiResponse.knowledgePoints.tips = [
          ...apiResponse.knowledgePoints.tips,
          ...commonErrors,
        ];
      }
    }

    return apiResponse;
  };

  const handleAnalyzeSentence = async (sentenceId: string) => {
    if (!currentAnalysis) return;

    try {
      const sentence = currentAnalysis.sentences.find(
        (s) => s.id === sentenceId
      );
      if (!sentence || sentence.isAnalyzed || sentence.isAnalyzing) return;

      // 设置正在分析状态
      setCurrentAnalysis((prev) => {
        if (!prev) return null;
        return {
          sentences: prev.sentences.map((s) =>
            s.id === sentenceId ? { ...s, isAnalyzing: true } : s
          ),
        };
      });

      const response = await fetch("/api/analyze-sentence", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ content: sentence.content }),
      }).then((res) => res.json());

      // 检查API返回的数据结构，并做相应处理
      if (response.error) {
        throw new Error(response.error);
      }

      // 更新状态 - 只使用explain_md字段
      setCurrentAnalysis((prev) => {
        if (!prev) return null;
        return {
          sentences: prev.sentences.map((s) =>
            s.id === sentenceId
              ? {
                  ...s,
                  isAnalyzing: false,
                  // 简化后只存储explain_md
                  explain_md: response.explanation || "暂无解析内容",
                  isAnalyzed: true,
                }
              : s
          ),
        };
      });
    } catch (error) {
      console.error("Sentence analysis failed:", error);
      // 分析失败时，重置分析状态
      setCurrentAnalysis((prev) => {
        if (!prev) return null;
        return {
          sentences: prev.sentences.map((s) =>
            s.id === sentenceId ? { ...s, isAnalyzing: false } : s
          ),
        };
      });
    }
  };

  const handleAnalyzeAll = async () => {
    if (!currentAnalysis) return;

    try {
      setIsAnalyzing(true);

      // 过滤出未分析的句子
      const unanalyzedSentences = currentAnalysis.sentences.filter(
        (s) => !s.isAnalyzed && !s.isAnalyzing
      );

      // 依次分析每个句子
      for (const sentence of unanalyzedSentences) {
        await handleAnalyzeSentence(sentence.id);
      }
    } catch (error) {
      console.error("Batch analysis failed:", error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleSaveAnalysis = async () => {
    if (!currentAnalysis) return;

    try {
      // 获取当前最新的paper记录（通常是最新添加的那个）
      const latestPaper = papers.length > 0 ? papers[papers.length - 1] : null;

      // 获取元数据
      const year = selectedYear || latestPaper?.year?.toString() || "";
      const type = selectedType || latestPaper?.type || "";
      const sectionType = selectedSectionType || latestPaper?.sectionType || "";

      // 先保存真题到papers表
      const paperResponse = await fetch("/api/papers", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          year,
          type,
          section_type: sectionType, // 注意这里使用snake_case格式来匹配后端
        }),
      });

      if (!paperResponse.ok) {
        const errorData = await paperResponse.json();
        throw new Error(errorData.error || "保存试题失败");
      }

      const paperData = await paperResponse.json();
      console.log("保存试题API返回的完整数据:", paperData);

      // 确保获取到了有效的paperId
      if (!paperData.id) {
        console.error("API返回数据中没有id字段:", paperData);
        if (paperData.data && paperData.data.id) {
          console.log("在data子对象中找到id字段");
          const paperId = paperData.data.id;
          console.log("获取到的文章ID:", paperId);

          // 组装句子数据
          const sentencesToSave = currentAnalysis.sentences
            .filter((s) => s.isAnalyzed)
            .map((s) => ({
              article_id: paperId,
              paragraph_num: s.paragraph_num || 1,
              sequence: s.sequence || 1,
              content: s.content,
              explain_md: s.explain_md || "",
              is_marked: s.is_marked || false, // 添加划线句子标记
            }));

          // 检查是否有数据要保存
          if (sentencesToSave.length === 0) {
            throw new Error("没有已分析的句子可以保存");
          }

          console.log("要保存的句子数据:", JSON.stringify(sentencesToSave));

          // 发送到服务器进行保存
          const response = await fetch("/api/sentences", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(sentencesToSave),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "保存句子失败");
          }

          alert("分析结果已保存！");
          setCurrentAnalysis(null);

          // 刷新真题列表
          setTimeout(() => {
            window.location.reload();
          }, 1000);
          return;
        }
        throw new Error("获取试题ID失败: 返回数据格式不正确");
      }

      const paperId = paperData.id;
      console.log("获取到的文章ID:", paperId);

      // 组装句子数据
      const sentencesToSave = currentAnalysis.sentences
        .filter((s) => s.isAnalyzed)
        .map((s) => ({
          article_id: paperId,
          paragraph_num: s.paragraph_num || 1,
          sequence: s.sequence || 1,
          content: s.content,
          explain_md: s.explain_md || "",
          is_marked: s.is_marked || false, // 添加划线句子标记
        }));

      // 检查是否有数据要保存
      if (sentencesToSave.length === 0) {
        throw new Error("没有已分析的句子可以保存");
      }

      console.log("要保存的句子数据:", JSON.stringify(sentencesToSave));

      // 发送到服务器进行保存
      const response = await fetch("/api/sentences", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(sentencesToSave),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "保存句子失败");
      }

      alert("分析结果已保存！");
      setCurrentAnalysis(null);

      // 刷新真题列表
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error("Save failed:", error);
      alert(
        `保存失败，请重试: ${error instanceof Error ? error.message : "未知错误"}`
      );
    }
  };

  const renderSentenceDetail = () => {
    if (!currentAnalysis || !selectedSentence) return null;

    const sentence = currentAnalysis.sentences.find(
      (s) => s.id === selectedSentence
    );
    if (!sentence) return null;

    return (
      <div className="bg-white rounded-2xl shadow-lg p-6 space-y-6">
        <div className="flex justify-between items-center border-b border-gray-100 pb-4">
          <h2 className="text-2xl font-bold text-gray-800">句子详情</h2>
          <button
            onClick={() => setViewMode("table")}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            返回列表
          </button>
        </div>

        {/* 原句 */}
        <div className="p-4 bg-gray-50 rounded-xl">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-500">
              段落 {sentence.paragraph_num || 1} · 序号 {sentence.sequence || 1}
            </span>
          </div>
          <p className="text-lg text-gray-900 leading-relaxed">
            {sentence.content}
          </p>
        </div>

        {/* 分析结果 */}
        {sentence.isAnalyzed ? (
          <div className="bg-blue-50 p-6 rounded-xl">
            <h4 className="font-semibold text-blue-800 mb-4">句子解析</h4>
            <div className="prose max-w-none">
              {/* 使用markdown渲染 */}
              <div
                className="text-gray-800"
                dangerouslySetInnerHTML={{
                  __html: sentence.explain_md || "暂无解析内容",
                }}
              />
            </div>
          </div>
        ) : (
          <div className="flex justify-center py-8">
            {sentence.isAnalyzing ? (
              <div className="flex flex-col items-center space-y-4">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
                <p className="text-blue-700 font-medium">正在分析中...</p>
              </div>
            ) : (
              <button
                onClick={() => handleAnalyzeSentence(sentence.id)}
                className="px-6 py-3 bg-blue-600 text-white text-lg rounded-xl hover:bg-blue-700 transition-colors"
              >
                分析此句
              </button>
            )}
          </div>
        )}
      </div>
    );
  };

  const renderSentenceTable = () => {
    if (!currentAnalysis) return null;

    const analyzedCount = currentAnalysis.sentences.filter(
      (s) => s.isAnalyzed
    ).length;
    const totalCount = currentAnalysis.sentences.length;

    // 检查是否为英语一的翻译题型
    const isEnglishOneTranslation =
      selectedType === "英语一" && selectedSectionType === "translation";

    return (
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
        <div className="p-6 border-b border-gray-100">
          <div className="flex justify-between items-center">
            <div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => {
                    setCurrentAnalysis(null);
                    setSelectedSentence(null);
                  }}
                  className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10 19l-7-7m0 0l7-7m-7 7h18"
                    />
                  </svg>
                  返回列表
                </button>
                <h2 className="text-2xl font-bold text-gray-800">分析结果</h2>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                已分析 {analyzedCount}/{totalCount} 句
              </p>
              {isEnglishOneTranslation && (
                <p className="text-sm text-blue-500 mt-1">
                  英语一翻译题型：请勾选需要作为划线句子的内容
                </p>
              )}
            </div>
            <div className="flex space-x-4">
              <button
                onClick={() => {
                  setCurrentAnalysis((prev) => ({
                    sentences: prev!.sentences.map((s) => ({
                      ...s,
                      isAnalyzed: false,
                      isAnalyzing: false,
                      explain_md: undefined,
                    })),
                  }));
                  handleAnalyzeAll();
                }}
                disabled={isAnalyzing}
                className={`px-6 py-3 text-white rounded-xl hover:shadow-lg transform hover:scale-105 transition-all duration-200 font-medium ${
                  isAnalyzing
                    ? "bg-gray-400 cursor-not-allowed"
                    : "bg-gradient-to-r from-purple-600 to-purple-700"
                }`}
              >
                {isAnalyzing ? "分析中..." : "重新分析全部"}
              </button>
              <button
                onClick={handleAnalyzeAll}
                disabled={isAnalyzing || analyzedCount === totalCount}
                className={`px-6 py-3 text-white rounded-xl hover:shadow-lg transform hover:scale-105 transition-all duration-200 font-medium ${
                  isAnalyzing || analyzedCount === totalCount
                    ? "bg-gray-400 cursor-not-allowed"
                    : "bg-gradient-to-r from-blue-600 to-blue-700"
                }`}
              >
                {isAnalyzing ? (
                  <span className="flex items-center">
                    <svg
                      className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    正在分析...
                  </span>
                ) : (
                  "分析未完成句子"
                )}
              </button>
              <button
                onClick={handleSaveAnalysis}
                disabled={analyzedCount === 0}
                className={`px-6 py-3 text-white rounded-xl hover:shadow-lg transform hover:scale-105 transition-all duration-200 font-medium ${
                  analyzedCount === 0
                    ? "bg-gray-400 cursor-not-allowed"
                    : "bg-gradient-to-r from-green-600 to-green-700"
                }`}
              >
                保存分析结果
              </button>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                  序号
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                  位置
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  英文句子
                </th>
                {isEnglishOneTranslation && (
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                    划线句子
                  </th>
                )}
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                  状态
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-100">
              {currentAnalysis.sentences.map((sentence, index) => (
                <tr
                  key={sentence.id}
                  className="hover:bg-gray-50 transition-colors"
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {index + 1}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    段落{sentence.paragraph_num || 1}-{sentence.sequence || 1}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    <div className="line-clamp-2">{sentence.content}</div>
                  </td>
                  {isEnglishOneTranslation && (
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center">
                      <input
                        type="checkbox"
                        checked={sentence.is_marked}
                        onChange={(e) => {
                          setCurrentAnalysis((prev) => {
                            if (!prev) return null;
                            return {
                              sentences: prev.sentences.map((s) =>
                                s.id === sentence.id
                                  ? { ...s, is_marked: e.target.checked }
                                  : s
                              ),
                            };
                          });
                        }}
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                    </td>
                  )}
                  <td className="px-6 py-4 whitespace-nowrap">
                    {sentence.isAnalyzing ? (
                      <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                        分析中...
                      </span>
                    ) : sentence.isAnalyzed ? (
                      <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        已分析
                      </span>
                    ) : (
                      <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                        未分析
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    {sentence.isAnalyzing ? (
                      <span className="text-gray-400">处理中...</span>
                    ) : sentence.isAnalyzed ? (
                      <button
                        onClick={() => {
                          setSelectedSentence(sentence.id);
                          setViewMode("detail");
                        }}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        查看详情
                      </button>
                    ) : (
                      <button
                        onClick={() => handleAnalyzeSentence(sentence.id)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        分析此句
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  // 历史记录表格
  const renderPapersTable = () => {
    return (
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
        <div className="p-6 border-b border-gray-100">
          <h2 className="text-2xl font-bold text-gray-800">真题列表</h2>
          <p className="text-sm text-gray-500 mt-1">
            共 {pagination.total} 条记录
          </p>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
          </div>
        ) : error ? (
          <div className="py-10 text-center">
            <p className="text-red-500">{error}</p>
            <button
              onClick={fetchPapers}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              重试
            </button>
          </div>
        ) : papers.length === 0 ? (
          <div className="py-16 text-center">
            <p className="text-gray-500 text-lg">暂无数据</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">
                    标题
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">
                    年份
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">
                    类型
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">
                    题型
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">
                    创建时间
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-100">
                {papers.map((paper) => (
                  <tr
                    key={paper.id}
                    className="hover:bg-gray-50 transition-colors duration-200"
                  >
                    <td className="px-6 py-4 text-sm text-gray-900 font-medium">
                      {paper.title}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-600">
                      {paper.year}年
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-600">
                      {paper.type}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-600">
                      {paper.sectionType === "use_of_english"
                        ? "完形填空"
                        : paper.sectionType === "reading_comprehension"
                          ? "阅读理解"
                          : paper.sectionType === "translation"
                            ? "翻译"
                            : paper.sectionType === "writing"
                              ? "写作"
                              : paper.sectionType}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-600">
                      {new Date(paper.createdAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 text-sm space-x-3">
                      <button
                        onClick={() => viewPaperDetail(paper.id)}
                        className="text-blue-600 hover:text-blue-800 font-medium"
                      >
                        查看句子
                      </button>
                      <Link
                        href={`/admin/sentences?year=${paper.year}&type=${paper.type}&sectionType=${paper.sectionType}`}
                        className="text-blue-600 hover:text-blue-800 font-medium"
                      >
                        管理句子
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* 分页 */}
        {pagination.totalPages > 1 && (
          <div className="px-6 py-4 flex justify-between items-center border-t border-gray-200">
            <div className="text-sm text-gray-500">
              显示 {(pagination.page - 1) * pagination.pageSize + 1} -{" "}
              {Math.min(
                pagination.page * pagination.pageSize,
                pagination.total
              )}{" "}
              条，共 {pagination.total} 条
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page === 1}
                className={`px-3 py-1 rounded ${
                  pagination.page === 1
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
              >
                上一页
              </button>
              {Array.from(
                { length: Math.min(5, pagination.totalPages) },
                (_, i) => {
                  // 显示当前页附近的页码
                  let pageNum: number;
                  if (pagination.totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (pagination.page <= 3) {
                    pageNum = i + 1;
                  } else if (pagination.page >= pagination.totalPages - 2) {
                    pageNum = pagination.totalPages - 4 + i;
                  } else {
                    pageNum = pagination.page - 2 + i;
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`px-3 py-1 rounded ${
                        pagination.page === pageNum
                          ? "bg-blue-500 text-white"
                          : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                }
              )}
              <button
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page === pagination.totalPages}
                className={`px-3 py-1 rounded ${
                  pagination.page === pagination.totalPages
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
              >
                下一页
              </button>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="w-full space-y-8">
      {/* 顶部操作栏 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
            真题管理
          </h1>
          <p className="text-gray-500 mt-2 text-lg">管理和分析考研英语真题</p>
        </div>
        <button
          onClick={() => setIsAnalyzerOpen(true)}
          className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white text-lg rounded-xl hover:shadow-lg transform hover:scale-105 transition-all duration-200 font-medium"
        >
          分析新内容
        </button>
      </div>

      {/* 筛选器 */}
      <div className="bg-white p-6 rounded-2xl shadow-lg">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="space-y-2">
            <label className="text-gray-700 text-lg font-medium">年份</label>
            <select
              value={selectedYear}
              onChange={(e) => handleFilterChange("year", e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
            >
              <option value="">所有年份</option>
              {Array.from({ length: 16 }, (_, i) => 2010 + i).map((year) => (
                <option key={year} value={year.toString()}>
                  {year}年
                </option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="text-gray-700 text-lg font-medium">类型</label>
            <select
              value={selectedType}
              onChange={(e) => handleFilterChange("type", e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
            >
              <option value="">所有类型</option>
              <option value="英语一">英语一</option>
              <option value="英语二">英语二</option>
            </select>
          </div>

          <div className="space-y-2">
            <label className="text-gray-700 text-lg font-medium">题型</label>
            <select
              value={selectedSectionType}
              onChange={(e) =>
                handleFilterChange("sectionType", e.target.value)
              }
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
            >
              <option value="">所有题型</option>
              <option value="use_of_english">完形填空</option>
              <option value="reading_comprehension">阅读理解</option>
              <option value="translation">翻译</option>
              <option value="writing">写作</option>
            </select>
          </div>
        </div>
        <div className="flex justify-end mt-4 space-x-4">
          <button
            onClick={clearFilters}
            className="px-4 py-2 text-blue-600 hover:text-blue-800 font-medium"
          >
            清除筛选
          </button>
          <button
            onClick={handleSearch}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            查询
          </button>
        </div>
      </div>

      {/* 分析结果展示 */}
      {currentAnalysis &&
        (viewMode === "table" ? renderSentenceTable() : renderSentenceDetail())}

      {/* 当无分析结果时显示真题列表 */}
      {!currentAnalysis && renderPapersTable()}

      <TextAnalyzer
        isOpen={isAnalyzerOpen}
        onClose={() => setIsAnalyzerOpen(false)}
        onAnalyze={handleAnalyze}
      />
    </div>
  );
};

export default PapersPage;
