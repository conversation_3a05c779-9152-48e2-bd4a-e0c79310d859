import { ExamLibrary } from "../components/ui/exam-library";
import ResourcesSidebar from "./components/ResourcesSidebar";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "考研英语真题逐句详解 | 面包资料屋",
  description:
    "提供考研英语一、英语二历年真题的逐句详解，包含完形填空、阅读理解、翻译和写作，帮助你系统备考",
};

export default function ExamLibraryPage() {
  return (
    <main className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <div className="lg:col-span-3">
            <ExamLibrary />
          </div>
          <div className="lg:col-span-1">
            <ResourcesSidebar />
          </div>
        </div>
      </div>
    </main>
  );
}
