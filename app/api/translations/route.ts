import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { DeepSeek } from "@/app/services/deepseek";

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// GET方法：获取所有翻译
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const paperId = searchParams.get("paperId");

    // 构建查询
    let query = supabase
      .from("translations")
      .select("*, papers(year, type, section_type)");

    // 添加过滤条件
    if (paperId) {
      query = query.eq("paper_id", paperId);
    }

    // 执行查询
    const { data, error } = await query.order("created_at", {
      ascending: false,
    });

    if (error) {
      throw error;
    }

    return NextResponse.json({
      success: true,
      data: data,
    });
  } catch (error) {
    console.error("Error fetching translations:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch translations",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// POST方法：创建新翻译
export async function POST(request: Request) {
  try {
    const {
      paperId,
      content,
      generateAITranslation = false,
    } = await request.json();

    if (!paperId || !content) {
      return NextResponse.json(
        { error: "Paper ID and content are required" },
        { status: 400 }
      );
    }

    // 检查paper_id是否存在
    const { data: paperData, error: paperError } = await supabase
      .from("papers")
      .select("id")
      .eq("id", paperId)
      .single();

    if (paperError || !paperData) {
      return NextResponse.json({ error: "Paper not found" }, { status: 404 });
    }

    // 检查是否已存在该paper_id的翻译
    const { data: existingTranslations, error: translationError } =
      await supabase.from("translations").select("id").eq("paper_id", paperId);

    if (translationError) {
      throw translationError;
    }

    let translationId;
    let referenceTranslation = null;
    let analysis = null;

    // 如果请求生成AI翻译
    if (generateAITranslation) {
      // 调用DeepSeek API生成翻译和分析
      const aiResult = await generateTranslationAnalysis(content);
      referenceTranslation = aiResult.reference_translation;
      analysis = aiResult.analysis;
    }

    if (existingTranslations && existingTranslations.length > 0) {
      // 更新现有记录
      const { error: updateError } = await supabase
        .from("translations")
        .update({
          content: content,
          reference_translation: referenceTranslation,
          analysis: analysis,
        })
        .eq("paper_id", paperId);

      if (updateError) {
        throw updateError;
      }

      translationId = existingTranslations[0].id;
    } else {
      // 创建新记录
      const { data: insertData, error: insertError } = await supabase
        .from("translations")
        .insert({
          paper_id: paperId,
          content: content,
          reference_translation: referenceTranslation,
          analysis: analysis,
        })
        .select("id")
        .single();

      if (insertError) {
        throw insertError;
      }

      translationId = insertData.id;
    }

    return NextResponse.json({
      success: true,
      message: "Translation saved successfully",
      data: {
        id: translationId,
        reference_translation: referenceTranslation,
        analysis: analysis,
      },
    });
  } catch (error) {
    console.error("Error saving translation:", error);
    return NextResponse.json(
      {
        error: "Failed to save translation",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// 生成翻译和分析的辅助函数
async function generateTranslationAnalysis(content: string) {
  const prompt = `
你是一位精通英汉翻译的考研英语专家。请对以下英语段落进行翻译分析。

原文：
${content}

请提供以下内容：
1. 准确、地道的中文翻译
2. 翻译分析（包括难点、关键词汇和表达方式等）

请以JSON格式返回分析结果，格式如下：
{
  "reference_translation": "完整的中文翻译",
  "analysis": "详细的翻译分析，包括翻译难点、应用的翻译技巧等"
}

JSON格式注意事项：
1. 必须使用英文双引号(")作为JSON的引号，不能使用中文引号("")
2. 在JSON内容中，不要使用撇号(')表示缩写，如don't、we're等，请写成完整形式：do not、we are
3. 如需在内容中使用引号，请用转义形式(\\")
4. 返回的JSON必须是严格有效的，只包含reference_translation和analysis两个字段
5. 不要添加任何前缀或后缀，直接返回JSON对象

非常重要：确保你的回复是能被JSON.parse()直接解析的有效JSON格式。
`;

  try {
    const deepseek = new DeepSeek();
    const response = await deepseek.chat({
      messages: [{ role: "user", content: prompt }],
    });

    const responseContent = response.choices[0].message.content.trim();
    console.log("AI返回的原始内容:", responseContent);

    // 尝试提取有效的JSON
    let jsonMatch = responseContent.match(/\{[\s\S]*\}/);
    let jsonStr = jsonMatch ? jsonMatch[0] : responseContent;

    // 替换可能存在的中文引号
    jsonStr = jsonStr.replace(/[""]|['']|[「」]/g, '"');

    // 尝试解析JSON
    try {
      const result = JSON.parse(jsonStr);

      // 验证返回的数据格式是否正确
      if (!result.reference_translation || !result.analysis) {
        throw new Error("AI返回的数据缺少必要字段");
      }

      // 确保只返回需要的字段
      return {
        reference_translation: result.reference_translation,
        analysis: result.analysis,
      };
    } catch (parseError) {
      console.error("JSON解析错误:", parseError);
      console.error("尝试解析的JSON字符串:", jsonStr);

      // 构建一个替代响应
      return {
        reference_translation: "AI生成的翻译格式有误，请重新提交",
        analysis: "无法解析AI返回的分析，请稍后再试",
      };
    }
  } catch (error) {
    console.error("调用AI接口错误:", error);
    return {
      reference_translation: "调用AI翻译服务失败，请稍后再试",
      analysis: "无法获取翻译分析",
    };
  }
}
