"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "../../../components/ui/textarea";
import { Switch } from "../../../components/ui/switch";
import { Loader2 } from "lucide-react";

const NewTranslationPage = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    year: "",
    type: "英语二",
    content: "",
    generateAITranslation: true,
  });

  // 处理输入变化
  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // 处理切换变化
  const handleSwitchChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, generateAITranslation: checked }));
  };

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.year || !formData.content) {
      setError("年份和翻译内容为必填项");
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // 先创建试题
      const paperResponse = await fetch("/api/papers", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          year: formData.year,
          type: formData.type,
          section_type: "translation",
        }),
      });

      if (!paperResponse.ok) {
        const errorData = await paperResponse.json();
        throw new Error(errorData.error || "创建试题失败");
      }

      const paperData = await paperResponse.json();
      const paperId = paperData.data.id;

      // 然后添加翻译内容
      if (formData.generateAITranslation) {
        // 使用AI生成翻译
        const translateResponse = await fetch("/api/translate", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            type: "paragraph",
            content: formData.content,
            paperId: paperId,
          }),
        });

        if (!translateResponse.ok) {
          const errorData = await translateResponse.json();
          throw new Error(errorData.error || "生成AI翻译失败");
        }
      } else {
        // 不使用AI，直接更新papers表
        const updateResponse = await fetch(`/api/papers/${paperId}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            content: formData.content,
          }),
        });

        if (!updateResponse.ok) {
          const errorData = await updateResponse.json();
          throw new Error(errorData.error || "保存翻译内容失败");
        }
      }

      router.push("/admin/translations");
    } catch (err) {
      setError(err instanceof Error ? err.message : "创建翻译失败");
      console.error("创建翻译错误:", err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">添加英语二整段翻译</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>新建翻译</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="bg-red-50 p-4 rounded-md border border-red-200 text-red-600">
                {error}
              </div>
            )}

            <div className="grid grid-cols-1 gap-4">
              <div>
                <Label htmlFor="year">年份</Label>
                <Input
                  id="year"
                  name="year"
                  value={formData.year}
                  onChange={handleInputChange}
                  placeholder="请输入年份，如：2023"
                  className="mt-1"
                />
                <p className="text-sm text-gray-500 mt-1">
                  请输入对应的试题年份
                </p>
              </div>

              <div>
                <Label htmlFor="content">翻译原文</Label>
                <Textarea
                  id="content"
                  name="content"
                  value={formData.content}
                  onChange={handleInputChange}
                  required
                  className="mt-1 min-h-[200px]"
                  placeholder="请输入需要翻译的英文段落..."
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="generateAITranslation"
                  checked={formData.generateAITranslation}
                  onCheckedChange={handleSwitchChange}
                />
                <Label htmlFor="generateAITranslation">
                  使用AI生成参考翻译和分析
                </Label>
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/admin/translations")}
              >
                取消
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isLoading ? "提交中..." : "提交"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default NewTranslationPage;
