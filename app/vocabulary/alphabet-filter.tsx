"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useState, useEffect } from "react";
import { AlphabetLetter } from "@/types/vocabulary";

const ALPHABET: AlphabetLetter[] = [
  "a",
  "b",
  "c",
  "d",
  "e",
  "f",
  "g",
  "h",
  "i",
  "j",
  "k",
  "l",
  "m",
  "n",
  "o",
  "p",
  "q",
  "r",
  "s",
  "t",
  "u",
  "v",
  "w",
  "x",
  "y",
  "z",
];

// 字母单词计数接口
interface LetterCounts {
  [key: string]: number;
}

export default function AlphabetFilter() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeLetter, setActiveLetter] = useState<string>(
    searchParams.get("letter") || "a"
  );
  const [isLoading, setIsLoading] = useState(false);
  const [letterCounts, setLetterCounts] = useState<LetterCounts>({});
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // 获取每个字母的单词数量
  useEffect(() => {
    async function fetchLetterCounts() {
      try {
        const response = await fetch("/api/vocabulary/letter-counts");
        const data = await response.json();
        setLetterCounts(data.counts || {});
      } catch (error) {
        console.error("Failed to fetch letter counts:", error);
      }
    }

    fetchLetterCounts();
  }, []);

  const handleLetterClick = (letter: string) => {
    if (letter === activeLetter) return;

    setActiveLetter(letter);
    setIsLoading(true);

    const params = new URLSearchParams(searchParams.toString());
    params.set("letter", letter);

    router.push(`/vocabulary?${params.toString()}`);
  };

  useEffect(() => {
    const letterParam = searchParams.get("letter");
    setActiveLetter(letterParam || "a");
    setIsLoading(false);
  }, [searchParams]);

  // 切换视图模式
  const toggleViewMode = () => {
    setViewMode(viewMode === "grid" ? "list" : "grid");
  };

  // 网格视图
  if (viewMode === "grid") {
    return (
      <div className="w-full">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-gray-700">按字母浏览</h2>
          <button
            onClick={toggleViewMode}
            className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
          >
            切换到列表视图
          </button>
        </div>

        <div className="grid grid-cols-4 sm:grid-cols-7 md:grid-cols-9 lg:grid-cols-13 gap-2">
          {ALPHABET.map((letter) => (
            <div
              key={letter}
              onClick={() => handleLetterClick(letter)}
              className={`rounded-lg border py-2 px-1 flex flex-col items-center justify-center cursor-pointer transition-colors hover:border-blue-500 hover:shadow-sm ${
                activeLetter === letter
                  ? "bg-blue-50 border-blue-500 shadow-sm"
                  : "bg-white border-gray-200"
              } ${!letterCounts[letter] ? "opacity-50" : ""}`}
            >
              <div className="text-xl font-bold text-blue-600 uppercase">
                {letter}
              </div>
              <div className="text-xs text-gray-500">
                {letterCounts[letter] || 0}
              </div>
            </div>
          ))}
        </div>

        {isLoading && (
          <div className="fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-full shadow-lg">
            正在加载...
          </div>
        )}
      </div>
    );
  }

  // 列表视图
  return (
    <div className="relative w-full">
      <div className="flex justify-between items-center mb-2">
        <h2 className="text-lg font-semibold text-gray-700">字母筛选</h2>
        <button
          onClick={toggleViewMode}
          className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
        >
          切换到网格视图
        </button>
      </div>

      <div className="w-full flex flex-wrap gap-1 justify-center">
        {ALPHABET.map((letter) => (
          <button
            key={letter}
            onClick={() => handleLetterClick(letter)}
            className={`w-8 h-8 flex items-center justify-center rounded-md text-sm font-medium uppercase ${
              activeLetter === letter
                ? "bg-blue-600 text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            } ${isLoading ? "opacity-50 cursor-not-allowed" : ""} ${
              !letterCounts[letter] ? "opacity-50" : ""
            }`}
            disabled={isLoading}
          >
            {letter}
          </button>
        ))}
      </div>

      {isLoading && (
        <div className="absolute top-10 left-0 right-0 text-center mt-2">
          <div className="inline-block px-3 py-1 rounded-full bg-blue-100 text-blue-700 text-xs">
            加载中...
          </div>
        </div>
      )}
    </div>
  );
}
