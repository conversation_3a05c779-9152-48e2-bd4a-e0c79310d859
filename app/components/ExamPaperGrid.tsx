"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";

interface Paper {
  id: string;
  title: string;
  year: number;
  type: string;
  sectionType: string;
  createdAt: string;
}

const ExamPaperGrid = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [papers, setPapers] = useState<Paper[]>([]);
  const [selectedYear, setSelectedYear] = useState(
    searchParams?.get("year") || ""
  );
  const [selectedType, setSelectedType] = useState(
    searchParams?.get("type") || ""
  );
  const [selectedSectionType, setSelectedSectionType] = useState(
    searchParams?.get("sectionType") || ""
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 分页状态
  const [pagination, setPagination] = useState({
    total: 0,
    page: parseInt(searchParams?.get("page") || "1"),
    pageSize: 12, // 每页显示更多卡片
    totalPages: 0,
  });

  // 从数据库加载papers列表
  const fetchPapers = async () => {
    try {
      setLoading(true);
      setError(null);

      // 构建查询参数
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        pageSize: pagination.pageSize.toString(),
      });

      if (selectedYear) params.append("year", selectedYear);
      if (selectedType) params.append("type", selectedType);
      if (selectedSectionType)
        params.append("sectionType", selectedSectionType);

      const response = await fetch(`/api/papers?${params.toString()}`);

      if (!response.ok) {
        throw new Error("获取真题列表失败");
      }

      const data = await response.json();

      if (data.success) {
        setPapers(data.data);
        setPagination(data.pagination);
      } else {
        throw new Error(data.error || "获取数据失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "未知错误");
      console.error("获取真题列表错误:", err);
    } finally {
      setLoading(false);
    }
  };

  // 初始加载和筛选条件变化时获取数据
  useEffect(() => {
    fetchPapers();

    // 更新URL查询参数，不触发页面刷新
    const params = new URLSearchParams();
    if (selectedYear) params.set("year", selectedYear);
    if (selectedType) params.set("type", selectedType);
    if (selectedSectionType) params.set("sectionType", selectedSectionType);
    if (pagination.page > 1) params.set("page", pagination.page.toString());

    const url = `${window.location.pathname}${params.toString() ? "?" + params.toString() : ""}`;
    window.history.replaceState({}, "", url);
  }, [selectedYear, selectedType, selectedSectionType, pagination.page]);

  // 处理筛选变化
  const handleFilterChange = (name: string, value: string) => {
    // 重置到第一页
    setPagination((prev) => ({
      ...prev,
      page: 1,
    }));

    if (name === "year") {
      setSelectedYear(value);
    } else if (name === "type") {
      setSelectedType(value);
    } else if (name === "sectionType") {
      setSelectedSectionType(value);
    }
  };

  // 清除筛选
  const clearFilters = () => {
    setSelectedYear("");
    setSelectedType("");
    setSelectedSectionType("");
    setPagination((prev) => ({
      ...prev,
      page: 1,
    }));
  };

  // 处理页码变化
  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({
      ...prev,
      page: newPage,
    }));
  };

  // 获取题型显示名称
  const getSectionTypeDisplayName = (sectionType: string) => {
    switch (sectionType) {
      case "use_of_english":
        return "完形填空";
      case "reading_comprehension":
        return "阅读理解";
      case "translation":
        return "翻译";
      case "writing":
        return "写作";
      default:
        return sectionType;
    }
  };

  return (
    <div className="space-y-8">
      {/* 筛选区域 */}
      <div className="bg-white rounded-xl shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">筛选真题</h2>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* 年份筛选 */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">年份</label>
            <select
              value={selectedYear}
              onChange={(e) => handleFilterChange("year", e.target.value)}
              className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
            >
              <option value="">所有年份</option>
              {Array.from({ length: 16 }, (_, i) => 2010 + i).map((year) => (
                <option key={year} value={year.toString()}>
                  {year}年
                </option>
              ))}
            </select>
          </div>

          {/* 类型筛选 */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">类型</label>
            <select
              value={selectedType}
              onChange={(e) => handleFilterChange("type", e.target.value)}
              className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
            >
              <option value="">所有类型</option>
              <option value="英语一">英语一</option>
              <option value="英语二">英语二</option>
            </select>
          </div>

          {/* 题型筛选 */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">题型</label>
            <select
              value={selectedSectionType}
              onChange={(e) =>
                handleFilterChange("sectionType", e.target.value)
              }
              className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
            >
              <option value="">所有题型</option>
              <option value="use_of_english">完形填空</option>
              <option value="reading_comprehension">阅读理解</option>
              <option value="translation">翻译</option>
              <option value="writing">写作</option>
            </select>
          </div>

          {/* 按钮组 */}
          <div className="flex items-end space-x-2">
            <button
              onClick={clearFilters}
              className="px-4 py-2 text-sm text-blue-600 hover:text-blue-800 font-medium"
            >
              重置
            </button>
            <button
              onClick={fetchPapers}
              className="flex-grow px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
            >
              查询
            </button>
          </div>
        </div>
      </div>

      {/* 真题列表展示 */}
      <div>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-gray-800">真题库</h2>
          <p className="text-gray-500">共 {pagination.total} 套真题</p>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
          </div>
        ) : error ? (
          <div className="bg-white rounded-xl shadow p-8 text-center">
            <p className="text-red-500 mb-4">{error}</p>
            <button
              onClick={fetchPapers}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              重试
            </button>
          </div>
        ) : papers.length === 0 ? (
          <div className="bg-white rounded-xl shadow p-12 text-center">
            <p className="text-lg text-gray-500">暂无符合条件的真题</p>
            <button
              onClick={clearFilters}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              清除筛选条件
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {papers.map((paper) => (
              <Link
                href={`/papers/${paper.id}`}
                key={paper.id}
                className="group transition-all duration-300 hover:shadow-lg"
              >
                <div className="bg-white h-full rounded-xl shadow overflow-hidden flex flex-col">
                  {/* 卡片顶部 - 年份和类型 */}
                  <div className="bg-blue-50 px-5 py-3 flex justify-between items-center">
                    <span className="font-bold text-blue-900">
                      {paper.year}年
                    </span>
                    <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                      {paper.type}
                    </span>
                  </div>

                  {/* 卡片内容 */}
                  <div className="p-5 flex-grow flex flex-col">
                    <h3 className="text-lg font-semibold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors">
                      {paper.title}
                    </h3>

                    <div className="mt-2 flex-grow flex flex-col justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center text-sm text-gray-600">
                          <span className="bg-blue-50 text-blue-700 px-3 py-1 rounded-md">
                            {getSectionTypeDisplayName(paper.sectionType)}
                          </span>
                        </div>
                      </div>

                      <div className="mt-4 pt-3 border-t border-gray-100 flex justify-between items-center">
                        <span className="text-xs text-gray-500">
                          {new Date(paper.createdAt).toLocaleDateString()}
                        </span>
                        <span className="text-blue-600 text-sm font-medium">
                          查看详情 →
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}

        {/* 分页控制 */}
        {pagination.totalPages > 1 && (
          <div className="mt-10 flex justify-center">
            <div className="flex space-x-1">
              <button
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page === 1}
                className={`px-4 py-2 rounded-md ${
                  pagination.page === 1
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"
                }`}
              >
                上一页
              </button>

              {Array.from(
                { length: Math.min(5, pagination.totalPages) },
                (_, i) => {
                  // 显示当前页附近的页码
                  let pageNum: number;
                  if (pagination.totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (pagination.page <= 3) {
                    pageNum = i + 1;
                  } else if (pagination.page >= pagination.totalPages - 2) {
                    pageNum = pagination.totalPages - 4 + i;
                  } else {
                    pageNum = pagination.page - 2 + i;
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`w-10 h-10 flex items-center justify-center rounded-md ${
                        pagination.page === pageNum
                          ? "bg-blue-600 text-white"
                          : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                }
              )}

              <button
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page === pagination.totalPages}
                className={`px-4 py-2 rounded-md ${
                  pagination.page === pagination.totalPages
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"
                }`}
              >
                下一页
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExamPaperGrid;
