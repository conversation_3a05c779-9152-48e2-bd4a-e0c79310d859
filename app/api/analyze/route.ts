import { NextResponse } from "next/server";
import OpenAI from "openai";

const openai = new OpenAI({
  baseURL: "https://api.deepseek.com",
  apiKey: process.env.DEEPSEEK_API_KEY,
});

export async function POST(request: Request) {
  try {
    const { content, prompt } = await request.json();

    const completion = await openai.chat.completions.create({
      messages: [
        {
          role: "system",
          content: "你是一个专业的英语考试分析助手，擅长分析考研英语真题。",
        },
        {
          role: "user",
          content: `${prompt}\n\n文本内容：\n${content}`,
        },
      ],
      model: "deepseek-chat",
    });

    const response = completion.choices[0].message.content;

    return NextResponse.json({ result: response });
  } catch (error) {
    console.error("DeepSeek API call failed:", error);
    return NextResponse.json({ error: "Analysis failed" }, { status: 500 });
  }
}
