"use client";

import React from "react";
import { Card, CardContent, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface WritingAnalysisProps {
  content: string;
}

// 定义英文键名到中文键名的映射
const keyMapping: Record<string, string> = {
  writing_thoughts: "写作思路",
  key_points: "重点难点分析",
  high_score_elements: "高分词汇和句式示例",
  common_errors: "常见写作误区提醒",
  scoring_tips: "得分要点提示",
};

export const WritingAnalysisPreview: React.FC<WritingAnalysisProps> = ({
  content,
}) => {
  try {
    // 尝试解析JSON格式的写作分析
    const originalData = JSON.parse(content);

    // 创建一个新对象，将英文键名转换为中文键名
    const analysisData: Record<string, any> = {};

    // 处理英文键名映射
    Object.entries(originalData).forEach(([key, value]) => {
      const chineseKey = keyMapping[key] || key;
      analysisData[chineseKey] = value;
    });

    return (
      <div className="space-y-6">
        {/* 写作思路部分 */}
        {analysisData.写作思路 && (
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="pt-6">
              <CardTitle className="text-lg font-medium mb-3 text-blue-700">
                写作思路
              </CardTitle>
              <div className="pl-2 border-l-2 border-blue-300">
                {typeof analysisData.写作思路 === "string" ? (
                  <div className="space-y-2">
                    {analysisData.写作思路
                      .split("\n")
                      .map((line: string, index: number) => (
                        <p key={index} className="text-gray-700">
                          {line}
                        </p>
                      ))}
                  </div>
                ) : (
                  <pre className="text-sm">
                    {JSON.stringify(analysisData.写作思路, null, 2)}
                  </pre>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 重点难点分析部分 */}
        {analysisData.重点难点分析 && (
          <Card className="bg-amber-50 border-amber-200">
            <CardContent className="pt-6">
              <CardTitle className="text-lg font-medium mb-3 text-amber-700">
                重点难点分析
              </CardTitle>
              <div className="pl-2 border-l-2 border-amber-300">
                {typeof analysisData.重点难点分析 === "string" ? (
                  <div className="space-y-2">
                    {analysisData.重点难点分析
                      .split("\n")
                      .map((line: string, index: number) => (
                        <p key={index} className="text-gray-700">
                          {line}
                        </p>
                      ))}
                  </div>
                ) : (
                  <pre className="text-sm">
                    {JSON.stringify(analysisData.重点难点分析, null, 2)}
                  </pre>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 高分词汇和句式示例 */}
        {analysisData.高分词汇和句式示例 && (
          <Card className="bg-green-50 border-green-200">
            <CardContent className="pt-6">
              <CardTitle className="text-lg font-medium mb-3 text-green-700">
                高分词汇和句式示例
              </CardTitle>
              <div className="grid grid-cols-1 gap-2">
                {Array.isArray(analysisData.高分词汇和句式示例) ? (
                  analysisData.高分词汇和句式示例.map(
                    (item: string, index: number) => (
                      <div key={index} className="flex items-start">
                        <Badge className="bg-green-100 text-green-800 mr-2 mt-1">
                          例{index + 1}
                        </Badge>
                        <p className="text-gray-700">{item}</p>
                      </div>
                    )
                  )
                ) : (
                  <pre className="text-sm">
                    {JSON.stringify(analysisData.高分词汇和句式示例, null, 2)}
                  </pre>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 常见写作误区提醒 */}
        {analysisData.常见写作误区提醒 && (
          <Card className="bg-red-50 border-red-200">
            <CardContent className="pt-6">
              <CardTitle className="text-lg font-medium mb-3 text-red-700">
                常见写作误区提醒
              </CardTitle>
              <div className="grid grid-cols-1 gap-2">
                {Array.isArray(analysisData.常见写作误区提醒) ? (
                  analysisData.常见写作误区提醒.map(
                    (item: string, index: number) => (
                      <div key={index} className="flex items-start">
                        <Badge className="bg-red-100 text-red-800 mr-2 mt-1">
                          注意{index + 1}
                        </Badge>
                        <p className="text-gray-700">{item}</p>
                      </div>
                    )
                  )
                ) : (
                  <pre className="text-sm">
                    {JSON.stringify(analysisData.常见写作误区提醒, null, 2)}
                  </pre>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 得分要点提示 */}
        {analysisData.得分要点提示 && (
          <Card className="bg-purple-50 border-purple-200">
            <CardContent className="pt-6">
              <CardTitle className="text-lg font-medium mb-3 text-purple-700">
                得分要点提示
              </CardTitle>
              <div className="grid grid-cols-1 gap-2">
                {Array.isArray(analysisData.得分要点提示) ? (
                  analysisData.得分要点提示.map(
                    (item: string, index: number) => (
                      <div key={index} className="flex items-start">
                        <Badge className="bg-purple-100 text-purple-800 mr-2 mt-1">
                          要点{index + 1}
                        </Badge>
                        <p className="text-gray-700">{item}</p>
                      </div>
                    )
                  )
                ) : (
                  <pre className="text-sm">
                    {JSON.stringify(analysisData.得分要点提示, null, 2)}
                  </pre>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 处理其他可能的键 */}
        {Object.entries(analysisData)
          .filter(
            ([key]) =>
              ![
                "写作思路",
                "重点难点分析",
                "高分词汇和句式示例",
                "常见写作误区提醒",
                "得分要点提示",
              ].includes(key)
          )
          .map(([key, value]) => (
            <Card key={key} className="bg-gray-50 border-gray-200">
              <CardContent className="pt-6">
                <CardTitle className="text-lg font-medium mb-3 text-gray-700">
                  {key}
                </CardTitle>
                <pre className="text-sm">{JSON.stringify(value, null, 2)}</pre>
              </CardContent>
            </Card>
          ))}
      </div>
    );
  } catch (error) {
    // 如果无法解析为JSON，则使用普通文本显示
    return (
      <div className="prose prose-sm max-w-none">
        <pre className="text-sm whitespace-pre-wrap">{content}</pre>
      </div>
    );
  }
};
