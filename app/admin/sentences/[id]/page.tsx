"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import dynamic from "next/dynamic";

const ReactMarkdown = dynamic(() => import("react-markdown"), { ssr: false });

interface Sentence {
  id: string;
  year: string;
  type: string;
  sectionType: string;
  createdAt: string;
  originalContent: string;
  indexNum: number;
  paragraphNum: number;
  explain_md?: string;
}

export default function SentenceDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const [sentence, setSentence] = useState<Sentence | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // 获取句子详情
  useEffect(() => {
    const fetchSentence = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/sentences/${params.id}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error("句子不存在");
          }
          throw new Error("获取句子详情失败");
        }

        const data = await response.json();

        if (data.success) {
          setSentence(data.data);
        } else {
          throw new Error(data.error || "获取数据失败");
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "未知错误");
        console.error("获取句子详情错误:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchSentence();
  }, [params.id]);

  // 删除句子
  const deleteSentence = async () => {
    try {
      setLoading(true);

      const response = await fetch(`/api/sentences?id=${params.id}`, {
        method: "DELETE",
      });

      const data = await response.json();

      if (data.success) {
        // 跳转到句子列表页面
        router.push("/admin/sentences");
      } else {
        throw new Error(data.error || "删除失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "删除失败");
      console.error("删除句子错误:", err);
    } finally {
      setLoading(false);
      setShowDeleteConfirm(false);
    }
  };

  if (loading) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
          <p className="text-blue-700 font-medium">正在加载...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="bg-red-100 p-4 rounded-full">
            <svg
              className="h-12 w-12 text-red-500"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <p className="text-red-700 font-medium">{error}</p>
          <Link
            href="/admin/sentences"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            返回列表
          </Link>
        </div>
      </div>
    );
  }

  if (!sentence) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <p className="text-gray-500 font-medium">找不到句子</p>
          <Link
            href="/admin/sentences"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            返回列表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-8">
      {/* 顶部标题和操作栏 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
            句子详情
          </h1>
          <p className="text-gray-500 mt-2 text-lg">
            查看句子 #{sentence.paragraphNum || 1}-{sentence.indexNum}{" "}
            的详细分析
          </p>
        </div>
        <div className="flex space-x-4">
          <Link
            href="/admin/sentences"
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors"
          >
            返回列表
          </Link>
          <Link
            href={`/admin/sentences/${params.id}/edit`}
            className="px-4 py-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors"
          >
            编辑句子
          </Link>
          <button
            onClick={() => setShowDeleteConfirm(true)}
            className="px-4 py-2 bg-red-600 text-white rounded-xl hover:bg-red-700 transition-colors"
          >
            删除句子
          </button>
        </div>
      </div>

      {/* 基本信息 */}
      <div className="bg-white rounded-2xl shadow-lg p-6">
        <h2 className="text-xl font-bold text-gray-800 mb-6">基本信息</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
          <div>
            <p className="text-sm text-gray-500">年份</p>
            <p className="text-lg font-medium text-gray-800">
              {sentence.year}年
            </p>
          </div>

          <div>
            <p className="text-sm text-gray-500">类型</p>
            <p className="text-lg font-medium text-gray-800">{sentence.type}</p>
          </div>

          <div>
            <p className="text-sm text-gray-500">题型</p>
            <p className="text-lg font-medium text-gray-800">
              {sentence.sectionType === "use_of_english"
                ? "完形填空"
                : sentence.sectionType === "reading_comprehension"
                  ? "阅读理解"
                  : sentence.sectionType === "translation"
                    ? "翻译"
                    : sentence.sectionType === "writing"
                      ? "写作"
                      : sentence.sectionType}
            </p>
          </div>

          <div>
            <p className="text-sm text-gray-500">序号</p>
            <p className="text-lg font-medium text-gray-800">
              段落 {sentence.paragraphNum || 1} / 句子 {sentence.indexNum}
            </p>
          </div>
        </div>

        <div className="mt-6">
          <p className="text-sm text-gray-500">原文句子</p>
          <p className="text-lg text-gray-800 mt-2 bg-gray-50 p-4 rounded-lg">
            {sentence.originalContent}
          </p>
        </div>

        {/* Markdown解析内容 */}
        {sentence.explain_md && (
          <div className="mt-8">
            <p className="text-sm text-gray-500 mb-2">解析内容</p>
            <div className="prose prose-blue max-w-none bg-gray-50 p-4 rounded-lg">
              <ReactMarkdown>{sentence.explain_md}</ReactMarkdown>
            </div>
          </div>
        )}
      </div>

      {/* 删除确认对话框 */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full">
            <h3 className="text-xl font-bold text-gray-900 mb-4">确认删除</h3>
            <p className="text-gray-600 mb-6">
              你确定要删除这个句子吗？此操作不可撤销。
            </p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={deleteSentence}
                disabled={loading}
                className={`px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 ${
                  loading ? "opacity-70 cursor-not-allowed" : ""
                }`}
              >
                {loading ? "处理中..." : "确认删除"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
