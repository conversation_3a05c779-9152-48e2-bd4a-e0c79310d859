"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, ArrowLeft, Save, RefreshCw, Trash } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface Translation {
  id: number;
  content: string;
  reference_translation: string | null;
  translation_analysis: string | null;
  year: string;
  type: string;
  sectionType: string;
  title: string | null;
  createdAt: string;
  updatedAt: string;
}

const EditTranslationPage = ({ params }: { params: { id: string } }) => {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const [translation, setTranslation] = useState<Translation | null>(null);
  const [formData, setFormData] = useState({
    content: "",
    reference_translation: "",
    analysis: "",
  });

  // 加载翻译数据
  useEffect(() => {
    const fetchTranslation = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/papers/${params.id}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "加载翻译数据失败");
        }

        const data = await response.json();
        setTranslation(data.data);
        setFormData({
          content: data.data.content || "",
          reference_translation: data.data.reference_translation || "",
          analysis: data.data.translation_analysis || "",
        });
      } catch (err) {
        console.error("加载翻译数据失败:", err);
        setError(err instanceof Error ? err.message : "加载翻译数据失败");
      } finally {
        setLoading(false);
      }
    };

    fetchTranslation();
  }, [params.id]);

  // 处理表单输入变化
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // 保存翻译
  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccessMessage(null);

      const requestData = {
        content: formData.content,
        reference_translation: formData.reference_translation,
        translation_analysis: formData.analysis,
      };

      const response = await fetch(`/api/papers/${params.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "保存翻译失败");
      }

      const data = await response.json();
      setSuccessMessage("翻译保存成功");

      // 更新页面显示的翻译数据
      if (translation) {
        setTranslation({
          ...translation,
          content: formData.content,
          reference_translation: formData.reference_translation,
          translation_analysis: formData.analysis,
        });
      }
    } catch (err) {
      console.error("保存翻译失败:", err);
      setError(err instanceof Error ? err.message : "保存翻译失败");
    } finally {
      setSaving(false);

      // 3秒后清除成功消息
      if (successMessage) {
        const timer = setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
        return () => clearTimeout(timer);
      }
    }
  };

  // 生成AI翻译
  const handleGenerateAI = async () => {
    try {
      setLoading(true);
      setError(null);

      let success = false;
      let retryCount = 0;
      const maxRetries = 5;
      let data:
        | { data: { reference_translation?: string; analysis?: string } }
        | undefined;

      while (!success && retryCount < maxRetries) {
        try {
          const response = await fetch("/api/translate", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              type: "paragraph",
              content: formData.content,
              paperId: translation?.id,
            }),
          });

          if (response.status === 422) {
            retryCount++;
            console.log(`收到422错误，正在进行第${retryCount}次重试...`);
            // 添加短暂延迟避免立即重试
            await new Promise((resolve) => setTimeout(resolve, 1000));
            continue;
          }

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "生成AI翻译失败");
          }

          data = await response.json();
          success = true;
        } catch (err) {
          if (retryCount >= maxRetries - 1) {
            throw err;
          }
          retryCount++;
          console.log(`发生错误，正在进行第${retryCount}次重试...`, err);
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }

      if (!success) {
        throw new Error(`尝试${maxRetries}次后仍然失败，请稍后再试`);
      }

      // 确保data存在
      if (!data) {
        throw new Error("获取翻译数据失败");
      }

      // 更新表单数据
      setFormData((prev) => ({
        ...prev,
        reference_translation: data?.data?.reference_translation || "",
        analysis: data?.data?.analysis || "",
      }));

      setSuccessMessage("AI翻译生成成功");
    } catch (err) {
      console.error("生成AI翻译失败:", err);
      setError(err instanceof Error ? err.message : "生成AI翻译失败");
    } finally {
      setLoading(false);
    }
  };

  // 删除翻译
  const handleDelete = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/papers/${params.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "删除翻译失败");
      }

      router.push("/admin/translations");
    } catch (err) {
      console.error("删除翻译失败:", err);
      setError(err instanceof Error ? err.message : "删除翻译失败");
      setLoading(false);
    }
  };

  if (loading && !translation) {
    return (
      <div className="flex justify-center items-center h-[70vh]">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push("/admin/translations")}
            className="mr-2"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            返回
          </Button>
          <h1 className="text-3xl font-bold">编辑翻译</h1>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            disabled={loading || saving}
            onClick={handleGenerateAI}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            生成AI翻译
          </Button>
          <Button
            variant="default"
            disabled={loading || saving}
            onClick={handleSave}
          >
            {saving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                保存中...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                保存
              </>
            )}
          </Button>
          <AlertDialog
            open={deleteDialogOpen}
            onOpenChange={setDeleteDialogOpen}
          >
            <AlertDialogTrigger asChild>
              <Button variant="destructive">
                <Trash className="h-4 w-4 mr-2" />
                删除
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>确认删除</AlertDialogTitle>
                <AlertDialogDescription>
                  此操作不可撤销，确定要删除此翻译记录吗？
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>取消</AlertDialogCancel>
                <AlertDialogAction onClick={handleDelete}>
                  确认删除
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6">
          {successMessage}
        </div>
      )}

      {translation && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>试题信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <Label>试题ID</Label>
                  <Input value={translation.id} disabled />
                </div>
                <div>
                  <Label>年份</Label>
                  <Input value={translation.year} disabled />
                </div>
                <div>
                  <Label>类型</Label>
                  <Input value={translation.type} disabled />
                </div>
                <div>
                  <Label>题型</Label>
                  <Input value={translation.sectionType} disabled />
                </div>
                <div>
                  <Label>标题</Label>
                  <Input value={translation.title || "无标题"} disabled />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>英语段落翻译</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="content" className="text-base">
                    原文内容
                  </Label>
                  <Textarea
                    id="content"
                    name="content"
                    value={formData.content}
                    onChange={handleInputChange}
                    placeholder="输入原文内容..."
                    className="h-40 mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="reference_translation" className="text-base">
                    参考翻译
                  </Label>
                  <Textarea
                    id="reference_translation"
                    name="reference_translation"
                    value={formData.reference_translation}
                    onChange={handleInputChange}
                    placeholder="输入参考翻译..."
                    className="h-40 mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="analysis" className="text-base">
                    翻译分析
                  </Label>
                  <Textarea
                    id="analysis"
                    name="analysis"
                    value={formData.analysis}
                    onChange={handleInputChange}
                    placeholder="输入翻译分析..."
                    className="h-40 mt-1"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default EditTranslationPage;
