"use client";

import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/app/lib/utils";

const chatBubbleVariants = cva("flex items-start gap-2.5 p-2 text-sm", {
  variants: {
    variant: {
      sent: "justify-end",
      received: "justify-start",
      system: "justify-center",
    },
  },
  defaultVariants: {
    variant: "received",
  },
});

export interface ChatBubbleProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof chatBubbleVariants> {}

const ChatBubble = React.forwardRef<HTMLDivElement, ChatBubbleProps>(
  ({ className, variant, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(chatBubbleVariants({ variant, className }))}
      {...props}
    />
  )
);
ChatBubble.displayName = "ChatBubble";

const ChatBubbleAvatar = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    src?: string;
    fallback: string;
  }
>(({ className, src, fallback, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "flex h-8 w-8 shrink-0 select-none items-center justify-center rounded-full border bg-muted text-muted-foreground",
      className
    )}
    {...props}
  >
    {src ? (
      <img
        src={src}
        alt={fallback}
        className="h-full w-full rounded-full object-cover"
      />
    ) : (
      <span className="text-xs font-medium">{fallback}</span>
    )}
  </div>
));
ChatBubbleAvatar.displayName = "ChatBubbleAvatar";

const chatBubbleMessageVariants = cva(
  "flex max-w-[75%] flex-col gap-1 rounded-lg px-3 py-2 text-sm",
  {
    variants: {
      variant: {
        sent: "ml-auto rounded-br-none bg-primary text-primary-foreground",
        received: "rounded-bl-none bg-muted text-foreground",
        system:
          "bg-blue-100 dark:bg-blue-900/30 text-center max-w-md mx-auto text-xs",
      },
    },
    defaultVariants: {
      variant: "received",
    },
  }
);

export interface ChatBubbleMessageProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof chatBubbleMessageVariants> {
  isLoading?: boolean;
}

const ChatBubbleMessage = React.forwardRef<
  HTMLDivElement,
  ChatBubbleMessageProps
>(({ className, variant, isLoading, children, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(chatBubbleMessageVariants({ variant, className }))}
    {...props}
  >
    {isLoading ? (
      <div className="flex items-center gap-1.5">
        <span className="h-2 w-2 animate-bounce rounded-full bg-current"></span>
        <span
          className="h-2 w-2 animate-bounce rounded-full bg-current"
          style={{ animationDelay: "0.2s" }}
        ></span>
        <span
          className="h-2 w-2 animate-bounce rounded-full bg-current"
          style={{ animationDelay: "0.4s" }}
        ></span>
      </div>
    ) : (
      children
    )}
  </div>
));
ChatBubbleMessage.displayName = "ChatBubbleMessage";

export { ChatBubble, ChatBubbleAvatar, ChatBubbleMessage };
