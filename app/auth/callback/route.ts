import { createClient } from "@/utils/supabase/server";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  // The `/auth/callback` route is required for the server-side auth flow implemented
  // by the SSR package. It exchanges an auth code for the user's session.
  // https://supabase.com/docs/guides/auth/server-side/nextjs
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get("code");

  // 获取原始请求 Origin (可能是 localhost:8080)
  const requestOrigin = requestUrl.origin;

  // 使用环境变量中配置的网站 URL (应该是 https://mbdata.site)
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || requestOrigin;

  const redirectTo = requestUrl.searchParams.get("redirect_to")?.toString();

  console.log("Auth callback received with URL:", request.url);
  console.log("Request Origin:", requestOrigin);
  console.log("Site URL from env:", process.env.NEXT_PUBLIC_SITE_URL);
  console.log("Final Site URL:", siteUrl);
  console.log("Code present:", !!code);
  console.log("Redirect to:", redirectTo);

  if (code) {
    const supabase = await createClient();
    await supabase.auth.exchangeCodeForSession(code);
  }

  // 构建最终的重定向URL，使用环境变量中的网站 URL 而非请求的 origin
  const finalRedirectUrl = redirectTo
    ? `${siteUrl}${redirectTo}`
    : `${siteUrl}/`;

  console.log("最终重定向到:", finalRedirectUrl);

  // URL to redirect to after sign up process completes
  return NextResponse.redirect(finalRedirectUrl);
}
