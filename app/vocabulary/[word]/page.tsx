import { createClient } from "@/lib/supabase/server";
import { Vocabulary } from "@/types/vocabulary";
import Link from "next/link";
import { ChevronLeft, BookOpen, Lightbulb } from "lucide-react";
import { Markdown } from "@/app/components/markdown";

export async function generateMetadata({
  params,
}: {
  params: { word: string };
}) {
  const word = decodeURIComponent(params.word);

  return {
    title: `${word} - 考研英语词汇 | MBDATA`,
    description: `查看 ${word} 的详细解析、例句和记忆方法，提高考研英语词汇学习效率。`,
  };
}

async function getVocabularyWord(word: string): Promise<Vocabulary | null> {
  const supabase = createClient();

  try {
    // 确保word是正确解码的
    const decodedWord = decodeURIComponent(word);

    const { data, error } = await supabase
      .from("vocabulary")
      .select("*")
      .eq("word", decodedWord)
      .single();

    if (error) {
      console.error("Error fetching vocabulary word:", error);
      return null;
    }

    return data || null;
  } catch (e) {
    console.error("Exception in getVocabularyWord:", e);
    return null;
  }
}

export default async function WordPage({
  params,
}: {
  params: { word: string };
}) {
  // 确保URL中的单词是正确解码的
  const decodedWord = decodeURIComponent(params.word);
  const wordData = await getVocabularyWord(decodedWord);

  if (!wordData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Link
          href="/vocabulary"
          className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-6"
        >
          <ChevronLeft className="w-5 h-5 mr-1" />
          返回词汇表
        </Link>

        <div className="bg-white rounded-xl shadow-md p-8 border border-gray-100 text-center">
          <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-8 w-8 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-3">
            抱歉，未找到单词
          </h2>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            我们找不到您要查看的单词"{decodedWord}
            "，它可能不存在或尚未添加到词汇表中。
          </p>
          <Link
            href="/vocabulary"
            className="inline-flex items-center justify-center px-5 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            返回词汇表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Link
        href="/vocabulary"
        className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-6"
      >
        <ChevronLeft className="w-5 h-5 mr-1" />
        返回词汇表
      </Link>

      <div className="bg-white rounded-xl shadow-md p-6 border border-gray-100">
        <div className="border-b pb-4 mb-6">
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-800">
                {wordData.word}
              </h1>
              {wordData.pronunciation && (
                <p className="mt-1 text-lg text-gray-500">
                  /{wordData.pronunciation}/
                </p>
              )}
              <p className="mt-3 text-xl text-gray-700">
                {wordData.simple_translation}
              </p>
            </div>
          </div>
        </div>

        {wordData.analysis && (
          <div className="mb-8">
            <div className="flex items-center text-gray-700 mb-3">
              <BookOpen className="h-5 w-5 mr-2 text-blue-600" />
              <h2 className="text-xl font-semibold">单词分析</h2>
            </div>
            <div className="pl-2 border-l-4 border-blue-100 py-1">
              <div className="prose prose-lg max-w-none prose-headings:text-gray-800 prose-headings:font-semibold prose-p:text-gray-600 prose-hr:my-4 prose-hr:border-gray-200">
                <Markdown>{wordData.analysis}</Markdown>
              </div>
            </div>
          </div>
        )}
        {/* 
        {wordData.draw_explain && (
          <div className="mt-8 bg-amber-50 p-5 rounded-xl border border-amber-100">
            <div className="flex items-center mb-2">
              <Lightbulb className="h-5 w-5 text-amber-500 mr-2" />
              <h3 className="font-semibold text-lg text-amber-800">记忆技巧</h3>
            </div>
            <p className="text-amber-700 pl-7">{wordData.draw_explain}</p>
          </div>
        )} */}
      </div>
    </div>
  );
}
