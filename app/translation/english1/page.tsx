import { Metadata } from "next";
import Link from "next/link";
import { ArrowLeft, Languages } from "lucide-react";

export const metadata: Metadata = {
  title: "考研英语一 | 翻译真题列表",
  description: "考研英语一翻译真题列表，支持划线句子翻译和解析",
};

// 这个页面会显示英语一的所有翻译题
export default function EnglishOneTranslationList() {
  return (
    <div className="container mx-auto py-8 px-4 max-w-5xl">
      <div className="mb-8">
        <Link
          href="/exam-library"
          className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors mb-4"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          返回真题库
        </Link>
        <h1 className="text-2xl md:text-3xl font-bold text-gray-800">
          考研英语一翻译真题列表
        </h1>
        <div className="flex items-center mt-2 text-gray-500 text-sm">
          <Languages className="w-4 h-4 mr-1" />
          <span>划线句子翻译</span>
        </div>
      </div>

      <div className="bg-white rounded-xl shadow p-6">
        <p className="text-center text-gray-500 py-8">
          请从真题库中选择具体的英语一翻译真题进行练习
        </p>
        <div className="flex justify-center mt-4">
          <Link
            href="/exam-library?type=英语一"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            前往真题库
          </Link>
        </div>
      </div>
    </div>
  );
}
