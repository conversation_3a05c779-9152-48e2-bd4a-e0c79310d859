import { resetPasswordAction } from "@/app/actions";
import { FormMessage, Message } from "@/components/form-message";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { RiLockPasswordLine, RiShieldKeyholeLine } from "react-icons/ri";

export default async function ResetPassword(props: {
  searchParams: Promise<Message>;
}) {
  const searchParams = await props.searchParams;
  return (
    <div className="flex-1 w-full flex justify-center items-center px-4 py-8">
      <Card className="shadow-lg transition-all duration-300 hover:shadow-xl w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-2">
            <div className="rounded-full bg-purple-100 p-3 text-purple-600 dark:bg-purple-900 dark:text-purple-300">
              <RiShieldKeyholeLine size={24} />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-center">
            重置密码
          </CardTitle>
          <CardDescription className="text-center">
            请在下方输入您的新密码
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form className="flex flex-col gap-4">
            <div className="space-y-2">
              <Label htmlFor="password" className="text-sm font-medium">
                新密码
              </Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
                  <RiLockPasswordLine />
                </div>
                <Input
                  type="password"
                  name="password"
                  id="password"
                  placeholder="请输入新密码"
                  required
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword" className="text-sm font-medium">
                确认密码
              </Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
                  <RiLockPasswordLine />
                </div>
                <Input
                  type="password"
                  name="confirmPassword"
                  id="confirmPassword"
                  placeholder="请再次输入新密码"
                  required
                  className="pl-10"
                />
              </div>
            </div>

            <SubmitButton
              formAction={resetPasswordAction}
              pendingText="提交中..."
              className="mt-2 w-full bg-purple-600 hover:bg-purple-700 text-white"
            >
              重置密码
            </SubmitButton>
            <FormMessage message={searchParams} />
          </form>
        </CardContent>
        <CardFooter className="flex justify-center border-t pt-4">
          <p className="text-xs text-gray-500">
            请确保您的新密码足够安全且易于记忆
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}
