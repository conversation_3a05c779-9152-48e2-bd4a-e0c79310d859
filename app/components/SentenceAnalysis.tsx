"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import {
  ChevronDown,
  ChevronUp,
  BookOpen,
  Lightbulb,
  AlertCircle,
} from "lucide-react";

interface Translation {
  main?: string;
  alternatives?: string[];
  difficulties?: string[];
  tips?: string[];
}

interface GrammarAnalysis {
  structure?: string;
  keyPoints?: string[];
  specialCases?: string[];
  learningTips?: string[];
}

interface Word {
  word?: string;
  pos?: string;
  meaning?: string;
  collocations?: string[];
  synonyms?: string[];
  memoryTips?: string;
}

interface Phrase {
  phrase?: string;
  meaning?: string;
  usage?: string;
  examples?: string[];
}

interface Vocabulary {
  keyWords?: Word[];
  phrases?: Phrase[];
}

interface SentenceStructure {
  type?: string;
  components?: string[];
  clauses?: string[];
  specialPatterns?: string[];
  rewriteSuggestions?: string[];
}

interface KnowledgePoints {
  examPoints?: string[];
  relatedQuestions?: string[];
  commonMistakes?: string[];
  learningTips?: string[];
  reviewFocus?: string[];
}

interface AnalysisResult {
  translation?: Translation;
  grammarAnalysis?: GrammarAnalysis;
  vocabulary?: Vocabulary;
  sentenceStructure?: SentenceStructure;
  knowledgePoints?: KnowledgePoints;
}

interface SentenceAnalysisProps {
  result: AnalysisResult;
}

const SentenceAnalysis: React.FC<SentenceAnalysisProps> = ({ result }) => {
  const [expandedSections, setExpandedSections] = useState<
    Record<string, boolean>
  >({});

  const toggleSection = (section: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const renderTranslation = () => {
    const translation = result.translation || {};
    return (
      <div className="space-y-4">
        {translation.main && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold mb-2">主要翻译</h3>
            <p className="text-gray-700">{translation.main}</p>
          </div>
        )}

        {translation.alternatives && translation.alternatives.length > 0 && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold mb-2">备选翻译</h3>
            <ul className="list-disc pl-5 space-y-2">
              {translation.alternatives.map((alt, index) => (
                <li key={index} className="text-gray-700">
                  {alt}
                </li>
              ))}
            </ul>
          </div>
        )}

        {translation.difficulties && translation.difficulties.length > 0 && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold mb-2">翻译难点</h3>
            <ul className="list-disc pl-5 space-y-2">
              {translation.difficulties.map((diff, index) => (
                <li key={index} className="text-gray-700">
                  {diff}
                </li>
              ))}
            </ul>
          </div>
        )}

        {translation.tips && translation.tips.length > 0 && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold mb-2">翻译技巧</h3>
            <ul className="list-disc pl-5 space-y-2">
              {translation.tips.map((tip, index) => (
                <li key={index} className="text-gray-700">
                  {tip}
                </li>
              ))}
            </ul>
          </div>
        )}

        {!translation.main &&
          !translation.alternatives?.length &&
          !translation.difficulties?.length &&
          !translation.tips?.length && (
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <p className="text-gray-500 italic">暂无翻译数据</p>
            </div>
          )}
      </div>
    );
  };

  const renderGrammarAnalysis = () => {
    const grammar = result.grammarAnalysis || {};
    return (
      <div className="space-y-4">
        {grammar.structure && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold mb-2">句子结构</h3>
            <p className="text-gray-700">{grammar.structure}</p>
          </div>
        )}

        {grammar.keyPoints && grammar.keyPoints.length > 0 && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold mb-2">关键语法点</h3>
            <ul className="list-disc pl-5 space-y-2">
              {grammar.keyPoints.map((point, index) => (
                <li key={index} className="text-gray-700">
                  {point}
                </li>
              ))}
            </ul>
          </div>
        )}

        {grammar.specialCases && grammar.specialCases.length > 0 && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold mb-2">特殊语法现象</h3>
            <ul className="list-disc pl-5 space-y-2">
              {grammar.specialCases.map((special, index) => (
                <li key={index} className="text-gray-700">
                  {special}
                </li>
              ))}
            </ul>
          </div>
        )}

        {grammar.learningTips && grammar.learningTips.length > 0 && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold mb-2">学习建议</h3>
            <ul className="list-disc pl-5 space-y-2">
              {grammar.learningTips.map((tip, index) => (
                <li key={index} className="text-gray-700">
                  {tip}
                </li>
              ))}
            </ul>
          </div>
        )}

        {!grammar.structure &&
          !grammar.keyPoints?.length &&
          !grammar.specialCases?.length &&
          !grammar.learningTips?.length && (
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <p className="text-gray-500 italic">暂无语法分析数据</p>
            </div>
          )}
      </div>
    );
  };

  const renderVocabulary = () => {
    const vocabulary = result.vocabulary || {};
    const keyWords = vocabulary.keyWords || [];
    const phrases = vocabulary.phrases || [];

    return (
      <div className="space-y-4">
        {keyWords.length > 0 && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold mb-2">核心词汇</h3>
            <div className="space-y-4">
              {keyWords.map((word, index) => (
                <div key={index} className="border-b pb-4 last:border-b-0">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-xl font-bold">{word.word || ""}</span>
                    {word.pos && <Badge variant="secondary">{word.pos}</Badge>}
                  </div>
                  {word.meaning && (
                    <p className="text-gray-700 mb-2">{word.meaning}</p>
                  )}
                  <div className="space-y-2">
                    {word.collocations && word.collocations.length > 0 && (
                      <div>
                        <span className="font-medium">常见搭配：</span>
                        <div className="flex flex-wrap gap-2 mt-1">
                          {word.collocations.map((coll, i) => (
                            <Badge key={i} variant="outline">
                              {coll}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                    {word.synonyms && word.synonyms.length > 0 && (
                      <div>
                        <span className="font-medium">近义词：</span>
                        <div className="flex flex-wrap gap-2 mt-1">
                          {word.synonyms.map((syn, i) => (
                            <Badge key={i} variant="outline">
                              {syn}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                    {word.memoryTips && (
                      <div>
                        <span className="font-medium">记忆技巧：</span>
                        <p className="text-gray-700 mt-1">{word.memoryTips}</p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {phrases.length > 0 && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold mb-2">重要短语</h3>
            <div className="space-y-4">
              {phrases.map((phrase, index) => (
                <div key={index} className="border-b pb-4 last:border-b-0">
                  {phrase.phrase && (
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-lg font-semibold">
                        {phrase.phrase}
                      </span>
                    </div>
                  )}
                  {phrase.meaning && (
                    <p className="text-gray-700 mb-2">{phrase.meaning}</p>
                  )}
                  {phrase.usage && (
                    <p className="text-gray-700 mb-2">用法：{phrase.usage}</p>
                  )}
                  {phrase.examples && phrase.examples.length > 0 && (
                    <div>
                      <span className="font-medium">例句：</span>
                      <ul className="list-disc pl-5 space-y-1 mt-1">
                        {phrase.examples.map((example, i) => (
                          <li key={i} className="text-gray-700">
                            {example}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {keyWords.length === 0 && phrases.length === 0 && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <p className="text-gray-500 italic">暂无词汇和短语数据</p>
          </div>
        )}
      </div>
    );
  };

  const renderSentenceStructure = () => {
    const structure = result.sentenceStructure || {};
    return (
      <div className="space-y-4">
        {structure.type && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold mb-2">句子类型</h3>
            <p className="text-gray-700">{structure.type}</p>
          </div>
        )}

        {structure.components && structure.components.length > 0 && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold mb-2">句子成分</h3>
            <ul className="list-disc pl-5 space-y-2">
              {structure.components.map((component, index) => (
                <li key={index} className="text-gray-700">
                  {component}
                </li>
              ))}
            </ul>
          </div>
        )}

        {structure.clauses && structure.clauses.length > 0 && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold mb-2">从句分析</h3>
            <ul className="list-disc pl-5 space-y-2">
              {structure.clauses.map((clause, index) => (
                <li key={index} className="text-gray-700">
                  {clause}
                </li>
              ))}
            </ul>
          </div>
        )}

        {structure.specialPatterns && structure.specialPatterns.length > 0 && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold mb-2">特殊句式</h3>
            <ul className="list-disc pl-5 space-y-2">
              {structure.specialPatterns.map((pattern, index) => (
                <li key={index} className="text-gray-700">
                  {pattern}
                </li>
              ))}
            </ul>
          </div>
        )}

        {structure.rewriteSuggestions &&
          structure.rewriteSuggestions.length > 0 && (
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <h3 className="text-lg font-semibold mb-2">改写建议</h3>
              <ul className="list-disc pl-5 space-y-2">
                {structure.rewriteSuggestions.map((suggestion, index) => (
                  <li key={index} className="text-gray-700">
                    {suggestion}
                  </li>
                ))}
              </ul>
            </div>
          )}

        {!structure.type &&
          !structure.components?.length &&
          !structure.clauses?.length &&
          !structure.specialPatterns?.length &&
          !structure.rewriteSuggestions?.length && (
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <p className="text-gray-500 italic">暂无句子结构数据</p>
            </div>
          )}
      </div>
    );
  };

  const renderKnowledgePoints = () => {
    const knowledge = result.knowledgePoints || {};
    return (
      <div className="space-y-4">
        {knowledge.examPoints && knowledge.examPoints.length > 0 && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold mb-2">考点分析</h3>
            <div className="space-y-2">
              {knowledge.examPoints.map((point, index) => (
                <div key={index} className="flex items-start gap-2">
                  <BookOpen className="w-5 h-5 text-blue-500 mt-1" />
                  <p className="text-gray-700">{point}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {knowledge.relatedQuestions &&
          knowledge.relatedQuestions.length > 0 && (
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <h3 className="text-lg font-semibold mb-2">相关真题</h3>
              <ul className="list-disc pl-5 space-y-2">
                {knowledge.relatedQuestions.map((question, index) => (
                  <li key={index} className="text-gray-700">
                    {question}
                  </li>
                ))}
              </ul>
            </div>
          )}

        {knowledge.commonMistakes && knowledge.commonMistakes.length > 0 && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold mb-2">常见错误</h3>
            <div className="space-y-2">
              {knowledge.commonMistakes.map((mistake, index) => (
                <div key={index} className="flex items-start gap-2">
                  <AlertCircle className="w-5 h-5 text-red-500 mt-1" />
                  <p className="text-gray-700">{mistake}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {knowledge.learningTips && knowledge.learningTips.length > 0 && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold mb-2">学习建议</h3>
            <div className="space-y-2">
              {knowledge.learningTips.map((tip, index) => (
                <div key={index} className="flex items-start gap-2">
                  <Lightbulb className="w-5 h-5 text-yellow-500 mt-1" />
                  <p className="text-gray-700">{tip}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {knowledge.reviewFocus && knowledge.reviewFocus.length > 0 && (
          <div className="bg-white rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold mb-2">复习重点</h3>
            <ul className="list-disc pl-5 space-y-2">
              {knowledge.reviewFocus.map((focus, index) => (
                <li key={index} className="text-gray-700">
                  {focus}
                </li>
              ))}
            </ul>
          </div>
        )}

        {!knowledge.examPoints?.length &&
          !knowledge.relatedQuestions?.length &&
          !knowledge.commonMistakes?.length &&
          !knowledge.learningTips?.length &&
          !knowledge.reviewFocus?.length && (
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <p className="text-gray-500 italic">暂无知识点数据</p>
            </div>
          )}
      </div>
    );
  };

  // 检查是否有任何数据
  const hasAnyData = !!(
    result.translation ||
    result.grammarAnalysis ||
    result.vocabulary ||
    result.sentenceStructure ||
    result.knowledgePoints
  );

  if (!hasAnyData) {
    return (
      <div className="w-full max-w-4xl mx-auto p-4">
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-gray-500">暂无分析数据</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      <Tabs defaultValue="translation" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="translation">翻译</TabsTrigger>
          <TabsTrigger value="grammar">语法分析</TabsTrigger>
          <TabsTrigger value="vocabulary">词汇短语</TabsTrigger>
          <TabsTrigger value="structure">句子结构</TabsTrigger>
          <TabsTrigger value="knowledge">知识点</TabsTrigger>
        </TabsList>
        <TabsContent value="translation" className="mt-4">
          {renderTranslation()}
        </TabsContent>
        <TabsContent value="grammar" className="mt-4">
          {renderGrammarAnalysis()}
        </TabsContent>
        <TabsContent value="vocabulary" className="mt-4">
          {renderVocabulary()}
        </TabsContent>
        <TabsContent value="structure" className="mt-4">
          {renderSentenceStructure()}
        </TabsContent>
        <TabsContent value="knowledge" className="mt-4">
          {renderKnowledgePoints()}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SentenceAnalysis;
