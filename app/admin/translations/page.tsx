"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../components/ui/table";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { PlusCircle, Search, RefreshCw } from "lucide-react";

interface Translation {
  id: number;
  year: string;
  type: string;
  section_type: string;
  content: string;
  reference_translation: string | null;
  translation_analysis: string | null;
  created_at: string;
}

interface TranslationLine {
  id: number;
  sentence_id: number;
  original_content: string;
  reference_translation: string | null;
  difficulty_analysis: string | null;
  translation_skills: string | null;
  created_at: string;
}

const truncateText = (text: string | null, maxLength: number = 100) => {
  if (!text) return "无";
  return text.length > maxLength ? text.substring(0, maxLength) + "..." : text;
};

const TranslationsPage = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("english2");
  const [isLoading, setIsLoading] = useState(false);
  const [translations, setTranslations] = useState<Translation[]>([]);
  const [translationLines, setTranslationLines] = useState<TranslationLine[]>(
    []
  );
  const [searchQuery, setSearchQuery] = useState("");

  // 加载翻译数据
  const loadTranslations = async () => {
    setIsLoading(true);
    try {
      // 直接从papers表中获取翻译数据，使用更大的pageSize确保获取所有数据
      const res = await fetch(
        "/api/papers?sectionType=translation&pageSize=1000"
      );
      const data = await res.json();
      if (data.success) {
        // 转换数据格式以匹配现有界面
        const formattedData = data.data.map((item: any) => ({
          id: item.id,
          year: item.year,
          type: item.type,
          section_type: item.sectionType,
          content: item.content || "",
          reference_translation: item.reference_translation || null,
          translation_analysis: item.translation_analysis || null,
          created_at: item.createdAt,
        }));
        setTranslations(formattedData);
      }

      // 加载英语一划线句子翻译
      const lineRes = await fetch("/api/translation-lines");
      const lineData = await lineRes.json();
      if (lineData.success) {
        setTranslationLines(lineData.data);
      }
    } catch (error) {
      console.error("加载翻译数据失败:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // 初始加载数据
  useEffect(() => {
    loadTranslations();
  }, []);

  // 过滤数据
  const filteredTranslations = translations.filter(
    (item) =>
      item.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.year.includes(searchQuery) ||
      item.type.includes(searchQuery)
  );

  const filteredTranslationLines = translationLines.filter((item) =>
    item.original_content.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // 添加新翻译
  const handleAddTranslation = () => {
    router.push("/admin/translations/new");
  };

  // 添加新划线句子翻译
  const handleAddTranslationLine = () => {
    router.push("/admin/translation-lines/new");
  };

  // 刷新数据
  const handleRefresh = () => {
    loadTranslations();
  };

  return (
    <div className="container mx-auto ">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">翻译管理</h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            {isLoading ? "加载中..." : "刷新"}
          </Button>
        </div>
      </div>

      <div className="mb-6">
        <div className="flex gap-2 max-w-md">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="搜索..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="english2">英语二段落翻译</TabsTrigger>
          <TabsTrigger value="english1">
            英语一划线句子翻译 （废弃）
          </TabsTrigger>
        </TabsList>

        <TabsContent value="english2">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>英语二段落翻译</CardTitle>
              <Button onClick={handleAddTranslation}>
                <PlusCircle className="mr-2 h-4 w-4" />
                添加翻译
              </Button>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>年份</TableHead>
                    <TableHead className="w-32">类型</TableHead>
                    <TableHead>原文内容</TableHead>
                    <TableHead>参考翻译</TableHead>
                    <TableHead>翻译分析</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTranslations.length > 0 ? (
                    filteredTranslations.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>{item.id}</TableCell>
                        <TableCell>{item.year}</TableCell>
                        <TableCell className="w-32">{item.type}</TableCell>
                        <TableCell>{truncateText(item.content)}</TableCell>
                        <TableCell>
                          {truncateText(item.reference_translation)}
                        </TableCell>
                        <TableCell>
                          {truncateText(item.translation_analysis)}
                        </TableCell>
                        <TableCell>
                          {new Date(item.created_at).toLocaleString()}
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              router.push(`/admin/translations/${item.id}/edit`)
                            }
                          >
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-4">
                        {isLoading
                          ? "加载中..."
                          : searchQuery
                            ? "没有找到匹配的记录"
                            : "暂无数据"}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="english1">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>英语一划线句子翻译</CardTitle>
              <Button onClick={handleAddTranslationLine}>
                <PlusCircle className="mr-2 h-4 w-4" />
                添加划线句子
              </Button>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>句子ID</TableHead>
                    <TableHead>原句内容</TableHead>
                    <TableHead>参考翻译</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTranslationLines.length > 0 ? (
                    filteredTranslationLines.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>{item.id}</TableCell>
                        <TableCell>{item.sentence_id}</TableCell>
                        <TableCell>
                          {truncateText(item.original_content)}
                        </TableCell>
                        <TableCell>
                          {truncateText(item.reference_translation)}
                        </TableCell>
                        <TableCell>
                          {new Date(item.created_at).toLocaleString()}
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              router.push(
                                `/admin/translation-lines/${item.id}/edit`
                              )
                            }
                          >
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4">
                        {isLoading
                          ? "加载中..."
                          : searchQuery
                            ? "没有找到匹配的记录"
                            : "暂无数据"}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TranslationsPage;
