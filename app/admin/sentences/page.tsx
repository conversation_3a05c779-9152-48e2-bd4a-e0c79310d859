"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";

interface Sentence {
  id: string;
  title: string;
  year: string;
  type: string;
  sectionType: string;
  originalContent: string;
  createdAt: string;
  indexNum: number;
  paragraphNum: number;
}

interface Pagination {
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

const SentencesPage = () => {
  const router = useRouter();
  const [sentences, setSentences] = useState<Sentence[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 分页状态
  const [pagination, setPagination] = useState<Pagination>({
    total: 0,
    page: 1,
    pageSize: 10,
    totalPages: 0,
  });

  // 筛选状态
  const [filters, setFilters] = useState({
    year: "",
    type: "",
    sectionType: "",
    search: "",
  });

  // 临时搜索输入
  const [searchInput, setSearchInput] = useState("");

  // 确认对话框状态
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [sentenceToDelete, setSentenceToDelete] = useState<string | null>(null);

  // 加载句子数据
  const fetchSentences = async () => {
    try {
      setLoading(true);
      setError(null);

      // 构建查询参数
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        pageSize: pagination.pageSize.toString(),
      });

      if (filters.year) params.append("year", filters.year);
      if (filters.type) params.append("type", filters.type);
      if (filters.sectionType)
        params.append("sectionType", filters.sectionType);
      if (filters.search) params.append("search", filters.search);

      const response = await fetch(`/api/sentences?${params.toString()}`);

      if (!response.ok) {
        throw new Error("获取句子列表失败");
      }

      const data = await response.json();

      if (data.success) {
        setSentences(data.data);
        setPagination(data.pagination);
      } else {
        throw new Error(data.error || "获取数据失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "未知错误");
      console.error("获取句子列表错误:", err);
    } finally {
      setLoading(false);
    }
  };

  // 首次加载和筛选/分页变化时获取数据
  useEffect(() => {
    fetchSentences();
  }, [pagination.page, pagination.pageSize, filters]);

  // 处理页码变化
  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({
      ...prev,
      page: newPage,
    }));
  };

  // 处理搜索
  const handleSearch = () => {
    setFilters((prev) => ({
      ...prev,
      search: searchInput,
    }));
    // 重置到第一页
    setPagination((prev) => ({
      ...prev,
      page: 1,
    }));
  };

  // 处理筛选变化
  const handleFilterChange = (name: string, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [name]: value,
    }));
    // 重置到第一页
    setPagination((prev) => ({
      ...prev,
      page: 1,
    }));
  };

  // 清除筛选
  const clearFilters = () => {
    setFilters({
      year: "",
      type: "",
      sectionType: "",
      search: "",
    });
    setSearchInput("");
    setPagination((prev) => ({
      ...prev,
      page: 1,
    }));
  };

  // 删除句子
  const deleteSentence = async (id: string) => {
    try {
      const response = await fetch(`/api/sentences?id=${id}`, {
        method: "DELETE",
      });

      const data = await response.json();

      if (data.success) {
        // 更新句子列表
        fetchSentences();
      } else {
        throw new Error(data.error || "删除失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "删除失败");
      console.error("删除句子错误:", err);
    } finally {
      // 关闭确认对话框
      setShowDeleteConfirm(false);
      setSentenceToDelete(null);
    }
  };

  // 打开删除确认对话框
  const confirmDelete = (id: string) => {
    setSentenceToDelete(id);
    setShowDeleteConfirm(true);
  };

  return (
    <div className="w-full space-y-8">
      {/* 顶部标题和操作栏 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
            句子管理
          </h1>
          <p className="text-gray-500 mt-2 text-lg">管理和编辑分析的句子</p>
        </div>
        <div className="flex gap-4">
          <Link
            href="/admin/papers"
            className="px-6 py-3 bg-gray-100 text-gray-700 text-lg rounded-xl hover:bg-gray-200 transition-all duration-200 font-medium"
          >
            文章管理
          </Link>
          <Link
            href="/admin/sentences/new"
            className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white text-lg rounded-xl hover:shadow-lg transform hover:scale-105 transition-all duration-200 font-medium"
          >
            添加新句子
          </Link>
        </div>
      </div>

      {/* 筛选器 */}
      <div className="bg-white p-6 rounded-2xl shadow-lg">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="space-y-2">
            <label className="text-gray-700 text-lg font-medium">年份</label>
            <select
              value={filters.year}
              onChange={(e) => handleFilterChange("year", e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
            >
              <option value="">所有年份</option>
              {Array.from({ length: 16 }, (_, i) => 2010 + i).map((year) => (
                <option key={year} value={year.toString()}>
                  {year}年
                </option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="text-gray-700 text-lg font-medium">类型</label>
            <select
              value={filters.type}
              onChange={(e) => handleFilterChange("type", e.target.value)}
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
            >
              <option value="">所有类型</option>
              <option value="英语一">英语一</option>
              <option value="英语二">英语二</option>
            </select>
          </div>

          <div className="space-y-2">
            <label className="text-gray-700 text-lg font-medium">题型</label>
            <select
              value={filters.sectionType}
              onChange={(e) =>
                handleFilterChange("sectionType", e.target.value)
              }
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
            >
              <option value="">所有题型</option>
              <option value="use_of_english">完形填空</option>
              <option value="reading_comprehension">阅读理解</option>
              <option value="translation">翻译</option>
              <option value="writing">写作</option>
            </select>
          </div>

          <div className="space-y-2">
            <label className="text-gray-700 text-lg font-medium">搜索</label>
            <div className="flex space-x-2">
              <input
                type="text"
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                placeholder="搜索句子内容或标题..."
                className="flex-1 px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                onKeyDown={(e) => {
                  if (e.key === "Enter") handleSearch();
                }}
              />
              <button
                onClick={handleSearch}
                className="px-4 py-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors"
              >
                搜索
              </button>
            </div>
          </div>
        </div>

        <div className="flex justify-end mt-4">
          <button
            onClick={clearFilters}
            className="px-4 py-2 text-blue-600 hover:text-blue-800 font-medium"
          >
            清除筛选
          </button>
        </div>
      </div>

      {/* 句子列表 */}
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
        <div className="p-6 border-b border-gray-100">
          <h2 className="text-2xl font-bold text-gray-800">句子列表</h2>
          <p className="text-sm text-gray-500 mt-1">
            共 {pagination.total} 条记录
          </p>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
          </div>
        ) : error ? (
          <div className="py-10 text-center">
            <p className="text-red-500">{error}</p>
            <button
              onClick={fetchSentences}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              重试
            </button>
          </div>
        ) : sentences.length === 0 ? (
          <div className="py-16 text-center">
            <p className="text-gray-500 text-lg">暂无数据</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    段落/序号
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    年份/类型
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    题型
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    原文句子
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sentences.map((sentence) => (
                  <tr key={sentence.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {sentence.paragraphNum || 1}/{sentence.indexNum || "N/A"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {sentence.year}年
                      </div>
                      <div className="text-sm text-gray-500">
                        {sentence.type}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {sentence.sectionType === "use_of_english"
                        ? "完形填空"
                        : sentence.sectionType === "reading_comprehension"
                          ? "阅读理解"
                          : sentence.sectionType === "translation"
                            ? "翻译"
                            : sentence.sectionType === "writing"
                              ? "写作"
                              : sentence.sectionType}
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 line-clamp-2">
                        {sentence.originalContent}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(sentence.createdAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-4">
                        <Link
                          href={`/admin/sentences/${sentence.id}`}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          查看
                        </Link>
                        <Link
                          href={`/admin/sentences/${sentence.id}/edit`}
                          className="text-indigo-600 hover:text-indigo-900"
                        >
                          编辑
                        </Link>
                        <button
                          onClick={() => confirmDelete(sentence.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          删除
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* 分页 */}
        {pagination.totalPages > 1 && (
          <div className="px-6 py-4 flex justify-between items-center border-t border-gray-200">
            <div className="text-sm text-gray-500">
              显示 {(pagination.page - 1) * pagination.pageSize + 1} -{" "}
              {Math.min(
                pagination.page * pagination.pageSize,
                pagination.total
              )}{" "}
              条，共 {pagination.total} 条
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page === 1}
                className={`px-3 py-1 rounded ${
                  pagination.page === 1
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
              >
                上一页
              </button>
              {Array.from(
                { length: Math.min(5, pagination.totalPages) },
                (_, i) => {
                  // 显示当前页附近的页码
                  let pageNum: number;
                  if (pagination.totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (pagination.page <= 3) {
                    pageNum = i + 1;
                  } else if (pagination.page >= pagination.totalPages - 2) {
                    pageNum = pagination.totalPages - 4 + i;
                  } else {
                    pageNum = pagination.page - 2 + i;
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`px-3 py-1 rounded ${
                        pagination.page === pageNum
                          ? "bg-blue-500 text-white"
                          : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                }
              )}
              <button
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page === pagination.totalPages}
                className={`px-3 py-1 rounded ${
                  pagination.page === pagination.totalPages
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
              >
                下一页
              </button>
            </div>
          </div>
        )}
      </div>

      {/* 删除确认对话框 */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full">
            <h3 className="text-xl font-bold text-gray-900 mb-4">确认删除</h3>
            <p className="text-gray-600 mb-6">
              你确定要删除这个句子吗？此操作不可撤销。
            </p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => {
                  setShowDeleteConfirm(false);
                  setSentenceToDelete(null);
                }}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={() =>
                  sentenceToDelete && deleteSentence(sentenceToDelete)
                }
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                确认删除
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SentencesPage;
