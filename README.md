# 考研英语真题分析系统

这是一个使用Next.js开发的考研英语真题分析系统，支持对考研英语真题进行智能分析，提供句子翻译、语法分析、重点词汇和考点提示等功能。

## 技术栈

- **前端框架**: Next.js 14 + TypeScript + TailwindCSS
- **后端服务**: Next.js API Routes
- **数据库**: MySQL
- **部署**: Docker + Docker Compose

## 功能特点

- 真题管理：按年份、类型、题型分类管理考研英语真题
- 智能分析：使用AI技术分析句子结构、语法特点和重点词汇
- 考点提示：突出显示历年高频考点，提供学习建议
- 数据存储：将分析结果保存到数据库，支持查询和复习

## 快速开始

### 使用Docker Compose（推荐）

1. 克隆项目

```bash
git clone https://github.com/yourusername/mbdata.git
cd mbdata
```

2. 创建环境变量文件

```bash
cp .env.example .env
```

编辑`.env`文件，设置数据库密码和DeepSeek API密钥。

3. 使用Docker Compose启动项目

```bash
docker-compose up -d
```

4. 访问应用
   打开浏览器访问 [http://localhost:3000](http://localhost:3000)

### 本地开发

1. 克隆项目

```bash
git clone https://github.com/yourusername/mbdata.git
cd mbdata
```

2. 安装依赖

```bash
npm install
```

3. 创建环境变量文件

```bash
cp .env.example .env.local
```

编辑`.env.local`文件，设置数据库连接和DeepSeek API密钥。

4. 启动MySQL数据库（可使用Docker）

```bash
docker run --name mbdata-mysql -e MYSQL_ROOT_PASSWORD=rootpassword -e MYSQL_DATABASE=mbdata -p 3306:3306 -d mysql:8.0
```

5. 初始化数据库

```bash
mysql -u root -p < db/init.sql
```

6. 启动开发服务器

```bash
npm run dev
```

7. 访问应用
   打开浏览器访问 [http://localhost:3000](http://localhost:3000)

## 数据库结构

项目使用MySQL数据库存储数据，主要包含以下表：

- `sentence_analysis`: 存储句子分析结果
- `papers`: 存储试题基础信息
- `users`: 存储用户信息（如需用户系统）

详细的数据库结构可以查看`db/init.sql`文件。

## 项目配置

### 环境变量

- `MYSQL_HOST`: MySQL数据库主机地址
- `MYSQL_PORT`: MySQL数据库端口
- `MYSQL_USER`: MySQL数据库用户名
- `MYSQL_PASSWORD`: MySQL数据库密码
- `MYSQL_DATABASE`: MySQL数据库名称
- `DEEPSEEK_API_KEY`: DeepSeek API密钥

## 部署说明

项目已配置Docker和Docker Compose，可轻松部署到支持Docker的环境中。

## 问题反馈

如有问题或建议，请提交Issue或联系开发者。

## 许可证

[MIT](./LICENSE)

## 替换公众号二维码

要替换公众号二维码图片，请按照以下步骤操作：

1. 将您的公众号二维码图片命名为 `wechat-qrcode.jpg`
2. 将图片放入 `public/images/` 目录下，替换现有的占位图片
3. 重新构建或重启应用以应用更改

图片建议尺寸为 300x300 像素，格式为JPG或PNG，确保二维码清晰可见。
