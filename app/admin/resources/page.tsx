"use client";

import { useState, useEffect, useRef } from "react";
import {
  Upload,
  Trash2,
  FileAudio,
  File,
  FolderOpen,
  Plus,
  Download,
  Eye,
  X,
} from "lucide-react";

interface StorageFile {
  id: string;
  name: string;
  size: number;
  type: string;
  created_at: string;
  updated_at: string;
}

interface UploadProgress {
  fileName: string;
  progress: number;
  status: "uploading" | "completed" | "error";
  error?: string;
}

export default function ResourcesManagementPage() {
  const [currentPath, setCurrentPath] = useState(""); // 当前文件夹路径
  const [files, setFiles] = useState<StorageFile[]>([]);
  const [folders, setFolders] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null);
  const [newFolderName, setNewFolderName] = useState("");
  const [showNewFolderInput, setShowNewFolderInput] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  // 加载文件列表
  const loadFiles = async (path: string = "") => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(
        `/api/admin/resources?path=${encodeURIComponent(path)}`
      );

      if (!response.ok) {
        throw new Error(`加载失败: ${response.statusText}`);
      }

      const data = await response.json();
      setFiles(data.files || []);
      setFolders(data.folders || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : "加载文件失败");
    } finally {
      setLoading(false);
    }
  };

  // 组件加载时获取文件列表
  useEffect(() => {
    loadFiles(currentPath);
  }, [currentPath]);

  // 处理文件上传
  const handleFileUpload = async () => {
    if (!selectedFiles || selectedFiles.length === 0) return;

    const newProgress: UploadProgress[] = Array.from(selectedFiles).map(
      (file) => ({
        fileName: file.name,
        progress: 0,
        status: "uploading",
      })
    );

    setUploadProgress(newProgress);

    for (let i = 0; i < selectedFiles.length; i++) {
      const file = selectedFiles[i];

      try {
        // 使用普通上传接口
        await uploadSmallFile(file, i);
      } catch (err) {
        setUploadProgress((prev) =>
          prev.map((item, index) =>
            index === i
              ? {
                  ...item,
                  status: "error",
                  error: err instanceof Error ? err.message : "上传失败",
                }
              : item
          )
        );
      }
    }

    // 上传完成后刷新文件列表
    setTimeout(() => {
      loadFiles(currentPath);
      setShowUploadModal(false);
      setSelectedFiles(null);
      setUploadProgress([]);
    }, 1000);
  };

  // 小文件上传
  const uploadSmallFile = async (file: File, index: number) => {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("path", currentPath);

    const response = await fetch("/api/admin/resources/upload", {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`上传失败: ${response.statusText}`);
    }

    // 更新进度
    setUploadProgress((prev) =>
      prev.map((item, idx) =>
        idx === index ? { ...item, progress: 100, status: "completed" } : item
      )
    );
  };

  // 删除文件
  const handleDeleteFile = async (fileName: string) => {
    if (!confirm(`确定要删除文件 "${fileName}" 吗？`)) return;

    try {
      const response = await fetch("/api/admin/resources/delete", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          fileName,
          path: currentPath,
        }),
      });

      if (!response.ok) {
        throw new Error(`删除失败: ${response.statusText}`);
      }

      // 刷新文件列表
      loadFiles(currentPath);
    } catch (err) {
      setError(err instanceof Error ? err.message : "删除文件失败");
    }
  };

  // 创建新文件夹
  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) return;

    try {
      const response = await fetch("/api/admin/resources/folder", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          folderName: newFolderName,
          path: currentPath,
        }),
      });

      if (!response.ok) {
        throw new Error(`创建文件夹失败: ${response.statusText}`);
      }

      setNewFolderName("");
      setShowNewFolderInput(false);
      loadFiles(currentPath);
    } catch (err) {
      setError(err instanceof Error ? err.message : "创建文件夹失败");
    }
  };

  // 导航到文件夹
  const navigateToFolder = (folderName: string) => {
    const newPath = currentPath ? `${currentPath}/${folderName}` : folderName;
    setCurrentPath(newPath);
  };

  // 返回上级目录
  const navigateUp = () => {
    const parts = currentPath.split("/");
    parts.pop();
    setCurrentPath(parts.join("/"));
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // 获取文件图标
  const getFileIcon = (fileName: string, type: string) => {
    if (
      type.includes("audio") ||
      fileName.toLowerCase().includes(".mp3") ||
      fileName.toLowerCase().includes(".wav")
    ) {
      return <FileAudio className="w-6 h-6 text-green-600" />;
    }
    return <File className="w-6 h-6 text-gray-500" />;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="bg-white rounded-lg shadow-lg">
        {/* 头部 */}
        <div className="border-b border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">资源管理</h1>
              <p className="text-gray-600 mt-1">
                管理学习资源文件，支持音频文件上传
              </p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => setShowNewFolderInput(true)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <Plus className="w-4 h-4 mr-2" />
                新建文件夹
              </button>
              <button
                onClick={() => setShowUploadModal(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
              >
                <Upload className="w-4 h-4 mr-2" />
                上传文件
              </button>
            </div>
          </div>

          {/* 面包屑导航 */}
          <div className="mt-4 flex items-center text-sm text-gray-600">
            <button
              onClick={() => setCurrentPath("")}
              className="hover:text-blue-600 transition-colors"
            >
              根目录
            </button>
            {currentPath
              .split("/")
              .filter(Boolean)
              .map((part, index, array) => (
                <div key={index} className="flex items-center">
                  <span className="mx-2">/</span>
                  <button
                    onClick={() => {
                      const newPath = array.slice(0, index + 1).join("/");
                      setCurrentPath(newPath);
                    }}
                    className="hover:text-blue-600 transition-colors"
                  >
                    {part}
                  </button>
                </div>
              ))}
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="mx-6 mt-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {/* 新建文件夹输入 */}
        {showNewFolderInput && (
          <div className="mx-6 mt-4 p-4 bg-gray-50 rounded-md">
            <div className="flex items-center gap-3">
              <input
                type="text"
                value={newFolderName}
                onChange={(e) => setNewFolderName(e.target.value)}
                placeholder="输入文件夹名称"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                onKeyPress={(e) => e.key === "Enter" && handleCreateFolder()}
              />
              <button
                onClick={handleCreateFolder}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                创建
              </button>
              <button
                onClick={() => {
                  setShowNewFolderInput(false);
                  setNewFolderName("");
                }}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
              >
                取消
              </button>
            </div>
          </div>
        )}

        {/* 文件列表 */}
        <div className="p-6">
          {loading ? (
            <div className="text-center py-12">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <p className="mt-2 text-gray-600">加载中...</p>
            </div>
          ) : (
            <div className="space-y-2">
              {/* 返回上级按钮 */}
              {currentPath && (
                <div
                  onClick={navigateUp}
                  className="flex items-center p-3 hover:bg-gray-50 rounded-md cursor-pointer border-b border-gray-100"
                >
                  <FolderOpen className="w-6 h-6 text-blue-600 mr-3" />
                  <span className="text-blue-600 font-medium">
                    .. 返回上级目录
                  </span>
                </div>
              )}

              {/* 文件夹列表 */}
              {folders.map((folder) => (
                <div
                  key={folder}
                  onClick={() => navigateToFolder(folder)}
                  className="flex items-center p-3 hover:bg-gray-50 rounded-md cursor-pointer border-b border-gray-100"
                >
                  <FolderOpen className="w-6 h-6 text-blue-600 mr-3" />
                  <span className="font-medium">{folder}</span>
                </div>
              ))}

              {/* 文件列表 */}
              {files.map((file) => (
                <div
                  key={file.id}
                  className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-md border-b border-gray-100"
                >
                  <div className="flex items-center min-w-0 flex-1">
                    {getFileIcon(file.name, file.type)}
                    <div className="ml-3 min-w-0 flex-1">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {file.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatFileSize(file.size)} •{" "}
                        {new Date(file.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  {/* <div className="flex items-center gap-2">
                    <button
                      onClick={() => handleDeleteFile(file.name)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
                      title="删除文件"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div> */}
                </div>
              ))}

              {folders.length === 0 && files.length === 0 && (
                <div className="text-center py-12 text-gray-500">
                  <FolderOpen className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>此文件夹为空</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* 上传模态框 */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">上传文件</h3>
              <button
                onClick={() => {
                  setShowUploadModal(false);
                  setSelectedFiles(null);
                  setUploadProgress([]);
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="p-6">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  选择文件
                </label>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept="audio/*,.pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx"
                  onChange={(e) => setSelectedFiles(e.target.files)}
                  className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                />
                <p className="mt-1 text-xs text-gray-500">
                  支持音频文件、PDF、Word、Excel、PowerPoint等格式
                </p>
              </div>

              {selectedFiles && selectedFiles.length > 0 && (
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-700 mb-2">
                    已选择 {selectedFiles.length} 个文件:
                  </p>
                  <div className="max-h-32 overflow-y-auto">
                    {Array.from(selectedFiles).map((file, index) => (
                      <div key={index} className="text-xs text-gray-600 py-1">
                        {file.name} ({formatFileSize(file.size)})
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 上传进度 */}
              {uploadProgress.length > 0 && (
                <div className="mb-4 space-y-2">
                  {uploadProgress.map((progress, index) => (
                    <div key={index} className="border rounded-md p-3">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-xs font-medium text-gray-700 truncate">
                          {progress.fileName}
                        </span>
                        <span className="text-xs text-gray-500">
                          {progress.status === "completed"
                            ? "完成"
                            : progress.status === "error"
                              ? "失败"
                              : `${Math.round(progress.progress)}%`}
                        </span>
                      </div>
                      {progress.status === "uploading" && (
                        <div className="w-full bg-gray-200 rounded-full h-1">
                          <div
                            className="bg-blue-600 h-1 rounded-full transition-all duration-300"
                            style={{ width: `${progress.progress}%` }}
                          ></div>
                        </div>
                      )}
                      {progress.error && (
                        <p className="text-xs text-red-600 mt-1">
                          {progress.error}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              )}

              <div className="flex justify-end gap-3">
                <button
                  onClick={() => {
                    setShowUploadModal(false);
                    setSelectedFiles(null);
                    setUploadProgress([]);
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  onClick={handleFileUpload}
                  disabled={
                    !selectedFiles ||
                    selectedFiles.length === 0 ||
                    uploadProgress.some((p) => p.status === "uploading")
                  }
                  className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {uploadProgress.some((p) => p.status === "uploading")
                    ? "上传中..."
                    : "开始上传"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
