"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, ArrowLeft, Save, RefreshCw, Trash } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface TranslationLine {
  id: number;
  sentence_id: number;
  reference_translation: string | null;
  difficulty_analysis: string | null;
  translation_skills: string | null;
  original_content: string;
  year: string;
  type: string;
  article_id: number;
  created_at: string;
  updated_at: string;
}

const EditTranslationLinePage = ({ params }: { params: { id: string } }) => {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const [translationLine, setTranslationLine] =
    useState<TranslationLine | null>(null);
  const [formData, setFormData] = useState({
    reference_translation: "",
    difficulty_analysis: "",
    translation_skills: "",
  });

  // 加载翻译行数据
  useEffect(() => {
    const fetchTranslationLine = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/translation-lines/${params.id}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "加载翻译数据失败");
        }

        const data = await response.json();
        setTranslationLine(data.data);
        setFormData({
          reference_translation: data.data.reference_translation || "",
          difficulty_analysis: data.data.difficulty_analysis || "",
          translation_skills: data.data.translation_skills || "",
        });
      } catch (err) {
        console.error("加载翻译数据失败:", err);
        setError(err instanceof Error ? err.message : "加载翻译数据失败");
      } finally {
        setLoading(false);
      }
    };

    fetchTranslationLine();
  }, [params.id]);

  // 处理表单输入变化
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // 保存翻译
  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccessMessage(null);

      const response = await fetch(`/api/translation-lines/${params.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "保存翻译失败");
      }

      const data = await response.json();
      setSuccessMessage("翻译保存成功");

      // 更新页面显示的翻译数据
      if (translationLine) {
        setTranslationLine({
          ...translationLine,
          ...formData,
        });
      }
    } catch (err) {
      console.error("保存翻译失败:", err);
      setError(err instanceof Error ? err.message : "保存翻译失败");
    } finally {
      setSaving(false);

      // 3秒后清除成功消息
      if (successMessage) {
        const timer = setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
        return () => clearTimeout(timer);
      }
    }
  };

  // 生成AI翻译
  const handleGenerateAI = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!translationLine) {
        throw new Error("翻译数据未加载");
      }

      const response = await fetch("/api/translate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          type: "sentence",
          content: translationLine.original_content,
          sentenceId: translationLine.sentence_id,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "生成AI翻译失败");
      }

      const data = await response.json();

      // 更新表单数据
      setFormData({
        reference_translation: data.data.reference_translation || "",
        difficulty_analysis: data.data.difficulty_analysis || "",
        translation_skills: data.data.translation_skills || "",
      });

      setSuccessMessage("AI翻译生成成功");
    } catch (err) {
      console.error("生成AI翻译失败:", err);
      setError(err instanceof Error ? err.message : "生成AI翻译失败");
    } finally {
      setLoading(false);
    }
  };

  // 删除翻译
  const handleDelete = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/translation-lines/${params.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "删除翻译失败");
      }

      router.push("/admin/translations");
    } catch (err) {
      console.error("删除翻译失败:", err);
      setError(err instanceof Error ? err.message : "删除翻译失败");
      setLoading(false);
    }
  };

  if (loading && !translationLine) {
    return (
      <div className="flex justify-center items-center h-[70vh]">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push("/admin/translations")}
            className="mr-2"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            返回
          </Button>
          <h1 className="text-3xl font-bold">编辑划线句子翻译</h1>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            disabled={loading || saving}
            onClick={handleGenerateAI}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            生成AI翻译
          </Button>
          <Button
            variant="default"
            disabled={loading || saving}
            onClick={handleSave}
          >
            {saving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                保存中...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                保存
              </>
            )}
          </Button>
          <AlertDialog
            open={deleteDialogOpen}
            onOpenChange={setDeleteDialogOpen}
          >
            <AlertDialogTrigger asChild>
              <Button variant="destructive">
                <Trash className="h-4 w-4 mr-2" />
                删除
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>确认删除</AlertDialogTitle>
                <AlertDialogDescription>
                  此操作不可撤销，确定要删除此翻译记录吗？
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>取消</AlertDialogCancel>
                <AlertDialogAction onClick={handleDelete}>
                  确认删除
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6">
          {successMessage}
        </div>
      )}

      {translationLine && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>句子信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <Label>句子ID</Label>
                  <Input value={translationLine.sentence_id} disabled />
                </div>
                <div>
                  <Label>年份</Label>
                  <Input value={translationLine.year} disabled />
                </div>
                <div>
                  <Label>类型</Label>
                  <Input value={translationLine.type} disabled />
                </div>
                <div className="col-span-3">
                  <Label>原句内容</Label>
                  <div className="p-3 border rounded-md mt-1 bg-yellow-50 border-yellow-200">
                    {translationLine.original_content}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>划线句子翻译</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="reference_translation" className="text-base">
                    参考翻译
                  </Label>
                  <Textarea
                    id="reference_translation"
                    name="reference_translation"
                    value={formData.reference_translation}
                    onChange={handleInputChange}
                    placeholder="输入参考翻译..."
                    className="h-24 mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="difficulty_analysis" className="text-base">
                    难点分析
                  </Label>
                  <Textarea
                    id="difficulty_analysis"
                    name="difficulty_analysis"
                    value={formData.difficulty_analysis}
                    onChange={handleInputChange}
                    placeholder="输入难点分析..."
                    className="h-32 mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="translation_skills" className="text-base">
                    翻译技巧
                  </Label>
                  <Textarea
                    id="translation_skills"
                    name="translation_skills"
                    value={formData.translation_skills}
                    onChange={handleInputChange}
                    placeholder="输入翻译技巧..."
                    className="h-32 mt-1"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default EditTranslationLinePage;
