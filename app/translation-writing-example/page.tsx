"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import TranslationComponent from "../components/TranslationComponent";
import WritingComponent from "../components/WritingComponent";

// 定义写作任务类型
type TaskType = "small_composition" | "large_composition";

// 示例翻译行数据
const exampleTranslationLines = [
  {
    id: 1,
    sentence_id: 1,
    content:
      "While acknowledging the potential benefits of artificial intelligence, many experts caution against overlooking the ethical implications.",
    reference_translation:
      "尽管承认人工智能的潜在益处，许多专家警告不要忽视其伦理影响。",
    difficulty_analysis:
      '翻译难点：\n1. "acknowledge"与"caution against"的准确翻译\n2. "ethical implications"的专业表达\n3. 保持句子的逻辑关系',
    translation_skills:
      '翻译技巧：\n1. 将让步状语从句放在句首，符合汉语表达习惯\n2. "caution against"译为"警告不要"，表达警示意义\n3. "implications"译为"影响"，在此语境下更为贴切',
  },
];

// 示例整段翻译数据
const exampleTranslation = {
  id: 1,
  paper_id: 1,
  content:
    "Climate change is one of the most pressing challenges of our time, requiring immediate and coordinated global action. Rising temperatures, changing precipitation patterns, and increasingly frequent extreme weather events are already affecting ecosystems, agriculture, and human settlements worldwide. Despite the overwhelming scientific consensus on the human causes of climate change, political and economic barriers continue to impede effective responses. Addressing this crisis will require not only technological innovation and policy reform but also fundamental changes in consumption patterns and economic systems.",
  reference_translation:
    "气候变化是我们这个时代最紧迫的挑战之一，需要立即采取协调一致的全球行动。气温上升、降水模式改变以及日益频繁的极端天气事件已经在全球范围内影响着生态系统、农业和人类聚居地。尽管在气候变化的人为原因上存在压倒性的科学共识，但政治和经济障碍仍然阻碍着有效的应对措施。应对这一危机不仅需要技术创新和政策改革，还需要消费模式和经济系统的根本性变革。",
  analysis:
    '翻译难点分析：\n1. 专业术语：如"climate change"、"precipitation patterns"、"extreme weather events"等环境领域专业术语的准确翻译\n2. 长句结构：第二句包含多个并列成分，需要在汉语中保持流畅\n3. 抽象概念：如"political and economic barriers"、"consumption patterns"等抽象概念的表达\n\n翻译技巧：\n1. 术语对应：使用领域内公认的术语翻译\n2. 分句处理：适当将英语长句在汉语中分成较短的句子\n3. 调整语序：按照汉语的表达习惯调整句子成分顺序',
};

// 示例写作任务数据
const exampleWritingTasks = [
  {
    id: 1,
    paper_id: 1,
    task_type: "small_composition" as TaskType,
    writing_type: "应用文",
    prompt:
      "Write an email to a friend about your recent vacation. You should write about 100 words.",
    word_requirement: "100 words",
    ai_reference:
      "Dear [Friend's Name],\n\nI hope this email finds you well. I've just returned from an amazing vacation in Thailand and wanted to share my experience with you.\n\nThe beaches were absolutely stunning with crystal-clear water and white sand. I spent most days relaxing by the sea and enjoying delicious local cuisine. The Thai people were incredibly friendly and welcoming.\n\nI've taken lots of photos and can't wait to show them to you when we meet next week.\n\nBest regards,\n[Your Name]",
    scoring_criteria:
      "评分标准：\n1. 内容完整性 (30%)\n2. 语言准确性 (30%)\n3. 结构与组织 (20%)\n4. 词汇使用 (20%)",
    tips: "# 小作文写作建议\n\n1. **格式正确**: 注意应用文的格式，包括称呼、正文和结束语\n2. **简明扼要**: 控制字数在要求范围内\n3. **确保信息完整**: 应包含所有必要信息\n4. **语言自然**: 使用日常交流的语言风格",
  },
  {
    id: 2,
    paper_id: 1,
    task_type: "large_composition" as TaskType,
    writing_type: "图表作文",
    prompt:
      "Write an essay based on the chart below. In your writing, you should:\n1) interpret the chart, and\n2) give your comments.\nYou should write at least 150 words.",
    word_requirement: "不少于150词",
    image_url: "https://placehold.co/600x400?text=Example+Chart",
    ai_reference:
      "# The Rise of Online Education\n\nThe chart illustrates the dramatic growth in online education enrollment from 2018 to 2023. According to the data, the number of students taking online courses has increased from approximately 5 million in 2018 to over 15 million in 2023, representing a threefold increase over this five-year period.\n\nThis significant trend can be attributed to several factors. Firstly, technological advancements have made online learning platforms more accessible and user-friendly. High-speed internet access has become more widespread, allowing more people to participate in online education regardless of their location. Secondly, the COVID-19 pandemic, which began in early 2020, accelerated this shift as educational institutions worldwide were forced to adopt remote learning solutions.\n\nThe implications of this trend are profound. Online education offers flexibility that traditional classroom settings cannot match, allowing students to learn at their own pace and schedule. It also expands educational opportunities to those who might otherwise be unable to attend physical institutions due to geographical, financial, or physical limitations.\n\nHowever, challenges remain. The quality of online education varies significantly, and not all subjects are equally suited to remote learning formats. Additionally, the lack of direct social interaction can impact the development of crucial soft skills.\n\nIn conclusion, while the rapid growth of online education represents a positive development in expanding educational access, educational institutions and policymakers must ensure that this growth is accompanied by high standards of quality and effectiveness.",
    scoring_criteria:
      "评分标准：\n1. 内容完整性 (25%)\n2. 语言准确性 (25%)\n3. 结构与组织 (20%)\n4. 词汇多样性 (20%)\n5. 批判性思考 (10%)",
    tips: "# 大作文写作建议\n\n1. **结构清晰**: 包含引言、正文和结论\n2. **准确描述图表**: 先客观描述图表显示的主要趋势和数据\n3. **深入分析**: 分析趋势背后的原因和影响\n4. **提供个人见解**: 表达合理的个人观点\n5. **注意词汇多样性**: 使用多样的表达方式描述趋势\n6. **运用适当的连接词**: 使文章结构更加清晰",
  },
];

const originalContent = `In today's rapidly evolving technological landscape, artificial intelligence (AI) has emerged as a transformative force across various sectors. While acknowledging the potential benefits of artificial intelligence, many experts caution against overlooking the ethical implications. The integration of AI into healthcare, education, and business operations has demonstrated significant efficiency gains and innovative solutions to complex problems.

However, concerns about privacy, bias in algorithms, job displacement, and the concentration of power in tech companies have raised important questions about how AI should be developed and regulated. Striking the right balance between innovation and ethical considerations will be crucial for ensuring that AI technologies benefit society as a whole.`;

// 示例响应提交后的响应
const exampleWritingResponse = {
  content:
    "Dear Sarah,\n\nI hope this email finds you well. I've just returned from an incredible vacation in Japan and I'm excited to share my experiences with you.\n\nTokyo was a fascinating blend of traditional culture and modern technology. I visited ancient temples, explored bustling markets, and enjoyed the most amazing sushi. The cherry blossoms were in full bloom, creating a beautiful pink canopy throughout the city parks.\n\nKyoto offered a more traditional experience with its stunning temples and geisha districts. I even participated in a traditional tea ceremony!\n\nI took hundreds of photos and bought some unique souvenirs. Let's meet for coffee next week so I can show you everything.\n\nBest wishes,\nAlex",
  ai_score: 85,
  ai_feedback:
    '# 写作评分反馈\n\n## 总分：85/100\n\n### 内容完整性 (25/30)\n你的邮件包含了大部分必要的元素，如问候语、假期描述和结束语。内容充实，描述了日本之行的主要景点和体验。\n\n### 优点：\n- 清晰的结构和流畅的表达\n- 具体的细节（如东京的樱花、京都的寺庙）增强了真实感\n- 自然的开头和结尾\n\n### 改进建议：\n- 可以加入更多关于旅行时间或行程安排的具体信息\n- 与收件人的互动可以更加个性化\n\n### 语言准确性 (27/30)\n整体语言使用准确，没有明显的语法错误。\n\n### 词汇与表达 (18/20)\n使用了一些描述性词汇如"fascinating blend"、"bustling markets"，增强了表现力。\n\n### 修改示例：\n原句：\n> "I took hundreds of photos and bought some unique souvenirs."\n\n改进：\n> "I captured hundreds of photos of breathtaking scenery and purchased some unique handcrafted souvenirs that reflect Japan\'s rich cultural heritage."\n\n---\n\n总体而言，这是一篇优秀的应用文，符合邮件格式要求，内容丰富且语言得体。',
};

const TranslationWritingPage = () => {
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState("translation-english1");
  const [writingResponse, setWritingResponse] = useState<any>(null);

  // 从URL参数中获取初始标签
  useEffect(() => {
    const tab = searchParams?.get("tab");
    if (tab === "writing") {
      setActiveTab("writing-english1");
    } else if (tab === "translation") {
      setActiveTab("translation-english1");
    } else if (tab === "translation-english2") {
      setActiveTab("translation-english2");
    } else if (tab === "writing-english2") {
      setActiveTab("writing-english2");
    }
  }, [searchParams]);

  // 模拟翻译提交
  const handleTranslate = (text: string) => {
    console.log("提交翻译:", text);
    // 在实际应用中，这里应该调用API提交翻译
    alert("翻译已提交！在实际应用中会发送到服务器进行处理。");
  };

  // 模拟写作提交
  const handleWritingSubmit = async (taskId: number, content: string) => {
    console.log("提交作文:", { taskId, content });
    // 在实际应用中，这里应该调用API评分
    // 模拟API响应延迟
    await new Promise((resolve) => setTimeout(resolve, 1000));
    return exampleWritingResponse;
  };

  return (
    <main className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-800 mb-8 text-center">
          翻译与写作功能演示
        </h1>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="translation-english1">英语一翻译</TabsTrigger>
            <TabsTrigger value="translation-english2">英语二翻译</TabsTrigger>
            <TabsTrigger value="writing-english1">英语一写作</TabsTrigger>
            <TabsTrigger value="writing-english2">英语二写作</TabsTrigger>
          </TabsList>

          {/* 英语一翻译 */}
          <TabsContent value="translation-english1" className="mt-6">
            <TranslationComponent
              examType="英语一"
              translationLines={exampleTranslationLines}
              originalContent={originalContent}
              onUserTranslate={handleTranslate}
            />
          </TabsContent>

          {/* 英语二翻译 */}
          <TabsContent value="translation-english2" className="mt-6">
            <TranslationComponent
              examType="英语二"
              translation={exampleTranslation}
              onUserTranslate={handleTranslate}
            />
          </TabsContent>

          {/* 英语一写作 */}
          <TabsContent value="writing-english1" className="mt-6">
            <WritingComponent
              examType="英语一"
              tasks={exampleWritingTasks}
              onSubmit={handleWritingSubmit}
            />
          </TabsContent>

          {/* 英语二写作 */}
          <TabsContent value="writing-english2" className="mt-6">
            <WritingComponent
              examType="英语二"
              tasks={exampleWritingTasks}
              onSubmit={handleWritingSubmit}
            />
          </TabsContent>
        </Tabs>
      </div>
    </main>
  );
};

export default TranslationWritingPage;
