import { verifyOtpAction, resendOtpAction } from "@/app/actions";
import { SubmitButton } from "@/components/submit-button";
import { FormMessage, Message } from "@/components/form-message";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import Link from "next/link";
import {
  RiMailLine,
  RiLockLine,
  RiShieldCheckLine,
  RiRefreshLine,
} from "react-icons/ri";

export default async function VerifyOtp({
  searchParams,
}: {
  searchParams: { email?: string; password?: string } & Promise<Message>;
}) {
  const message = await searchParams;
  const email = searchParams.email || "";
  const password = searchParams.password || "";

  if ("message" in message && !email) {
    return (
      <div className="w-full flex-1 flex items-center justify-center gap-2 p-4">
        <FormMessage message={message} />
      </div>
    );
  }

  return (
    <Card className="shadow-lg transition-all duration-300 hover:shadow-xl">
      <CardHeader className="space-y-1">
        <div className="flex justify-center mb-2">
          <div className="rounded-full bg-blue-100 p-3 text-blue-600 dark:bg-blue-900 dark:text-blue-300">
            <RiShieldCheckLine size={24} />
          </div>
        </div>
        <CardTitle className="text-2xl font-bold text-center">
          验证账号
        </CardTitle>
        <CardDescription className="text-center">
          请输入发送至 {email} 的 6 位数验证码
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form className="flex flex-col gap-4">
          <input type="hidden" name="email" value={email} />
          {password && <input type="hidden" name="password" value={password} />}

          <div className="space-y-2">
            <Label htmlFor="token" className="text-sm font-medium">
              验证码
            </Label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
                <RiLockLine />
              </div>
              <Input
                name="token"
                id="token"
                placeholder="6位数验证码"
                required
                className="pl-10 text-lg tracking-widest text-center font-mono"
                maxLength={6}
                pattern="[0-9]{6}"
                inputMode="numeric"
                autoComplete="one-time-code"
              />
            </div>
          </div>

          <SubmitButton
            formAction={verifyOtpAction}
            pendingText="验证中..."
            className="mt-2 w-full bg-blue-600 hover:bg-blue-700 text-white"
          >
            验证
          </SubmitButton>
          <FormMessage message={message} />
        </form>
      </CardContent>
      <CardFooter className="flex flex-col items-center gap-2 border-t pt-4">
        <p className="text-xs text-gray-500 mb-2">
          没有收到验证码？请检查您的垃圾邮件文件夹
        </p>

        <div className="flex gap-4 items-center justify-center">
          <form>
            <input type="hidden" name="email" value={email} />
            {password && (
              <input type="hidden" name="password" value={password} />
            )}
            <SubmitButton
              formAction={resendOtpAction}
              pendingText="发送中..."
              className="text-sm text-blue-600 hover:underline dark:text-blue-400 bg-transparent hover:bg-transparent flex items-center gap-1"
            >
              <RiRefreshLine className="inline" />
              重新发送验证码
            </SubmitButton>
          </form>

          <Link
            className="text-sm text-gray-500 hover:underline dark:text-gray-400"
            href={`/sign-up`}
          >
            返回注册
          </Link>
        </div>
      </CardFooter>
    </Card>
  );
}
