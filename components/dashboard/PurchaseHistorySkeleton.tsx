export default function PurchaseHistorySkeleton() {
  return (
    <div className="overflow-hidden shadow sm:rounded-lg bg-white animate-pulse">
      <div className="px-4 py-5 sm:px-6">
        <div className="h-6 bg-gray-200 rounded w-1/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left w-1/4">
                <div className="h-4 bg-gray-200 rounded"></div>
              </th>
              <th className="px-6 py-3 text-left w-1/6">
                <div className="h-4 bg-gray-200 rounded"></div>
              </th>
              <th className="px-6 py-3 text-left w-1/6">
                <div className="h-4 bg-gray-200 rounded"></div>
              </th>
              <th className="px-6 py-3 text-left w-1/6">
                <div className="h-4 bg-gray-200 rounded"></div>
              </th>
              <th className="px-6 py-3 text-left w-1/6">
                <div className="h-4 bg-gray-200 rounded"></div>
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {Array.from({ length: 3 }).map((_, index) => (
              <tr key={index}>
                <td className="px-6 py-4">
                  <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </td>
                <td className="px-6 py-4">
                  <div className="h-4 bg-gray-200 rounded"></div>
                </td>
                <td className="px-6 py-4">
                  <div className="h-4 bg-gray-200 rounded"></div>
                </td>
                <td className="px-6 py-4">
                  <div className="h-6 bg-gray-200 rounded w-2/3"></div>
                </td>
                <td className="px-6 py-4">
                  <div className="h-5 bg-gray-200 rounded w-1/2"></div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
} 