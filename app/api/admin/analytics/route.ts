import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    // 获取查询参数中的日期
    const searchParams = request.nextUrl.searchParams;
    const dateParam = searchParams.get("date");

    // 设置日期范围
    let startDate: Date;
    let endDate: Date;

    if (dateParam) {
      // 将日期参数转换为日期对象
      startDate = new Date(dateParam);
      startDate.setHours(0, 0, 0, 0);

      endDate = new Date(dateParam);
      endDate.setHours(23, 59, 59, 999);
    } else {
      // 默认使用今天
      startDate = new Date();
      startDate.setHours(0, 0, 0, 0);

      endDate = new Date();
      endDate.setHours(23, 59, 59, 999);
    }

    // 转换为ISO字符串，用于数据库查询
    const startDateIso = startDate.toISOString();
    const endDateIso = endDate.toISOString();

    // 获取系统配置 - 免费下载限制
    const { data: configData } = await supabase
      .from("system_config")
      .select("value")
      .eq("key", "free_download_limit")
      .single();

    const freeLimit = configData ? parseInt(configData.value, 10) : 3;

    // 获取所有下载记录的统计信息
    const { data: allDownloadData, error: allDownloadError } = await supabase
      .from("download_counts")
      .select("*")
      .order("count", { ascending: false })
      .limit(10000); // 设置一个足够大的限制，或者使用分页获取所有数据

    if (allDownloadError) {
      console.error("获取下载记录失败:", allDownloadError);
      return NextResponse.json({ error: "获取下载记录失败" }, { status: 500 });
    }

    // 获取总用户数的准确计数
    const { count: totalUniqueVisitors, error: countError } = await supabase
      .from("download_counts")
      .select("*", { count: "exact", head: true });

    if (countError) {
      console.error("获取用户总数失败:", countError);
    }

    // 获取特定日期范围内有更新的下载记录
    const { data: filteredDownloadData, error: filteredDownloadError } =
      await supabase
        .from("download_counts")
        .select("*")
        .gte("updated_at", startDateIso)
        .lte("updated_at", endDateIso)
        .order("count", { ascending: false });

    if (filteredDownloadError) {
      console.error("获取按日期过滤的下载记录失败:", filteredDownloadError);
      return NextResponse.json({ error: "获取下载记录失败" }, { status: 500 });
    }

    // 计算统计数据
    const totalDownloads = allDownloadData.reduce(
      (sum, item) => sum + item.count,
      0
    );

    // 筛选当天的下载量
    const todayDownloads = filteredDownloadData.reduce(
      (sum, item) => sum + item.count,
      0
    );

    // 独立访问用户数 (总体的) - 使用准确计数，如果计数失败则使用数组长度作为备选
    const uniqueVisitors = totalUniqueVisitors || allDownloadData.length;

    // 当日独立访问用户数
    const todayUniqueVisitors = filteredDownloadData.length;

    // 达到限额的用户数 (总体的)
    const maxedOutUsers = allDownloadData.filter(
      (item) => item.count >= freeLimit
    ).length;

    // 当日达到限额的用户数
    const todayMaxedOutUsers = filteredDownloadData.filter(
      (item) => item.count >= freeLimit
    ).length;

    return NextResponse.json({
      totalDownloads,
      todayDownloads,
      uniqueVisitors,
      todayUniqueVisitors,
      maxedOutUsers,
      todayMaxedOutUsers,
      ipData: filteredDownloadData, // 只返回指定日期范围内的IP数据
      freeLimit,
    });
  } catch (error) {
    console.error("获取分析数据失败:", error);
    return NextResponse.json({ error: "获取分析数据失败" }, { status: 500 });
  }
}
