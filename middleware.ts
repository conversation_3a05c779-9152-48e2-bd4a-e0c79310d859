import { type NextRequest, NextResponse } from "next/server";
// 不再导入 updateSession
// import { updateSession } from "@/utils/supabase/middleware";

// 中间件变为空操作
export async function middleware(request: NextRequest) {
  console.log(`⚡ 中间件已完全禁用: ${request.nextUrl.pathname}`);
  // 直接返回，不做任何处理
  return NextResponse.next();
}

export const config = {
  // 中间件匹配配置
  // 如果你想完全禁用中间件，也可以设置为空数组，这样不会匹配任何路径
  matcher: [],
};
