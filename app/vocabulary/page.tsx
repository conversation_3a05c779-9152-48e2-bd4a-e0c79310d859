import VocabularyList from "./vocabulary-list";
import AlphabetFilter from "./alphabet-filter";
import SearchBar from "./search-bar";
import Loading from "./loading";
import { redirect } from "next/navigation";

export const metadata = {
  title: "考研英语词汇表 | MBDATA",
  description: "全面的考研英语词汇表，包含详细的单词分析、例句和记忆技巧",
};

export default function VocabularyPage({
  searchParams,
}: {
  searchParams: { letter?: string; search?: string };
}) {
  // 如果没有指定字母，默认重定向到字母A
  if (!searchParams.letter) {
    redirect("/vocabulary?letter=a");
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部标题栏 */}
      {/* <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="container mx-auto px-4 py-5">
          <div className="flex flex-col sm:flex-row justify-between items-center">
            <h1 className="text-2xl sm:text-3xl font-bold text-blue-600">
              考研英语词汇表
            </h1>
            <p className="mt-2 sm:mt-0 text-gray-500 text-sm">
              掌握核心词汇，提高英语水平
            </p>
          </div>
        </div>
      </div> */}

      <div className="container mx-auto px-4 py-6">
        {/* 搜索区域 */}
        <div className="mb-6 bg-white rounded-xl shadow-sm p-5 border border-gray-100">
          <h2 className="text-lg font-medium text-gray-800 mb-3">搜索单词</h2>
          <SearchBar />
        </div>

        {/* 字母浏览区域 */}
        <div className="mb-6 bg-white rounded-xl shadow-sm p-5 border border-gray-100">
          <AlphabetFilter />
        </div>

        {/* 单词列表区域 */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="p-5">
            <VocabularyList searchParams={searchParams} />
          </div>
        </div>

        {/* 页面底部信息 */}
        <div className="mt-8 text-center text-sm text-gray-500 py-4">
          <p>不断更新的考研英语词汇库 - 助力您的考研之路</p>
        </div>
      </div>
    </div>
  );
}

function VocabularyLoading() {
  return (
    <div className="p-8 space-y-8">
      <div className="flex justify-center items-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-blue-600">加载词汇表...</span>
      </div>

      {/* 模拟字母组的加载效果 */}
      <div className="space-y-6">
        {[1, 2].map((group) => (
          <div key={group}>
            <div className="h-8 w-16 bg-gray-200 animate-pulse mb-4 rounded"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Array.from({ length: 6 }, (_, i) => (
                <div
                  key={i}
                  className="h-36 rounded-lg bg-gray-100 animate-pulse"
                ></div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
