import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || "";
const supabase = createClient(supabaseUrl, supabaseKey);

export async function POST(request: Request) {
  try {
    // 获取请求数据
    const sentencesData = await request.json();

    if (!Array.isArray(sentencesData) || sentencesData.length === 0) {
      return NextResponse.json(
        { error: "Invalid data format, expected an array of sentences" },
        { status: 400 }
      );
    }

    // 准备要插入的数据
    const insertData = sentencesData.map((sentence) => {
      // 验证必填字段
      if (!sentence.article_id || !sentence.content) {
        throw new Error("Missing required fields: article_id or content");
      }

      return {
        article_id: sentence.article_id,
        explain_md: sentence.explain_md || "",
        paragraph_num: sentence.paragraph_num || 1,
        sequence: sentence.sequence || 1,
        content: sentence.content,
        is_marked: sentence.is_marked || false,
      };
    });

    // 执行Supabase批量插入
    const { error } = await supabase.from("sentences").insert(insertData);

    if (error) {
      throw error;
    }

    return NextResponse.json({
      success: true,
      message: "Sentences saved successfully",
    });
  } catch (error) {
    console.error("Error saving sentences:", error);
    return NextResponse.json(
      {
        error: "Failed to save sentences",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  const startTime = Date.now();
  console.log("GET sentences API 开始处理请求");

  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const year = searchParams.get("year");
    const type = searchParams.get("type");
    const sectionType = searchParams.get("sectionType");
    const search = searchParams.get("search");

    console.log(
      `查询参数: page=${page}, pageSize=${pageSize}, year=${year}, type=${type}, sectionType=${sectionType}, search=${search}`
    );

    // 计算偏移量
    const offset = (page - 1) * pageSize;

    // 启用流式传输模式
    const fetchDataStartTime = Date.now();

    // 使用 RPC 调用优化查询速度
    // 添加类型声明以解决TypeScript错误
    // 基本查询，不包含papers关联
    let query = supabase.from("sentences").select(`
        id, 
        content, 
        paragraph_num, 
        sequence, 
        created_at,
        article_id
      `);

    // 使用单独的子查询获取paper信息
    const paperInfoQuery = `
      papers!inner(
        year,
        type,
        section_type
      )
    `;

    // 仅当需要过滤时添加连接
    if (year || type || sectionType) {
      // 重新定义查询以包含关联表
      query = supabase.from("sentences").select(`
          id, 
          content, 
          paragraph_num, 
          sequence, 
          created_at,
          article_id,
          ${paperInfoQuery}
        `);

      if (year) {
        query = query.eq("papers.year", year);
      }

      if (type) {
        query = query.eq("papers.type", type);
      }

      if (sectionType) {
        query = query.eq("papers.section_type", sectionType);
      }
    }

    if (search) {
      // 仅在内容列上进行搜索以提高性能
      query = query.ilike("content", `%${search}%`);
    }

    // 获取分页数据
    const { data, error } = await query
      .range(offset, offset + pageSize - 1)
      .order("created_at", { ascending: false });

    console.log(
      `数据查询完成，耗时: ${Date.now() - fetchDataStartTime}ms, 结果数: ${data?.length}`
    );

    if (error) {
      throw error;
    }

    // 获取相关的papers信息，如果之前没有联表查询
    // 使用Record类型为papersData添加类型安全
    let papersData: Record<string, any> = {};
    if (!(year || type || sectionType)) {
      const articleIds = data.map((item) => item.article_id).filter(Boolean);

      if (articleIds.length > 0) {
        const papersStartTime = Date.now();
        const { data: papers, error: papersError } = await supabase
          .from("papers")
          .select("id, year, type, section_type")
          .in("id", articleIds);

        console.log(`Papers查询完成，耗时: ${Date.now() - papersStartTime}ms`);

        if (papersError) {
          throw papersError;
        }

        // 创建lookup表以便快速访问
        if (papers) {
          papersData = papers.reduce((acc: Record<string, any>, paper) => {
            acc[paper.id] = paper;
            return acc;
          }, {});
        }
      }
    }

    // 处理结果数据
    const formatStartTime = Date.now();
    const formattedData = data.map((item: any) => {
      let year, type, sectionType;

      if (item.papers) {
        // 如果通过联表查询获取了papers数据
        year = item.papers.year;
        type = item.papers.type;
        sectionType = item.papers.section_type;
      } else if (item.article_id && papersData[item.article_id]) {
        // 从分开的查询中获取papers数据
        const paper = papersData[item.article_id];
        year = paper.year;
        type = paper.type;
        sectionType = paper.section_type;
      } else {
        // 默认值
        year = "未知";
        type = "未知";
        sectionType = "未知";
      }

      return {
        id: item.id,
        year,
        type,
        sectionType,
        title: `${year}年全国硕士研究生招生考试${type}试题`,
        originalContent: item.content,
        paragraphNum: item.paragraph_num,
        indexNum: item.sequence,
        createdAt: item.created_at,
      };
    });

    console.log(`数据格式化完成，耗时: ${Date.now() - formatStartTime}ms`);

    // 简化的计数查询
    const countStartTime = Date.now();
    let countQuery;

    if (year || type || sectionType) {
      // 带关联的计数查询
      countQuery = supabase
        .from("sentences")
        .select("id, papers!inner(id)", { count: "exact" });

      if (year) {
        countQuery = countQuery.eq("papers.year", year);
      }
      if (type) {
        countQuery = countQuery.eq("papers.type", type);
      }
      if (sectionType) {
        countQuery = countQuery.eq("papers.section_type", sectionType);
      }
    } else {
      // 简单计数查询
      countQuery = supabase.from("sentences").select("id", { count: "exact" });
    }

    if (search) {
      countQuery = countQuery.ilike("content", `%${search}%`);
    }

    const { count, error: countError } = await countQuery;
    console.log(`计数查询完成，耗时: ${Date.now() - countStartTime}ms`);

    if (countError) {
      console.error("计数查询错误:", countError);
      // 如果计数查询出错，使用替代方法估算总数
      const estimatedTotal =
        page * pageSize + (data.length === pageSize ? pageSize : 0);
      const totalPages = Math.ceil(estimatedTotal / pageSize);

      console.log(
        `GET sentences API 请求处理完成(估计计数)，总耗时: ${Date.now() - startTime}ms`
      );
      return NextResponse.json({
        success: true,
        data: formattedData,
        pagination: {
          total: estimatedTotal,
          page,
          pageSize,
          totalPages,
          isEstimated: true,
        },
      });
    }

    const total = count || 0;
    const totalPages = Math.ceil(total / pageSize);

    console.log(
      `GET sentences API 请求处理完成，总耗时: ${Date.now() - startTime}ms`
    );
    return NextResponse.json({
      success: true,
      data: formattedData,
      pagination: {
        total,
        page,
        pageSize,
        totalPages,
      },
    });
  } catch (error) {
    console.error(
      `GET sentences API 错误，总耗时: ${Date.now() - startTime}ms`,
      error
    );
    return NextResponse.json(
      {
        error: "Failed to fetch sentences",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// DELETE方法：删除指定ID的句子
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "Missing required parameter: id" },
        { status: 400 }
      );
    }

    // 执行删除操作
    const { error } = await supabase.from("sentences").delete().eq("id", id);

    if (error) {
      throw error;
    }

    // 返回成功响应
    return NextResponse.json({
      success: true,
      message: "Sentence deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting sentence:", error);
    return NextResponse.json(
      {
        error: "Failed to delete sentence",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
