"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";

interface Paper {
  id: string;
  year: string;
  type: string;
  sectionType: string;
  createdAt: string;
}

interface UseOfEnglishText {
  id: string;
  paper_id: string;
  paragraph_number: number;
  original_text: string;
  text_analysis: string;
}

interface Option {
  text: string;
  translation: string;
}

interface UseOfEnglishQuestion {
  id: string;
  paper_id: string;
  question_number: number;
  paragraph_number: number;
  options: {
    A: Option;
    B: Option;
    C: Option;
    D: Option;
  };
  correct_answer: string;
  explanation: string;
}

const UseOfEnglishDetailPage = ({ params }: { params: { id: string } }) => {
  const router = useRouter();
  const [paper, setPaper] = useState<Paper | null>(null);
  const [paragraphs, setParagraphs] = useState<UseOfEnglishText[]>([]);
  const [questions, setQuestions] = useState<UseOfEnglishQuestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 表单状态
  const [originalText, setOriginalText] = useState<string>("");
  const [questionsText, setQuestionsText] = useState<string>("");
  const [aiRequesting, setAiRequesting] = useState(false);
  const [importLoading, setImportLoading] = useState(false);

  // 编辑题目相关状态
  const [editingQuestion, setEditingQuestion] =
    useState<UseOfEnglishQuestion | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editLoading, setEditLoading] = useState(false);

  // 加载试题信息
  useEffect(() => {
    const fetchPaperDetails = async () => {
      try {
        setLoading(true);
        setError(null);

        // 获取试题基本信息
        const paperResponse = await fetch(`/api/papers/${params.id}`);

        if (!paperResponse.ok) {
          throw new Error("获取试题信息失败");
        }

        const paperData = await paperResponse.json();

        if (paperData.success) {
          setPaper(paperData.data);

          // 获取完形填空文本内容
          const textsResponse = await fetch(
            `/api/use-of-english/texts?paperId=${params.id}`
          );

          if (!textsResponse.ok) {
            throw new Error("获取完形填空文本失败");
          }

          const textsData = await textsResponse.json();

          if (textsData.success) {
            setParagraphs(textsData.data || []);
          }

          // 获取题目列表
          const questionsResponse = await fetch(
            `/api/use-of-english/questions?paperId=${params.id}`
          );

          if (!questionsResponse.ok) {
            throw new Error("获取题目列表失败");
          }

          const questionsData = await questionsResponse.json();

          if (questionsData.success) {
            setQuestions(questionsData.data || []);
          }
        } else {
          throw new Error(paperData.error || "获取试题信息失败");
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "未知错误");
        console.error("获取试题详情错误:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchPaperDetails();
  }, [params.id]);

  // 保存完形填空原文
  const saveOriginalText = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!originalText.trim()) {
        throw new Error("请输入完形填空原文");
      }

      // 直接将整个原文保存到数据库
      const response = await fetch("/api/use-of-english/texts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          paper_id: params.id,
          original_text: originalText.trim(),
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "保存文本失败");
      }

      if (data.success) {
        // 刷新数据
        const textsResponse = await fetch(
          `/api/use-of-english/texts?paperId=${params.id}`
        );
        const textsData = await textsResponse.json();

        if (textsData.success) {
          setParagraphs(textsData.data || []);
          setOriginalText(""); // 清空输入框
        }
      } else {
        throw new Error(data.error || "保存文本失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "保存过程中出错");
      console.error("保存原文错误:", err);
    } finally {
      setLoading(false);
    }
  };

  // 将选项文本转换为带数字标记的格式（用于AI处理）
  const convertToNumberedOptions = () => {
    const allQuestions = [...questions].sort(
      (a, b) => a.question_number - b.question_number
    );
    let result = "";

    allQuestions.forEach((q) => {
      result += `${q.question_number}. A. ${q.options.A.text} B. ${q.options.B.text} C. ${q.options.C.text} D. ${q.options.D.text}\n`;
    });

    return result;
  };

  // 使用AI分析文本，生成解析和答案
  const analyzeWithAI = async () => {
    try {
      setAiRequesting(true);
      setError(null);

      // 确保有段落数据
      if (paragraphs.length === 0) {
        throw new Error("请先添加完形填空原文");
      }

      // 确保有题目数据
      if (questions.length === 0) {
        throw new Error("请先添加题目选项");
      }

      // 准备提交给AI的数据
      const originalText = paragraphs[0]?.original_text || "";
      const optionsText = convertToNumberedOptions();

      if (!originalText) {
        throw new Error("无法获取原文内容");
      }

      // 调用AI接口
      const response = await fetch("/api/ai/analyze-use-of-english", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          paper_id: params.id,
          original_text: originalText,
          options: optionsText,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "AI分析失败");
      }

      if (data.success) {
        // 刷新页面数据
        const textsResponse = await fetch(
          `/api/use-of-english/texts?paperId=${params.id}`
        );
        const textsData = await textsResponse.json();

        if (textsData.success) {
          setParagraphs(textsData.data || []);
        }

        const questionsResponse = await fetch(
          `/api/use-of-english/questions?paperId=${params.id}`
        );
        const questionsData = await questionsResponse.json();

        if (questionsData.success) {
          setQuestions(questionsData.data || []);
        }
      } else {
        throw new Error(data.error || "AI分析失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "AI分析出错");
      console.error("AI分析错误:", err);
    } finally {
      setAiRequesting(false);
    }
  };

  // 解析题目导入文本
  // 格式为：1. A. affected B. achieved C. extracted D. restored
  const parseQuestionsText = (text: string) => {
    const result = [];
    const lines = text.split("\n").filter((line) => line.trim());
    const expectedQuestionNumbers = Array.from({ length: 20 }, (_, i) => i + 1);
    const foundQuestionNumbers = new Set();

    for (const line of lines) {
      // 修改正则表达式，以支持多词选项
      // 匹配格式：题号. A. 选项A B. 选项B C. 选项C D. 选项D
      const match = line.match(
        /(\d+)\.\s+A\.\s+(.*?)\s+B\.\s+(.*?)\s+C\.\s+(.*?)\s+D\.\s+(.*?)$/
      );

      if (match) {
        const [_, questionNumber, optionA, optionB, optionC, optionD] = match;
        const qNum = parseInt(questionNumber, 10);
        foundQuestionNumbers.add(qNum);

        result.push({
          paper_id: params.id,
          question_number: qNum,
          paragraph_number: 1, // 默认第一段
          options: {
            A: { text: optionA.trim(), translation: "" },
            B: { text: optionB.trim(), translation: "" },
            C: { text: optionC.trim(), translation: "" },
            D: { text: optionD.trim(), translation: "" },
          },
          correct_answer: null,
        });
      } else {
        console.warn(`无法解析题目行: ${line}`);
      }
    }

    // 检查是否缺少题目
    const missingQuestions = expectedQuestionNumbers.filter(
      (num) => !foundQuestionNumbers.has(num)
    );

    if (missingQuestions.length > 0) {
      throw new Error(
        `题目不完整，缺少以下题号: ${missingQuestions.join(", ")}`
      );
    }

    return result;
  };

  // 导入题目
  const importQuestions = async () => {
    try {
      setImportLoading(true);
      setError(null);

      if (!questionsText.trim()) {
        throw new Error("请输入题目信息");
      }

      // 解析题目文本
      const parsedQuestions = parseQuestionsText(questionsText);

      if (parsedQuestions.length === 0) {
        throw new Error("无法解析题目信息，请检查格式");
      }

      // 检查题目是否完整（20题）
      if (parsedQuestions.length !== 20) {
        throw new Error(
          `题目数量不完整，应为20题，当前有${parsedQuestions.length}题`
        );
      }

      // 保存到数据库
      const response = await fetch("/api/use-of-english/questions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          paper_id: params.id,
          questions: parsedQuestions,
          replaceAll: false, // 增量添加，不清空现有题目
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "导入题目失败");
      }

      if (data.success) {
        // 刷新数据
        const questionsResponse = await fetch(
          `/api/use-of-english/questions?paperId=${params.id}`
        );
        const questionsData = await questionsResponse.json();

        if (questionsData.success) {
          setQuestions(questionsData.data || []);
          setQuestionsText(""); // 清空输入框
        }
      } else {
        throw new Error(data.error || "导入题目失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "导入过程中出错");
      console.error("导入题目错误:", err);
    } finally {
      setImportLoading(false);
    }
  };

  // 开始编辑题目
  const startEdit = (question: UseOfEnglishQuestion) => {
    setEditingQuestion(JSON.parse(JSON.stringify(question)));
    setIsEditing(true);
  };

  // 取消编辑
  const cancelEdit = () => {
    setEditingQuestion(null);
    setIsEditing(false);
  };

  // 更新编辑中题目的字段
  const updateEditingQuestion = (field: string, value: any) => {
    if (!editingQuestion) return;

    if (field.startsWith("option_")) {
      // 处理选项更新
      const [_, optionKey, optionField] = field.split("_");
      setEditingQuestion({
        ...editingQuestion,
        options: {
          ...editingQuestion.options,
          [optionKey]: {
            ...editingQuestion.options[optionKey as "A" | "B" | "C" | "D"],
            [optionField]: value,
          },
        },
      });
    } else {
      // 处理其他字段更新
      setEditingQuestion({
        ...editingQuestion,
        [field]: value,
      });
    }
  };

  // 保存编辑的题目
  const saveEditedQuestion = async () => {
    if (!editingQuestion) return;

    try {
      setEditLoading(true);
      setError(null);

      const response = await fetch(
        `/api/use-of-english/questions/${editingQuestion.id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(editingQuestion),
        }
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "保存题目失败");
      }

      if (data.success) {
        // 更新题目列表
        const updatedQuestions = questions.map((q) =>
          q.id === editingQuestion.id ? editingQuestion : q
        );
        setQuestions(updatedQuestions);

        // 关闭编辑模式
        setIsEditing(false);
        setEditingQuestion(null);
      } else {
        throw new Error(data.error || "保存题目失败");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "保存题目过程中出错");
      console.error("保存编辑题目错误:", err);
    } finally {
      setEditLoading(false);
    }
  };

  // 加载中状态
  if (loading && !paper) {
    return (
      <div className="w-full min-h-[50vh] flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
          <p className="text-blue-700 font-medium">正在加载...</p>
        </div>
      </div>
    );
  }

  // 错误状态
  if (error && !paper) {
    return (
      <div className="w-full min-h-[50vh] flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="bg-red-100 p-4 rounded-full">
            <svg
              className="h-12 w-12 text-red-500"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <p className="text-red-700 font-medium">{error}</p>
          <Link
            href="/admin/use-of-english"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            返回列表
          </Link>
        </div>
      </div>
    );
  }

  // 试题不存在
  if (!paper) {
    return (
      <div className="w-full min-h-[50vh] flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <p className="text-gray-700 font-medium">找不到试题信息</p>
          <Link
            href="/admin/use-of-english"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            返回列表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-8">
      {/* 顶部标题和操作栏 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
            完形填空试题管理
          </h1>
          <p className="text-gray-500 mt-2 text-lg">
            {paper.year}年{paper.type}完形填空
          </p>
        </div>
        <div className="flex space-x-4">
          <Link
            href="/admin/use-of-english"
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors"
          >
            返回列表
          </Link>
          <button
            onClick={analyzeWithAI}
            disabled={
              aiRequesting || paragraphs.length === 0 || questions.length === 0
            }
            className={`px-4 py-2 rounded-xl transition-colors ${
              aiRequesting || paragraphs.length === 0 || questions.length === 0
                ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                : "bg-purple-600 text-white hover:bg-purple-700"
            }`}
          >
            {aiRequesting ? "AI分析中..." : "使用AI生成解析和答案"}
          </button>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-500"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* 完形填空内容和题目管理（合并为一个页面） */}
      <div className="space-y-8">
        {/* 完形填空原文部分 */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="p-6 border-b border-gray-100">
            <h2 className="text-xl font-bold text-gray-800">完形填空原文</h2>
          </div>

          <div className="p-6 space-y-4">
            {paragraphs.length > 0 ? (
              <div className="border rounded-lg p-4">
                <div className="prose max-w-none mb-4">
                  <p>{paragraphs[0]?.original_text}</p>
                </div>

                {paragraphs[0]?.text_analysis && (
                  <div className="mt-4 bg-gray-50 rounded-lg p-4">
                    <h4 className="text-md font-medium text-gray-700 mb-2">
                      解析:
                    </h4>
                    <div
                      className="prose max-w-none"
                      dangerouslySetInnerHTML={{
                        __html: paragraphs[0].text_analysis,
                      }}
                    />
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-500">暂无完形填空原文，请添加内容</p>
              </div>
            )}

            {/* 仅在没有原文时显示添加原文表单 */}
            {paragraphs.length === 0 && (
              <div className="border-t border-gray-100 pt-4 mt-4">
                <div className="mb-4">
                  <label
                    htmlFor="original-text"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    原文内容
                  </label>
                  <textarea
                    id="original-text"
                    rows={10}
                    value={originalText}
                    onChange={(e) => setOriginalText(e.target.value)}
                    className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    placeholder="粘贴完形填空原文，用双下划线加数字表示填空位置，例如: In 1924 America's National Research Council sent two engineers to __1__ a series of..."
                  />
                </div>

                <div className="flex justify-end">
                  <button
                    onClick={saveOriginalText}
                    disabled={!originalText.trim() || loading}
                    className={`px-4 py-2 rounded-md ${
                      !originalText.trim() || loading
                        ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                        : "bg-blue-600 text-white hover:bg-blue-700"
                    }`}
                  >
                    {loading ? "保存中..." : "保存原文"}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 题目管理部分 */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="p-6 border-b border-gray-100">
            <h2 className="text-xl font-bold text-gray-800">题目管理</h2>
            <p className="text-sm text-gray-500 mt-1">
              共 {questions.length} 个题目
            </p>
          </div>

          {questions.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      题号
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      选项
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-10%">
                      正确答案
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4">
                      解析
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {[...questions]
                    .sort((a, b) => a.question_number - b.question_number)
                    .map((question) => (
                      <tr key={question.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {question.question_number}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900">
                            <div>A. {question.options.A.text}</div>
                            <div>B. {question.options.B.text}</div>
                            <div>C. {question.options.C.text}</div>
                            <div>D. {question.options.D.text}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                            {question.correct_answer || "未设置"}
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900 max-w-xs truncate">
                            {question.explanation ? (
                              <span>
                                {question.explanation.substring(0, 50)}...
                              </span>
                            ) : (
                              <span className="text-gray-500">无解析</span>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => startEdit(question)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            编辑
                          </button>
                        </td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="p-6 text-center">
              <p className="text-gray-500">暂无题目，请批量导入题目</p>
            </div>
          )}

          {/* 仅在没有题目时显示导入题目表单 */}
          {questions.length === 0 && (
            <div className="p-6 border-t border-gray-100">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  导入格式说明
                </label>
                <div className="p-3 bg-gray-50 rounded-lg text-sm text-gray-600">
                  <p>每行一个题目，格式如下：</p>
                  <pre className="mt-2">
                    题号. A. 选项A B. 选项B C. 选项C D. 选项D
                  </pre>
                  <p className="mt-2">示例：</p>
                  <pre className="mt-1">
                    1. A. affected B. achieved C. extracted D. restored
                  </pre>
                  <p className="mt-2">支持多词选项：</p>
                  <pre className="mt-1">
                    20. A. by all means B. at all costs C. in a word D. as a
                    result
                  </pre>
                  <p className="mt-2 text-yellow-600">
                    注意：必须导入完整的1-20题，缺少任何一题都会导入失败
                  </p>
                </div>
              </div>

              <div className="mb-4">
                <label
                  htmlFor="questions-import"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  题目信息
                </label>
                <textarea
                  id="questions-import"
                  rows={10}
                  value={questionsText}
                  onChange={(e) => setQuestionsText(e.target.value)}
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="粘贴题目信息，每行一个题目..."
                />
              </div>

              <div className="flex justify-end">
                <button
                  onClick={importQuestions}
                  disabled={!questionsText.trim() || importLoading}
                  className={`px-4 py-2 rounded-md ${
                    !questionsText.trim() || importLoading
                      ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                      : "bg-blue-600 text-white hover:bg-blue-700"
                  }`}
                >
                  {importLoading ? "导入中..." : "导入题目"}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 题目编辑模态框 */}
      {isEditing && editingQuestion && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-lg w-full max-w-3xl max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-100 flex justify-between items-center">
              <h2 className="text-xl font-bold text-gray-800">
                编辑题目 #{editingQuestion.question_number}
              </h2>
              <button
                onClick={cancelEdit}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            <div className="p-6 space-y-4">
              {/* 选项编辑 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-700">选项</h3>

                {["A", "B", "C", "D"].map((option) => (
                  <div
                    key={option}
                    className="grid grid-cols-6 gap-4 items-center"
                  >
                    <div className="col-span-1">
                      <label className="font-medium text-gray-700">
                        选项 {option}
                      </label>
                    </div>
                    <div className="col-span-5">
                      <input
                        type="text"
                        value={
                          editingQuestion.options[
                            option as "A" | "B" | "C" | "D"
                          ].text
                        }
                        onChange={(e) =>
                          updateEditingQuestion(
                            `option_${option}_text`,
                            e.target.value
                          )
                        }
                        className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                ))}
              </div>

              {/* 正确答案 */}
              <div className="space-y-2">
                <h3 className="text-lg font-medium text-gray-700">正确答案</h3>
                <div className="flex space-x-4">
                  {["A", "B", "C", "D"].map((option) => (
                    <label key={option} className="inline-flex items-center">
                      <input
                        type="radio"
                        name="correct_answer"
                        value={option}
                        checked={editingQuestion.correct_answer === option}
                        onChange={() =>
                          updateEditingQuestion("correct_answer", option)
                        }
                        className="form-radio h-5 w-5 text-blue-600"
                      />
                      <span className="ml-2 text-gray-700">{option}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* 解析 */}
              <div className="space-y-2">
                <h3 className="text-lg font-medium text-gray-700">解析</h3>
                <textarea
                  rows={5}
                  value={editingQuestion.explanation || ""}
                  onChange={(e) =>
                    updateEditingQuestion("explanation", e.target.value)
                  }
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="输入题目解析..."
                />
              </div>

              {/* 操作按钮 */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  onClick={cancelEdit}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
                >
                  取消
                </button>
                <button
                  onClick={saveEditedQuestion}
                  disabled={editLoading}
                  className={`px-4 py-2 rounded-lg ${
                    editLoading
                      ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                      : "bg-blue-600 text-white hover:bg-blue-700"
                  }`}
                >
                  {editLoading ? "保存中..." : "保存"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UseOfEnglishDetailPage;
