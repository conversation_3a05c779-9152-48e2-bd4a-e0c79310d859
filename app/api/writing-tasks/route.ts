import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { DeepSeek } from "@/app/services/deepseek";

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// GET方法：获取写作任务
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    // 如果提供了ID，则获取单个写作任务
    if (id) {
      const { data, error } = await supabase
        .from("papers")
        .select("*")
        .eq("id", id)
        .eq("section_type", "writing")
        .single();

      if (error) {
        throw error;
      }

      return NextResponse.json({
        success: true,
        data,
      });
    }

    // 否则获取所有写作任务列表
    const { data, error } = await supabase
      .from("papers")
      .select("*")
      .eq("section_type", "writing")
      .order("created_at", { ascending: false });

    if (error) {
      throw error;
    }

    return NextResponse.json({
      success: true,
      data,
    });
  } catch (error) {
    console.error("获取写作任务失败:", error);
    return NextResponse.json({ error: "获取写作任务失败" }, { status: 500 });
  }
}

// POST方法：创建写作任务
export async function POST(request: Request) {
  try {
    // 解析请求数据
    const formData = await request.formData();
    const year = formData.get("year") as string;
    const type = formData.get("type") as string;
    const taskType = formData.get("task_type") as string;
    const prompt = formData.get("content") as string;
    const imageUrl = formData.get("image_url") as string;
    const imageDescription = formData.get("image_description") as string;

    // 验证必填字段
    if (!year || !type || !prompt || !taskType) {
      return NextResponse.json({ error: "缺少必要字段" }, { status: 400 });
    }

    // 构建AI提示词
    const isSmallComposition = taskType === "small_composition";
    const systemPrompt = `你是一位经验丰富的考研英语写作老师。请根据以下作文题目创作一篇优秀的${isSmallComposition ? "小作文" : "大作文"}范文。

## 题目要求
${prompt}

${
  !isSmallComposition && imageDescription
    ? `## 题目图片描述
${imageDescription}`
    : ""
}

请确保写作内容：
1. 符合题目要求和字数限制
2. 结构清晰，语言准确流畅
3. 使用适当的连接词和过渡语
${isSmallComposition ? "4. 符合应用信函的格式规范" : "4. 合理运用高级词汇和句式"}

请以下面的JSON格式返回你的回答：
\`\`\`json
{
  "ai_reference": "这里是范文全文，使用Markdown格式...",
  "writing_analysis": {
    "writing_thoughts": "【请使用中文】这里是详细的写作思路分析，可以包含多行文本",
    "key_points": "【请使用中文】这里是重点难点分析，可以包含多行文本",
    "high_score_elements": ["【请使用中文】这里是高分词汇和句式示例1", "示例2", "示例3", "可以有多个示例"],
    "common_errors": ["【请使用中文】这里是常见写作误区提醒1", "提醒2", "提醒3", "可以有多个提醒"],
    "scoring_tips": ["【请使用中文】这里是得分要点提示1", "提示2", "提示3", "可以有多个提示"]
  }
}
\`\`\`

请注意:
1. 确保返回的是有效的JSON格式
2. writing_thoughts和key_points应为文本字符串(可包含换行)
3. high_score_elements、common_errors和scoring_tips必须是字符串数组
4. 请严格按照上述JSON结构返回，这对于前端正确显示至关重要
5. 【重要】writing_analysis部分必须使用中文输出，不要使用英文`;

    // 调用DeepSeek API生成写作范文和分析
    // 注意：这里我们只在实际使用时创建实例，减少不必要的连接
    const deepseek = new DeepSeek(process.env.DEEPSEEK_API_KEY);
    const aiResult = await deepseek.chatCompletions({
      model: "deepseek-chat",
      messages: [
        {
          role: "system",
          content: systemPrompt,
        },
      ],
      temperature: 0.7,
      max_tokens: 4000,
    });
    console.log(systemPrompt);
    // 解析AI回复
    const aiReplyContent = aiResult.choices[0].message.content;
    let jsonData;

    try {
      // 提取JSON部分
      const jsonMatch = aiReplyContent.match(/```json\n([\s\S]*?)\n```/);
      if (jsonMatch && jsonMatch[1]) {
        jsonData = JSON.parse(jsonMatch[1]);
      } else {
        // 尝试直接解析整个回复
        jsonData = JSON.parse(aiReplyContent);
      }

      // 验证JSON数据结构
      if (!jsonData.ai_reference || !jsonData.writing_analysis) {
        throw new Error("AI返回的数据格式不正确");
      }
    } catch (error) {
      console.error("解析AI回复失败:", error);
      console.log("原始AI回复:", aiReplyContent);
      jsonData = {
        ai_reference: "解析失败，请重试",
        writing_analysis: {
          writing_thoughts: "解析失败，请重试",
          key_points: "解析失败，请重试",
          high_score_elements: ["解析失败，请重试"],
          common_errors: ["解析失败，请重试"],
          scoring_tips: ["解析失败，请重试"],
        },
      };
    }

    // 准备任务数据
    const taskData = {
      year,
      type,
      section_type: "writing",
      task_type: taskType,
      content: prompt,
      ai_reference: jsonData.ai_reference,
      writing_analysis: JSON.stringify(jsonData.writing_analysis),
      is_public: true,
      image_url: imageUrl || null,
      image_description: imageDescription || null,
    };

    // 将数据保存到Supabase
    const { data, error } = await supabase
      .from("papers")
      .insert(taskData)
      .select()
      .single();

    if (error) {
      console.error("Supabase 错误详情:", error);
      throw error;
    }

    return NextResponse.json({
      success: true,
      message: "创建写作任务成功",
      data,
    });
  } catch (error) {
    console.error("创建写作任务失败:", error);
    return NextResponse.json({ error: "创建写作任务失败" }, { status: 500 });
  }
}

// PUT方法：更新写作任务
export async function PUT(request: Request) {
  try {
    const { id, ...updateData } = await request.json();

    if (!id) {
      return NextResponse.json({ error: "缺少ID字段" }, { status: 400 });
    }

    // 使用Supabase更新数据
    const { data, error } = await supabase
      .from("papers")
      .update(updateData)
      .eq("id", id)
      .eq("section_type", "writing")
      .select()
      .single();

    if (error) {
      throw error;
    }

    return NextResponse.json({
      success: true,
      message: "写作任务更新成功",
      data,
    });
  } catch (error) {
    console.error("更新写作任务失败:", error);
    return NextResponse.json({ error: "更新写作任务失败" }, { status: 500 });
  }
}
