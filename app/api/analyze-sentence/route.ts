import { NextResponse } from "next/server";
import { DeepSeek } from "@/app/services/deepseek";

// 简化接口，只返回Markdown格式的解析内容
interface AnalysisResult {
  explanation: string; // Markdown格式的解析内容
}

export async function POST(request: Request) {
  try {
    const { content } = await request.json();

    if (!content) {
      return NextResponse.json(
        { error: "Content is required" },
        { status: 400 }
      );
    }

    const prompt = `
你是一位资深的考研英语考试分析专家，拥有丰富的考研英语教学和试题分析经验。请对以下英语句子进行全方位的专业分析，并以Markdown格式输出分析结果。

分析要求：
1. 翻译部分：
   - 提供准确、地道的中文翻译
   - 标注翻译难点和技巧
   - 对于难点词组提供备选翻译

2. 词汇和短语部分：
   - 列出句子中的重要词汇和短语，包括词性、中文释义
   - 提供常见搭配和用法示例
   - 特别标注考研高频词汇
   - 提供近义词辨析（如适用）
   - 给出记忆技巧或词源分析（如有助于记忆）

3. 句子结构部分：
   - 分析句子类型（简单句/复合句/并列句等）
   - 标注句子各个成分及其功能
   - 分析从句类型和作用（如适用）
   - 指出特殊句式（倒装/强调/省略等）

4. 语法分析部分：
   - 详细分析使用的时态、语态、语气等
   - 解释特殊的语法现象或难点
   - 分析长难句的层次结构
   - 提供语法学习要点

5. 知识点总结部分：
   - 总结该句包含的考研考点
   - 提供相似练习句及其翻译
   - 给出解题思路和应试技巧
   - 指出易错点和注意事项

返回格式要求：
请使用规范的Markdown格式输出分析内容，包括各级标题、列表、加粗、引用等元素，使内容层次分明、易于阅读。

以下是一个示例输出（仅供参考格式）：

## 句子翻译

> 原句：The rapid advancement of artificial intelligence has transformed various sectors of society, bringing both opportunities and challenges that warrant careful consideration.

**翻译**：人工智能的快速发展已经改变了社会的各个领域，带来了机遇与挑战，值得仔细考虑。

**翻译要点**：
- "rapid advancement" 译为"快速发展"，表示技术进步的速度
- "various sectors" 译为"各个领域"，表示影响范围广泛
- "warrant careful consideration" 译为"值得仔细考虑"，体现了审慎态度

## 词汇与短语

**重要词汇**：
- advancement (n.) 发展，进步 - 常与technological, scientific等词搭配
- transform (v.) 改变，转变 - 强调彻底的、本质的变化
- various (adj.) 各种各样的 - 常用于指多样性
- sector (n.) 部门，领域 - formal，多用于学术或正式场合
- warrant (v.) 值得，保证 - 正式用语，表示合理性或必要性

**近义词辨析**：
- advancement vs. progress: advancement更强调主动推进，progress侧重过程中的发展

## 句子结构分析

这是一个**复合句**，包含主句和分词短语作状语：
- 主句：The rapid advancement of artificial intelligence has transformed various sectors of society
- 现在分词短语：bringing both opportunities and challenges that warrant careful consideration（作结果状语）
  - 其中"that warrant careful consideration"是定语从句，修饰"opportunities and challenges"

## 语法分析

1. **时态**：使用现在完成时"has transformed"，表示过去发生且影响持续到现在的动作
2. **非谓语动词**：使用现在分词"bringing"构成分词短语，表示主句动作的结果
3. **定语从句**：使用"that warrant..."构成定语从句，修饰前面的名词

## 知识点总结

**考点**：
1. 复合句结构的理解与翻译
2. 非谓语动词的使用（分词短语作状语）
3. 学术性词汇的掌握与应用

**练习**：
> Although the tension in the office may have eased since the recent conflict, the atmosphere will be tense at the staff meeting this week as managers discuss how to improve workplace communication.

**翻译**：尽管自从最近的冲突以来，办公室的紧张气氛可能已经缓解，但本周的员工会议将会非常紧张，因为经理们将会讨论如何改善工作场所的沟通。

**应试技巧**：
- 遇到长难句时，先找出主干部分，再逐步分析修饰成分
- 翻译时可适当调整语序，使译文更符合中文表达习惯

待分析句子：
${content}

请按照上述Markdown格式提供详细的句子分析，确保输出的是有效的Markdown文本，分析内容要专业、全面且有深度。只需要返回Markdown内容，不要包含任何其他说明文字或JSON结构。
`;

    const deepseek = new DeepSeek();
    const response = await deepseek.chat({
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
    });

    // 直接使用AI返回的Markdown内容，无需额外解析
    const explanation = response.choices[0].message.content.trim();

    // 检查内容是否为空
    if (!explanation) {
      throw new Error("Empty analysis result");
    }

    return NextResponse.json({ explanation });
  } catch (error: unknown) {
    console.error("Analyze sentence failed:", error);
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    return NextResponse.json(
      { error: "Failed to analyze sentence", details: errorMessage },
      { status: 500 }
    );
  }
}
