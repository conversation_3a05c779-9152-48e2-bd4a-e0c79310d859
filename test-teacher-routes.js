// 测试教资路由生成
function getTeacherResourceDetailUrl(resource, type, teacherSelectionPath) {
  // 对所有路径段进行URL编码，并添加资源ID
  const encodedPath = teacherSelectionPath.map(segment => encodeURIComponent(segment)).join('/');
  const encodedId = encodeURIComponent(resource.id);
  return `/learning-resources/teacher/${type}/${encodedPath}/${encodedId}`;
}

// 测试用例
const testResource = {
  id: "test-resource-123",
  name: "测试资源"
};

// 测试科目一和科目二（2层路径）
console.log("科目一路径:", getTeacherResourceDetailUrl(
  testResource, 
  "teacher_secondary", 
  ["科目一（综合素质）", "历年真题"]
));

console.log("科目二路径:", getTeacherResourceDetailUrl(
  testResource, 
  "teacher_secondary", 
  ["科目二（教育知识与能力）", "答案解析"]
));

// 测试科目三（4层路径）
console.log("科目三初中政治真题:", getTeacherResourceDetailUrl(
  testResource, 
  "teacher_secondary", 
  ["科目三", "初中", "政治", "历年真题"]
));

console.log("科目三高中英语解析:", getTeacherResourceDetailUrl(
  testResource, 
  "teacher_secondary", 
  ["科目三", "高中", "英语", "答案解析"]
));
