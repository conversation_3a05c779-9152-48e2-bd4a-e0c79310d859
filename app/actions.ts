"use server";

import { encodedRedirect } from "@/utils/utils";
import { createClient } from "@/utils/supabase/server";
import { headers } from "next/headers";
import { redirect } from "next/navigation";

export const signUpAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const password = formData.get("password")?.toString();
  const supabase = await createClient();

  // 获取请求的原始 origin
  const requestOrigin = (await headers()).get("origin");

  // 优先使用环境变量中的网站 URL，如果没有则使用请求 origin
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || requestOrigin;

  if (!email || !password) {
    return encodedRedirect("error", "/sign-up", "邮箱和密码不能为空");
  }

  // 使用 OTP 注册/登录，而不是魔法链接
  const { error } = await supabase.auth.signInWithOtp({
    email,
    options: {
      shouldCreateUser: true, // 如果用户不存在，则创建新用户
      data: {
        password, // 将密码存储在用户元数据中，待验证后设置
      },
    },
  });

  if (error) {
    console.error(error.code + " " + error.message);

    // 根据错误代码返回不同的错误信息
    let errorMessage = "注册失败";

    if (error.message.includes("already registered")) {
      errorMessage = "该邮箱已注册，请直接登录或使用其他邮箱";
    } else if (error.message.includes("Invalid email")) {
      errorMessage = "邮箱格式不正确，请检查";
    } else if (error.message.includes("rate limited")) {
      errorMessage = "操作频率过高，请稍后再试";
    } else if (error.message.includes("User already registered")) {
      errorMessage = "用户已注册，请直接登录";
    } else if (error.message.includes("network")) {
      errorMessage = "网络连接错误，请检查您的网络连接";
    }

    return encodedRedirect("error", "/sign-up", errorMessage);
  } else {
    // 成功发送验证码后，重定向到验证页面
    return redirect(
      `/verify-otp?email=${encodeURIComponent(email)}&password=${encodeURIComponent(password)}`
    );
  }
};

export const verifyOtpAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const token = formData.get("token")?.toString();
  const password = formData.get("password")?.toString();
  const supabase = await createClient();

  if (!email || !token) {
    return encodedRedirect("error", "/verify-otp", "邮箱和验证码不能为空");
  }

  // 验证 OTP
  const { data, error } = await supabase.auth.verifyOtp({
    email,
    token,
    type: "email",
  });

  if (error) {
    console.error(error);

    // 根据错误代码返回不同的错误信息
    let errorMessage = "验证码验证失败";

    if (error.message.includes("Invalid otp")) {
      errorMessage = "验证码无效或已过期，请重新获取";
    } else if (error.message.includes("rate limited")) {
      errorMessage = "操作频率过高，请稍后再试";
    } else if (error.message.includes("network")) {
      errorMessage = "网络连接错误，请检查您的网络连接";
    }

    return encodedRedirect(
      "error",
      `/verify-otp?email=${encodeURIComponent(email)}`,
      errorMessage
    );
  }

  // 如果有密码参数（注册流程），则设置用户密码
  if (password && data.user) {
    const { error: updateError } = await supabase.auth.updateUser({
      password: password,
    });

    if (updateError) {
      console.error(updateError);
      return encodedRedirect(
        "error",
        "/sign-in",
        "账户已验证，但密码设置失败，请使用忘记密码重置"
      );
    }

    // 在设置密码成功后，直接使用密码登录
    const { error: signInError } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (signInError) {
      console.error(signInError);
      return encodedRedirect(
        "error",
        "/sign-in",
        "账户已验证，但自动登录失败，请手动登录"
      );
    }

    // 登录成功，直接跳转到首页
    return redirect("/?welcome=true");
  }

  // 如果没有密码参数（例如登录验证流程），则依然跳转到登录页
  return redirect("/sign-in?verified=true");
};

export const resendOtpAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const password = formData.get("password")?.toString();
  const supabase = await createClient();

  if (!email) {
    return encodedRedirect("error", "/verify-otp", "邮箱不能为空");
  }

  // 重新发送 OTP
  const { error } = await supabase.auth.signInWithOtp({
    email,
    options: {
      shouldCreateUser: true,
      data: password ? { password } : undefined,
    },
  });

  if (error) {
    console.error(error);

    // 根据错误代码返回不同的错误信息
    let errorMessage = "验证码发送失败";

    if (error.message.includes("rate limited")) {
      errorMessage = "操作频率过高，请稍后再试";
    } else if (error.message.includes("Invalid email")) {
      errorMessage = "邮箱格式不正确，请检查";
    } else if (error.message.includes("network")) {
      errorMessage = "网络连接错误，请检查您的网络连接";
    }

    return encodedRedirect(
      "error",
      `/verify-otp?email=${encodeURIComponent(email)}${password ? `&password=${encodeURIComponent(password)}` : ""}`,
      errorMessage
    );
  }

  return encodedRedirect(
    "success",
    `/verify-otp?email=${encodeURIComponent(email)}${password ? `&password=${encodeURIComponent(password)}` : ""}`,
    "验证码已重新发送，请查看您的邮箱"
  );
};

export const signInAction = async (formData: FormData) => {
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;
  const redirectTo = formData.get("redirectTo") as string;
  const supabase = await createClient();

  const { error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    console.error(error);

    // 根据错误代码返回不同的错误信息
    let errorMessage = "登录失败";

    // 身份验证错误处理
    if (error.message.includes("Invalid login credentials")) {
      errorMessage = "邮箱或密码错误，请重试";
    } else if (error.message.includes("Email not confirmed")) {
      errorMessage = "邮箱未验证，请先验证您的邮箱";
    } else if (error.message.includes("Invalid email")) {
      errorMessage = "无效的邮箱格式";
    } else if (error.message.includes("rate limited")) {
      errorMessage = "登录尝试次数过多，请稍后再试";
    } else if (error.message.includes("User not found")) {
      errorMessage = "用户不存在，请先注册";
    } else if (error.message.includes("network")) {
      errorMessage = "网络连接错误，请检查您的网络连接";
    }

    return encodedRedirect("error", "/sign-in", errorMessage);
  }

  // 检查是否需要重定向到指定页面
  if (redirectTo) {
    return redirect(redirectTo);
  }

  return redirect("/");
};

export const forgotPasswordAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const supabase = await createClient();

  // 获取请求的原始 origin
  const requestOrigin = (await headers()).get("origin");

  // 优先使用环境变量中的网站 URL，如果没有则使用请求 origin
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || requestOrigin;

  const callbackUrl = formData.get("callbackUrl")?.toString();

  if (!email) {
    return encodedRedirect("error", "/forgot-password", "邮箱不能为空");
  }

  console.log(
    "忘记密码使用的重定向 URL:",
    `${siteUrl}/auth/callback?redirect_to=/protected/reset-password`
  );

  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${siteUrl}/auth/callback?redirect_to=/protected/reset-password`,
  });

  if (error) {
    console.error(error.message);

    // 根据错误代码返回不同的错误信息
    let errorMessage = "无法重置密码";

    if (error.message.includes("User not found")) {
      errorMessage = "找不到该邮箱对应的用户，请检查您的邮箱地址";
    } else if (error.message.includes("Invalid email")) {
      errorMessage = "邮箱格式不正确，请检查";
    } else if (error.message.includes("rate limited")) {
      errorMessage = "操作频率过高，请稍后再试";
    } else if (error.message.includes("network")) {
      errorMessage = "网络连接错误，请检查您的网络连接";
    }

    return encodedRedirect("error", "/forgot-password", errorMessage);
  }

  if (callbackUrl) {
    return redirect(callbackUrl);
  }

  return encodedRedirect(
    "success",
    "/forgot-password",
    "请查看您的邮箱获取重置密码的链接。"
  );
};

export const resetPasswordAction = async (formData: FormData) => {
  const supabase = await createClient();

  const password = formData.get("password") as string;
  const confirmPassword = formData.get("confirmPassword") as string;

  if (!password || !confirmPassword) {
    return encodedRedirect(
      "error",
      "/protected/reset-password",
      "密码和确认密码不能为空"
    );
  }

  if (password !== confirmPassword) {
    return encodedRedirect(
      "error",
      "/protected/reset-password",
      "两次输入的密码不一致"
    );
  }

  const { error } = await supabase.auth.updateUser({
    password: password,
  });

  if (error) {
    console.error(error.message);

    // 根据错误代码返回不同的错误信息
    let errorMessage = "密码更新失败";

    if (error.message.includes("weak password")) {
      errorMessage = "密码强度不足，请使用更复杂的密码";
    } else if (error.message.includes("Password should be")) {
      errorMessage = "密码不符合要求，密码长度至少为8个字符";
    } else if (error.message.includes("session expired")) {
      errorMessage = "会话已过期，请重新请求密码重置链接";
    } else if (error.message.includes("network")) {
      errorMessage = "网络连接错误，请检查您的网络连接";
    }

    return encodedRedirect("error", "/protected/reset-password", errorMessage);
  }

  return encodedRedirect("success", "/protected/reset-password", "密码已更新");
};

export const signOutAction = async () => {
  const supabase = await createClient();
  await supabase.auth.signOut();
  return redirect("/sign-in");
};
