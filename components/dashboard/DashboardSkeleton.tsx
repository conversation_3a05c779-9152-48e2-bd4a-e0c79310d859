import PurchaseHistorySkeleton from "./PurchaseHistorySkeleton";

export default function DashboardSkeleton() {
  return (
    <div className="max-w-4xl mx-auto">
      {/* 用户信息骨架图 */}
      <div className="mb-8 bg-white p-6 rounded-lg shadow-sm animate-pulse">
        <div className="flex flex-col md:flex-row justify-between items-start">
          <div className="w-full">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-5 bg-gray-200 rounded w-2/3 mb-3"></div>
            <div className="mt-4">
              <div className="h-5 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-5 bg-gray-200 rounded w-2/3 mb-2"></div>
            </div>
          </div>
          <div className="mt-4 md:mt-0">
            <div className="h-10 bg-gray-200 rounded w-24"></div>
          </div>
        </div>
      </div>

      {/* 购买历史骨架图 */}
      <div className="mb-8">
        <PurchaseHistorySkeleton />
      </div>
    </div>
  );
} 