import { createClient } from "@supabase/supabase-js";
import { NextResponse } from "next/server";

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// 资源接口定义
export interface Resource {
  id: string;
  name: string;
  size: number;
  url: string;
  created_at: string;
  type: string;
  category?: string; // 大学生创新创业项目分类
}

// 通过文件名获取文件类型
function getFileTypeFromName(fileName: string) {
  const extension = fileName.split(".").pop()?.toLowerCase();
  if (extension === "pdf") return "application/pdf";
  if (extension === "doc" || extension === "docx") return "application/msword";
  if (extension === "xls" || extension === "xlsx") return "application/excel";
  if (extension === "ppt" || extension === "pptx")
    return "application/powerpoint";
  return "application/octet-stream";
}

// 获取资源函数
async function getResources(path: string, category: string) {
  const { data, error } = await supabase.storage.from("mbdata").list(path, {
    sortBy: { column: "name", order: "asc" },
  });

  if (error) {
    console.error(`获取${path}资源失败:`, error);
    return [];
  }

  if (!data || data.length === 0) {
    return [];
  }

  return await Promise.all(
    data.map(async (file) => {
      const { data: urlData } = supabase.storage
        .from("mbdata")
        .getPublicUrl(`${path}/${file.name}`);

      return {
        id: file.id,
        name: file.name,
        size: file.metadata.size,
        url: urlData.publicUrl,
        created_at: file.created_at,
        type: file.metadata.mimetype || getFileTypeFromName(file.name),
        category: category,
      };
    })
  );
}

// GET请求处理函数 - 获取大创项目资源
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const resourceType = searchParams.get("type") || "recommended"; // 默认为精品案例

    let resources: Resource[] = [];

    // 根据请求类型获取不同文件夹下的资源
    if (resourceType === "recommended") {
      const recommendedResources = await getResources(
        "大学生创新创业/01.2.3 精品案例(推荐）",
        "精品案例(推荐)"
      );
      resources = [...resources, ...recommendedResources];
    }

    if (resourceType === "app") {
      const appResources = await getResources(
        "大学生创新创业/04 APP移动应用",
        "APP移动应用"
      );
      resources = [...resources, ...appResources];
    }

    if (resourceType === "ecommerce") {
      const ecommerceResources = await getResources(
        "大学生创新创业/05 电子商务",
        "电子商务"
      );
      resources = [...resources, ...ecommerceResources];
    }

    if (resourceType === "internet") {
      const internetResources = await getResources(
        "大学生创新创业/06 互联网项目",
        "互联网项目"
      );
      resources = [...resources, ...internetResources];
    }

    if (resourceType === "tech") {
      const techResources = await getResources(
        "大学生创新创业/07 科技项目",
        "科技项目"
      );
      resources = [...resources, ...techResources];
    }

    if (resourceType === "fashion") {
      const fashionResources = await getResources(
        "大学生创新创业/08 服装服饰箱包",
        "服装服饰箱包"
      );
      resources = [...resources, ...fashionResources];
    }

    if (resourceType === "health") {
      const healthResources = await getResources(
        "大学生创新创业/09 大健康项目(医疗、医药、保健）",
        "大健康项目(医疗、医药、保健）"
      );
      resources = [...resources, ...healthResources];
    }

    if (resourceType === "ai") {
      const aiResources = await getResources(
        "大学生创新创业/10 人工智能",
        "人工智能"
      );
      resources = [...resources, ...aiResources];
    }

    if (resourceType === "culture") {
      const cultureResources = await getResources(
        "大学生创新创业/11 文化传媒",
        "文化传媒"
      );
      resources = [...resources, ...cultureResources];
    }

    if (resourceType === "food") {
      const foodResources = await getResources(
        "大学生创新创业/12 食品饮料酒类",
        "食品饮料酒类"
      );
      resources = [...resources, ...foodResources];
    }

    if (resourceType === "homestay") {
      const homestayResources = await getResources(
        "大学生创新创业/13 民宿项目",
        "民宿项目"
      );
      resources = [...resources, ...homestayResources];
    }

    if (resourceType === "cafe") {
      const cafeResources = await getResources(
        "大学生创新创业/14 奶茶咖啡甜品蛋糕面包",
        "奶茶咖啡甜品蛋糕面包"
      );
      resources = [...resources, ...cafeResources];
    }

    if (resourceType === "hotel") {
      const hotelResources = await getResources(
        "大学生创新创业/15 酒店旅游",
        "酒店旅游"
      );
      resources = [...resources, ...hotelResources];
    }

    if (resourceType === "housekeeping") {
      const housekeepingResources = await getResources(
        "大学生创新创业/16 家政服务",
        "家政服务"
      );
      resources = [...resources, ...housekeepingResources];
    }

    if (resourceType === "logistics") {
      const logisticsResources = await getResources(
        "大学生创新创业/17 仓储物流",
        "仓储物流"
      );
      resources = [...resources, ...logisticsResources];
    }

    if (resourceType === "food_service") {
      const foodServiceResources = await getResources(
        "大学生创新创业/18 餐饮美食",
        "餐饮美食"
      );
      resources = [...resources, ...foodServiceResources];
    }

    if (resourceType === "supermarket") {
      const supermarketResources = await getResources(
        "大学生创新创业/19 超市便利水果店",
        "超市便利水果店"
      );
      resources = [...resources, ...supermarketResources];
    }

    if (resourceType === "pets") {
      const petsResources = await getResources(
        "大学生创新创业/20 宠物项目",
        "宠物项目"
      );
      resources = [...resources, ...petsResources];
    }

    if (resourceType === "toys") {
      const toysResources = await getResources(
        "大学生创新创业/21 儿童玩具",
        "儿童玩具"
      );
      resources = [...resources, ...toysResources];
    }

    if (resourceType === "fitness") {
      const fitnessResources = await getResources(
        "大学生创新创业/22 健身运动体育用品",
        "健身运动体育用品"
      );
      resources = [...resources, ...fitnessResources];
    }

    if (resourceType === "beauty") {
      const beautyResources = await getResources(
        "大学生创新创业/23 美容美发化妆品美甲",
        "美容美发化妆品美甲"
      );
      resources = [...resources, ...beautyResources];
    }

    if (resourceType === "agriculture") {
      const agricultureResources = await getResources(
        "大学生创新创业/24 农业项目",
        "农业项目"
      );
      resources = [...resources, ...agricultureResources];
    }

    if (resourceType === "automotive") {
      const automotiveResources = await getResources(
        "大学生创新创业/25 汽车类",
        "汽车类"
      );
      resources = [...resources, ...automotiveResources];
    }

    if (resourceType === "furniture") {
      const furnitureResources = await getResources(
        "大学生创新创业/26 家具家纺",
        "家具家纺"
      );
      resources = [...resources, ...furnitureResources];
    }

    if (resourceType === "elderly") {
      const elderlyResources = await getResources(
        "大学生创新创业/27 养老项目",
        "养老项目"
      );
      resources = [...resources, ...elderlyResources];
    }

    if (resourceType === "outdoor") {
      const outdoorResources = await getResources(
        "大学生创新创业/28 户外项目",
        "户外项目"
      );
      resources = [...resources, ...outdoorResources];
    }

    if (resourceType === "new_energy") {
      const newEnergyResources = await getResources(
        "大学生创新创业/29 新能源项目",
        "新能源项目"
      );
      resources = [...resources, ...newEnergyResources];
    }

    if (resourceType === "photography") {
      const photographyResources = await getResources(
        "大学生创新创业/30 摄影摄像婚庆",
        "摄影摄像婚庆"
      );
      resources = [...resources, ...photographyResources];
    }

    if (resourceType === "advertising") {
      const advertisingResources = await getResources(
        "大学生创新创业/31 广告营销策划",
        "广告营销策划"
      );
      resources = [...resources, ...advertisingResources];
    }

    if (resourceType === "education") {
      const educationResources = await getResources(
        "大学生创新创业/32 教育培训",
        "教育培训"
      );
      resources = [...resources, ...educationResources];
    }

    if (resourceType === "mobile_comm") {
      const mobileCommResources = await getResources(
        "大学生创新创业/33 移动通信",
        "移动通信"
      );
      resources = [...resources, ...mobileCommResources];
    }

    if (resourceType === "environment") {
      const environmentResources = await getResources(
        "大学生创新创业/34 环保项目",
        "环保项目"
      );
      resources = [...resources, ...environmentResources];
    }

    if (resourceType === "charity") {
      const charityResources = await getResources(
        "大学生创新创业/35 公益项目",
        "公益项目"
      );
      resources = [...resources, ...charityResources];
    }

    if (resourceType === "construction") {
      const constructionResources = await getResources(
        "大学生创新创业/36 工程建筑",
        "工程建筑"
      );
      resources = [...resources, ...constructionResources];
    }

    if (resourceType === "trade") {
      const tradeResources = await getResources(
        "大学生创新创业/37 进出口贸易",
        "进出口贸易"
      );
      resources = [...resources, ...tradeResources];
    }

    if (resourceType === "entertainment") {
      const entertainmentResources = await getResources(
        "大学生创新创业/38 娱乐休闲",
        "娱乐休闲"
      );
      resources = [...resources, ...entertainmentResources];
    }

    if (resourceType === "hr") {
      const hrResources = await getResources(
        "大学生创新创业/39 人力资源项目",
        "人力资源项目"
      );
      resources = [...resources, ...hrResources];
    }

    if (resourceType === "fmcg") {
      const fmcgResources = await getResources(
        "大学生创新创业/40 快消品项目",
        "快消品项目"
      );
      resources = [...resources, ...fmcgResources];
    }

    if (resourceType === "software") {
      const softwareResources = await getResources(
        "大学生创新创业/41 软件开发",
        "软件开发"
      );
      resources = [...resources, ...softwareResources];
    }

    if (resourceType === "electronics") {
      const electronicsResources = await getResources(
        "大学生创新创业/42 电子数码",
        "电子数码"
      );
      resources = [...resources, ...electronicsResources];
    }

    if (resourceType === "design") {
      const designResources = await getResources(
        "大学生创新创业/43 方案设计",
        "方案设计"
      );
      resources = [...resources, ...designResources];
    }

    if (resourceType === "kindergarten") {
      const kindergartenResources = await getResources(
        "大学生创新创业/44 幼儿园项目",
        "幼儿园项目"
      );
      resources = [...resources, ...kindergartenResources];
    }

    if (resourceType === "home_building") {
      const homeBuildingResources = await getResources(
        "大学生创新创业/45 家居建材",
        "家居建材"
      );
      resources = [...resources, ...homeBuildingResources];
    }

    if (resourceType === "mother_baby") {
      const motherBabyResources = await getResources(
        "大学生创新创业/46 母婴用品",
        "母婴用品"
      );
      resources = [...resources, ...motherBabyResources];
    }

    if (resourceType === "iot") {
      const iotResources = await getResources(
        "大学生创新创业/47 物联网项目",
        "物联网项目"
      );
      resources = [...resources, ...iotResources];
    }

    if (resourceType === "sharing") {
      const sharingResources = await getResources(
        "大学生创新创业/48 共享经济",
        "共享经济"
      );
      resources = [...resources, ...sharingResources];
    }

    if (resourceType === "consulting") {
      const consultingResources = await getResources(
        "大学生创新创业/49 管理咨询",
        "管理咨询"
      );
      resources = [...resources, ...consultingResources];
    }

    if (resourceType === "machinery") {
      const machineryResources = await getResources(
        "大学生创新创业/50 机械制造业",
        "机械制造业"
      );
      resources = [...resources, ...machineryResources];
    }

    if (resourceType === "finance") {
      const financeResources = await getResources(
        "大学生创新创业/51 金融行业",
        "金融行业"
      );
      resources = [...resources, ...financeResources];
    }

    if (resourceType === "big_data") {
      const bigDataResources = await getResources(
        "大学生创新创业/52 大数据",
        "大数据"
      );
      resources = [...resources, ...bigDataResources];
    }

    if (resourceType === "printing") {
      const printingResources = await getResources(
        "大学生创新创业/53 打字复印",
        "打字复印"
      );
      resources = [...resources, ...printingResources];
    }

    if (resourceType === "umbrella") {
      const umbrellaResources = await getResources(
        "大学生创新创业/54 雨伞类",
        "雨伞类"
      );
      resources = [...resources, ...umbrellaResources];
    }

    if (resourceType === "jewelry") {
      const jewelryResources = await getResources(
        "大学生创新创业/55 珠宝行业",
        "珠宝行业"
      );
      resources = [...resources, ...jewelryResources];
    }

    if (resourceType === "livestream") {
      const livestreamResources = await getResources(
        "大学生创新创业/56 直播和电竞",
        "直播和电竞"
      );
      resources = [...resources, ...livestreamResources];
    }

    if (resourceType === "sanitary") {
      const sanitaryResources = await getResources(
        "大学生创新创业/57 卫生用品",
        "卫生用品"
      );
      resources = [...resources, ...sanitaryResources];
    }

    if (resourceType === "watches") {
      const watchesResources = await getResources(
        "大学生创新创业/58 手表项目",
        "手表项目"
      );
      resources = [...resources, ...watchesResources];
    }

    if (resourceType === "flowers") {
      const flowersResources = await getResources(
        "大学生创新创业/59 花卉鲜花",
        "花卉鲜花"
      );
      resources = [...resources, ...flowersResources];
    }

    if (resourceType === "legal") {
      const legalResources = await getResources(
        "大学生创新创业/60 法律服务",
        "法律服务"
      );
      resources = [...resources, ...legalResources];
    }

    if (resourceType === "tea") {
      const teaResources = await getResources(
        "大学生创新创业/61 茶叶茶馆",
        "茶叶茶馆"
      );
      resources = [...resources, ...teaResources];
    }

    if (resourceType === "campus") {
      const campusResources = await getResources(
        "大学生创新创业/62 校园项目",
        "校园项目"
      );
      resources = [...resources, ...campusResources];
    }

    if (resourceType === "transport") {
      const transportResources = await getResources(
        "大学生创新创业/63 运输工具",
        "运输工具"
      );
      resources = [...resources, ...transportResources];
    }

    if (resourceType === "media") {
      const mediaResources = await getResources(
        "大学生创新创业/64 自媒体",
        "自媒体"
      );
      resources = [...resources, ...mediaResources];
    }

    if (resourceType === "pottery") {
      const potteryResources = await getResources(
        "大学生创新创业/65 陶瓷陶艺",
        "陶瓷陶艺"
      );
      resources = [...resources, ...potteryResources];
    }

    if (resourceType === "stationery") {
      const stationeryResources = await getResources(
        "大学生创新创业/66 文具类",
        "文具类"
      );
      resources = [...resources, ...stationeryResources];
    }

    if (resourceType === "bookstore") {
      const bookstoreResources = await getResources(
        "大学生创新创业/67 书屋书店",
        "书屋书店"
      );
      resources = [...resources, ...bookstoreResources];
    }

    if (resourceType === "gifts") {
      const giftsResources = await getResources(
        "大学生创新创业/68 礼品饰品类",
        "礼品饰品类"
      );
      resources = [...resources, ...giftsResources];
    }

    if (resourceType === "applications") {
      const applicationsResources = await getResources(
        "大学生创新创业/创新创业相关申请书",
        "创新创业相关申请书"
      );
      resources = [...resources, ...applicationsResources];
    }

    // 添加缓存控制头，防止客户端过度缓存
    return NextResponse.json(
      { resources },
      {
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      }
    );
  } catch (error) {
    console.error("获取大学生创新创业资源出错:", error);
    return NextResponse.json({ error: "获取资源出错" }, { status: 500 });
  }
}
