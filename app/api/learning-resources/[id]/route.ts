import { createClient } from "@supabase/supabase-js";
import { NextResponse } from "next/server";
import {
  RESOURCE_PATHS,
  TYPE_CATEGORY_MAP,
  Resource,
  getFileTypeFromName,
  NESTED_TEACHER_RESOURCE_PATHS,
  TEACHER_RESOURCE_CONFIGS,
} from "@/app/lib/learning-resources-config";

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// 根据ID直接查找资源（在所有可能的路径中搜索）
async function findResourceById(
  resourceId: string,
  type: string,
  category: string
): Promise<Resource | null> {
  // 获取对应的存储路径配置
  const typeConfig = RESOURCE_PATHS[type];
  if (!typeConfig) {
    return null;
  }

  // 遍历该类型下的所有section路径
  for (const [sectionName, path] of Object.entries(typeConfig)) {
    try {
      // 获取该路径下的所有文件
      const { data, error } = await supabase.storage.from("mbdata").list(path, {
        sortBy: { column: "name", order: "desc" },
      });

      if (error || !data) {
        console.error(`获取${path}资源失败:`, error);
        continue; // 继续查找下一个路径
      }

      // 查找指定ID的文件
      const file = data.find((f) => f.id === resourceId);
      if (file) {
        // 找到了文件，获取公共URL并返回
        const { data: urlData } = supabase.storage
          .from("mbdata")
          .getPublicUrl(`${path}/${file.name}`);

        return {
          id: file.id,
          name: file.name,
          size: file.metadata.size,
          url: urlData.publicUrl,
          created_at: file.created_at,
          type: file.metadata.mimetype || getFileTypeFromName(file.name),
          category: TYPE_CATEGORY_MAP[type] || category,
          section: sectionName,
        };
      }
    } catch (error) {
      console.error(`在路径${path}中查找资源${resourceId}失败:`, error);
      continue; // 继续查找下一个路径
    }
  }

  // 在所有路径中都没有找到该资源
  return null;
}

// 根据嵌套路径查找教资资源
async function findTeacherResourceById(
  resourceId: string,
  type: string,
  nestedPath: string[]
): Promise<Resource | null> {
  // 获取教资资源配置
  const teacherConfig = TEACHER_RESOURCE_CONFIGS.find(
    (config) => config.id === type
  );
  if (!teacherConfig?.nestedSections) {
    return null;
  }

  // 根据嵌套路径获取存储路径
  let current: any = teacherConfig.nestedSections;
  for (const key of nestedPath) {
    if (current && typeof current === "object" && key in current) {
      current = current[key];
    } else {
      return null;
    }
  }

  // current 现在应该是一个路径字符串
  if (typeof current !== "string") {
    return null;
  }

  try {
    // 获取该路径下的所有文件
    const { data, error } = await supabase.storage
      .from("mbdata")
      .list(current, {
        sortBy: { column: "name", order: "desc" },
      });

    if (error || !data) {
      console.error(`获取教资资源${current}失败:`, error);
      return null;
    }

    // 查找指定ID的文件
    const file = data.find((f) => f.id === resourceId);
    if (file) {
      // 找到了文件，获取公共URL并返回
      const { data: urlData } = supabase.storage
        .from("mbdata")
        .getPublicUrl(`${current}/${file.name}`);

      return {
        id: file.id,
        name: file.name,
        size: file.metadata.size,
        url: urlData.publicUrl,
        created_at: file.created_at,
        type: file.metadata.mimetype || getFileTypeFromName(file.name),
        category: nestedPath[0] || "",
        section: nestedPath[1] || "",
      };
    }
  } catch (error) {
    console.error(`在教资路径${current}中查找资源${resourceId}失败:`, error);
  }

  return null;
}

// GET请求处理函数 - 根据ID获取单个资源
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get("type");
    const category = searchParams.get("category");
    const nestedPath = searchParams.get("nestedPath");

    if (!type) {
      return NextResponse.json(
        { error: "缺少必要参数: type" },
        { status: 400 }
      );
    }

    const resourceId = params.id;
    let resource: Resource | null = null;

    // 检查是否是教资资源类型
    const isTeacherResource = TEACHER_RESOURCE_CONFIGS.some(
      (config) => config.id === type
    );

    if (isTeacherResource && nestedPath) {
      // 处理教资资源
      const pathArray = nestedPath.split("|").map(decodeURIComponent);
      resource = await findTeacherResourceById(resourceId, type, pathArray);
    } else if (category) {
      // 处理普通资源
      resource = await findResourceById(
        resourceId,
        type,
        decodeURIComponent(category)
      );
    } else {
      return NextResponse.json(
        { error: "缺少必要参数: category 或 nestedPath" },
        { status: 400 }
      );
    }

    if (!resource) {
      return NextResponse.json({ error: "资源不存在" }, { status: 404 });
    }

    return NextResponse.json(
      { resource },
      {
        headers: {
          "Cache-Control": "public, max-age=3600", // 缓存1小时
        },
      }
    );
  } catch (error) {
    console.error("获取资源详情出错:", error);
    return NextResponse.json({ error: "获取资源失败" }, { status: 500 });
  }
}
