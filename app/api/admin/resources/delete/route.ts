import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// DELETE请求处理函数 - 删除文件
export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    const { fileName, path } = body;

    if (!fileName) {
      return NextResponse.json({ error: "文件名不能为空" }, { status: 400 });
    }

    // 构建文件路径
    const filePath = path ? `${path}/${fileName}` : fileName;

    // 从Supabase Storage删除文件
    const { data, error } = await supabase.storage
      .from("mbdata")
      .remove([filePath]);

    if (error) {
      console.error("删除文件失败:", error);
      return NextResponse.json(
        { error: "删除文件失败", details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "文件删除成功",
      deletedFiles: data,
    });
  } catch (error) {
    console.error("删除过程中出现错误:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
