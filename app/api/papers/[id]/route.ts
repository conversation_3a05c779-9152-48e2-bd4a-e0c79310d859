import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// 性能计时辅助函数
const timeStart = (label: string) => {
  console.time(`⏱️ ${label}`);
  return Date.now();
};

const timeEnd = (label: string, startTime: number) => {
  const duration = Date.now() - startTime;
  console.timeEnd(`⏱️ ${label}`);
  console.log(`🕒 ${label} 耗时: ${duration}ms`);
  return duration;
};

// 简单内存缓存实现
type CacheEntry = {
  data: any;
  expiry: number;
};

const cache: Record<string, CacheEntry> = {};

const cacheGet = (key: string) => {
  const entry = cache[key];
  if (entry && entry.expiry > Date.now()) {
    console.log(`🔄 使用缓存：${key}`);
    return entry.data;
  }
  return null;
};

const cacheSet = (key: string, data: any, ttlSeconds: number = 60) => {
  cache[key] = {
    data,
    expiry: Date.now() + ttlSeconds * 1000,
  };
};

const clearCache = (keyPrefix: string) => {
  Object.keys(cache).forEach((key) => {
    if (key.startsWith(keyPrefix)) {
      delete cache[key];
    }
  });
  console.log(`🧹 已清除缓存: ${keyPrefix}*`);
};

// GET 方法：获取单个真题详情及其关联的句子
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  const totalStartTime = timeStart("GET /api/papers/[id] 总耗时");
  try {
    const id = params.id;

    if (!id) {
      timeEnd("GET /api/papers/[id] 总耗时", totalStartTime);
      return NextResponse.json(
        { error: "Missing required field: id" },
        { status: 400 }
      );
    }

    // 尝试从缓存获取数据
    const cacheKey = `paper_detail_${id}`;
    const cachedData = cacheGet(cacheKey);

    if (cachedData) {
      timeEnd("GET /api/papers/[id] 总耗时", totalStartTime);
      return NextResponse.json({
        success: true,
        data: cachedData,
        from_cache: true,
      });
    }

    // 并行查询paper和sentences数据
    const fetchDataStartTime = timeStart("并行查询真题和句子数据");
    const [paperResult, sentenceResult] = await Promise.all([
      // 查询paper数据
      supabase.from("papers").select("*").eq("id", id).single(),

      // 查询关联的sentences数据
      supabase
        .from("sentences")
        .select("*")
        .eq("article_id", id)
        .order("paragraph_num")
        .order("sequence"),
    ]);
    timeEnd("并行查询真题和句子数据", fetchDataStartTime);

    const { data: paperData, error: paperError } = paperResult;
    const { data: sentenceData, error: sentenceError } = sentenceResult;

    if (paperError) {
      throw paperError;
    }

    if (!paperData) {
      timeEnd("GET /api/papers/[id] 总耗时", totalStartTime);
      return NextResponse.json({ error: "Paper not found" }, { status: 404 });
    }

    if (sentenceError) {
      throw sentenceError;
    }

    // 转换数据格式以保持与原有API兼容
    const formatDataStartTime = timeStart("格式化数据");
    const formattedPaper = {
      id: paperData.id,
      year: paperData.year,
      type: paperData.type,
      sectionType: paperData.section_type,
      content: paperData.content,
      reference_translation: paperData.reference_translation,
      translation_analysis: paperData.translation_analysis,
      title: `${paperData.year}年全国硕士研究生招生考试${paperData.type}试题`,
      createdAt: paperData.created_at,
      updatedAt: paperData.updated_at,
    };

    // 转换句子数据
    const formattedSentences = sentenceData.map((sentence) => ({
      id: sentence.id,
      paragraphNum: sentence.paragraph_num,
      indexNum: sentence.sequence,
      originalContent: sentence.content,
      explain_md: sentence.explain_md,
      is_marked: sentence.is_marked,
      createdAt: sentence.created_at,
    }));

    // 合并结果数据
    const responseData = {
      ...formattedPaper,
      sentences: formattedSentences,
    };
    timeEnd("格式化数据", formatDataStartTime);

    // 缓存结果数据 - 缓存5分钟
    cacheSet(cacheKey, responseData, 300);

    // 返回paper数据及其关联的sentences
    timeEnd("GET /api/papers/[id] 总耗时", totalStartTime);
    return NextResponse.json({
      success: true,
      data: responseData,
    });
  } catch (error) {
    timeEnd("GET /api/papers/[id] 总耗时", totalStartTime);
    console.error("Error fetching paper:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch paper",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// PUT 方法：更新真题内容
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  const totalStartTime = timeStart("PUT /api/papers/[id] 总耗时");
  try {
    const id = params.id;
    if (!id) {
      timeEnd("PUT /api/papers/[id] 总耗时", totalStartTime);
      return NextResponse.json(
        { error: "Missing required field: id" },
        { status: 400 }
      );
    }

    const parseBodyStartTime = timeStart("解析请求体");
    const data = await request.json();
    timeEnd("解析请求体", parseBodyStartTime);

    // 构建更新字段
    const prepareDataStartTime = timeStart("准备更新数据");
    const updateData: any = {};

    // 检查并添加要更新的字段
    if (data.content !== undefined) {
      updateData.content = data.content;
    }

    if (data.reference_translation !== undefined) {
      updateData.reference_translation = data.reference_translation;
    }

    if (
      data.translation_analysis !== undefined ||
      data.analysis !== undefined
    ) {
      updateData.translation_analysis =
        data.translation_analysis || data.analysis;
    }

    // 如果没有要更新的字段，则返回错误
    if (Object.keys(updateData).length === 0) {
      timeEnd("PUT /api/papers/[id] 总耗时", totalStartTime);
      return NextResponse.json(
        { error: "No fields to update" },
        { status: 400 }
      );
    }
    timeEnd("准备更新数据", prepareDataStartTime);

    // 执行更新
    const updateStartTime = timeStart("执行数据更新");
    const { error } = await supabase
      .from("papers")
      .update(updateData)
      .eq("id", id);
    timeEnd("执行数据更新", updateStartTime);

    if (error) {
      throw error;
    }

    // 清除相关缓存
    clearCache(`paper_detail_${id}`);
    clearCache("papers_");

    // 返回成功响应
    timeEnd("PUT /api/papers/[id] 总耗时", totalStartTime);
    return NextResponse.json({
      success: true,
      message: "Paper updated successfully",
      data: { id },
    });
  } catch (error) {
    timeEnd("PUT /api/papers/[id] 总耗时", totalStartTime);
    console.error("Error updating paper:", error);
    return NextResponse.json(
      {
        error: "Failed to update paper",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// DELETE 方法：删除真题及其关联的句子（通过外键约束自动删除）
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  const totalStartTime = timeStart("DELETE /api/papers/[id] 总耗时");
  try {
    const id = params.id;

    if (!id) {
      timeEnd("DELETE /api/papers/[id] 总耗时", totalStartTime);
      return NextResponse.json(
        { error: "Missing required field: id" },
        { status: 400 }
      );
    }

    // 删除paper记录（关联的sentences会通过外键约束自动删除）
    const deleteStartTime = timeStart("执行删除操作");
    const { error } = await supabase.from("papers").delete().eq("id", id);
    timeEnd("执行删除操作", deleteStartTime);

    if (error) {
      throw error;
    }

    // 清除相关缓存
    clearCache(`paper_detail_${id}`);
    clearCache("papers_");

    // 返回成功响应
    timeEnd("DELETE /api/papers/[id] 总耗时", totalStartTime);
    return NextResponse.json({
      success: true,
      message: "Paper deleted successfully",
    });
  } catch (error) {
    timeEnd("DELETE /api/papers/[id] 总耗时", totalStartTime);
    console.error("Error deleting paper:", error);
    return NextResponse.json(
      {
        error: "Failed to delete paper",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
