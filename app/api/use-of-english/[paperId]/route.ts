import { NextRequest, NextResponse } from "next/server";
import { createServerSupabaseClient } from "@/utils/supabase/server";

/**
 * 检查用户订阅状态
 */
async function checkSubscription(userId: string) {
  try {
    const supabase = await createServerSupabaseClient();

    // 首先检查是否有终身会员购买记录
    const { data: lifetimeMembership } = await supabase
      .from("zpay_transactions")
      .select("*")
      .eq("user_id", userId)
      .eq("status", "success")
      .eq("product_id", "lifetime-membership")
      .limit(1);

    // 如果有终身会员记录
    if (lifetimeMembership && lifetimeMembership.length > 0) {
      return {
        isActive: true,
        type: "终身会员",
        endDate: "永久有效",
      };
    }

    // 如果没有终身会员，查询常规订阅
    const now = new Date();
    const { data: subscriptions } = await supabase
      .from("zpay_transactions")
      .select("*")
      .eq("user_id", userId)
      .eq("status", "success")
      .eq("is_subscription", true)
      .lt("subscription_start", now.toISOString())
      .gt("subscription_end", now.toISOString())
      .order("subscription_end", { ascending: false })
      .limit(1);

    if (subscriptions && subscriptions.length > 0) {
      return {
        isActive: true,
        type:
          subscriptions[0].metadata?.subscription_period === "yearly"
            ? "年付专业版"
            : "月付专业版",
        endDate: subscriptions[0].subscription_end,
      };
    }

    return { isActive: false };
  } catch (error) {
    console.error("检查订阅状态出错:", error);
    return { isActive: false };
  }
}

/**
 * 获取完形填空试题数据
 * GET /api/use-of-english/:paperId
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { paperId: string } }
) {
  try {
    const paperId = parseInt(params.paperId);

    if (isNaN(paperId)) {
      return NextResponse.json(
        { success: false, error: "无效的试卷ID" },
        { status: 400 }
      );
    }

    const supabase = await createServerSupabaseClient();

    // 获取试卷基本信息
    const { data: paper, error: paperError } = await supabase
      .from("papers")
      .select("id, year, type, section_type")
      .eq("id", paperId)
      .eq("section_type", "use_of_english")
      .single();

    // 增加详细错误信息
    if (paperError) {
      console.error("查询试卷信息错误:", paperError);
      return NextResponse.json(
        {
          success: false,
          error: "未找到试卷",
          details: paperError.message,
          code: "PAPER_QUERY_ERROR",
        },
        { status: 404 }
      );
    }

    if (!paper) {
      // 尝试查询不带section_type限制的试卷信息，确认试卷是否存在
      const { data: checkPaper } = await supabase
        .from("papers")
        .select("id, section_type")
        .eq("id", paperId)
        .single();

      let detailMsg = "试卷ID不存在";
      if (checkPaper) {
        detailMsg = `试卷存在但section_type为"${checkPaper.section_type}"，不是"use_of_english"`;
      }

      return NextResponse.json(
        {
          success: false,
          error: "未找到完形填空试卷",
          details: detailMsg,
          code: "PAPER_NOT_FOUND",
        },
        { status: 404 }
      );
    }

    // 检查用户是否有权限访问特定年份
    // 非会员可访问的年份
    const FREE_ACCESS_YEARS = ["2024", "2025"];

    // 如果不是免费年份，检查用户是否有订阅权限
    if (!FREE_ACCESS_YEARS.includes(paper.year)) {
      // 获取当前用户
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        return NextResponse.json(
          { success: false, error: "请登录后访问" },
          { status: 401 }
        );
      }

      // 检查用户订阅状态
      const subscription = await checkSubscription(user.id);
      if (!subscription?.isActive) {
        return NextResponse.json(
          { success: false, error: "此内容仅对订阅用户开放" },
          { status: 403 }
        );
      }
    }

    // 获取完形填空原文
    const { data: text, error: textError } = await supabase
      .from("use_of_english_texts")
      .select("*")
      .eq("paper_id", paperId)
      .single();

    // 获取完形填空题目
    const { data: questions, error: questionsError } = await supabase
      .from("use_of_english_questions")
      .select("*")
      .eq("paper_id", paperId)
      .order("question_number", { ascending: true });

    return NextResponse.json({
      success: true,
      data: {
        paper,
        text: text || null,
        questions: questions || [],
      },
    });
  } catch (error) {
    console.error("获取完形填空数据出错:", error);
    return NextResponse.json(
      { success: false, error: "获取数据失败" },
      { status: 500 }
    );
  }
}
