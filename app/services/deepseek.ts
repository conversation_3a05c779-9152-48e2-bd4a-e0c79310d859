export interface AnalysisResult {
  sentences: Array<{
    content: string;
    translation: string;
    grammarAnalysis: string;
    keyWords: string[];
    examPoints: string[];
  }>;
}

interface Message {
  role: "user" | "assistant" | "system";
  content: string;
}

interface ChatCompletionsOptions {
  model: string;
  messages: Message[];
  temperature?: number;
  max_tokens?: number;
}

interface ChatResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
}

export class DeepSeek {
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.DEEPSEEK_API_KEY || "";
    this.baseUrl = "https://api.deepseek.com/v1";
  }

  async chat({ messages }: { messages: Message[] }): Promise<ChatResponse> {
    return this.chatCompletions({
      model: "deepseek-chat",
      messages,
      temperature: 0.7,
      max_tokens: 2000,
    });
  }

  async chatCompletions(
    options: ChatCompletionsOptions
  ): Promise<ChatResponse> {
    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${this.apiKey}`,
      },
      body: JSON.stringify({
        model: options.model,
        messages: options.messages,
        temperature: options.temperature || 0.7,
        max_tokens: options.max_tokens || 2000,
      }),
    });

    if (!response.ok) {
      throw new Error("Failed to call DeepSeek API");
    }

    return response.json();
  }
}

export async function analyzeText(
  content: string,
  prompt: string
): Promise<AnalysisResult> {
  try {
    const response = await fetch("/api/analyze", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ content, prompt }),
    });

    if (!response.ok) {
      throw new Error("Analysis request failed");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Analysis failed:", error);
    throw error;
  }
}
