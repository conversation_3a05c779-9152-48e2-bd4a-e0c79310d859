"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import { Loader2 } from "lucide-react";
import ImageUploader from "@/app/components/ImageUploader";

export default function NewWritingTaskPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 表单数据
  const [year, setYear] = useState("");
  const [type, setType] = useState("");
  const [taskType, setTaskType] = useState<
    "small_composition" | "large_composition"
  >("small_composition");
  const [content, setContent] = useState("");
  const [imageUrl, setImageUrl] = useState("");
  const [imageDescription, setImageDescription] = useState("");

  // 处理图片上传结果
  const handleImageUploaded = (url: string, description: string) => {
    setImageUrl(url);
    setImageDescription(description);
  };

  // 创建写作任务
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!year || !type || !taskType || !content) {
      alert("请填写所有必填字段");
      return;
    }

    // 如果是大作文且没有上传图片，提示用户
    if (taskType === "large_composition" && !imageUrl) {
      const confirm = window.confirm(
        "您选择了大作文但未上传图片，是否继续创建任务？"
      );
      if (!confirm) return;
    }

    setIsSubmitting(true);
    try {
      // 准备表单数据
      const formData = new FormData();
      formData.append("year", year);
      formData.append("type", type);
      formData.append("task_type", taskType);
      formData.append("content", content);

      // 如果有图片信息，添加到表单
      if (imageUrl) {
        formData.append("image_url", imageUrl);
      }
      if (imageDescription) {
        formData.append("image_description", imageDescription);
      }

      // 发送请求
      const res = await fetch("/api/writing-tasks", {
        method: "POST",
        body: formData,
      });

      const data = await res.json();

      if (data.success) {
        alert("创建成功!");
        router.push("/admin/writing-tasks");
      } else {
        throw new Error(data.error || "创建失败");
      }
    } catch (error) {
      console.error("创建写作任务失败:", error);
      alert("创建写作任务失败");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto my-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">创建写作任务</h1>
        <Button
          variant="outline"
          onClick={() => router.push("/admin/writing-tasks")}
        >
          返回列表
        </Button>
      </div>

      <Card>
        <CardContent className="pt-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="year">年份 *</Label>
                <Input
                  id="year"
                  value={year}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setYear(e.target.value)
                  }
                  placeholder="例如：2023"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">考试类型 *</Label>
                <Select value={type} onValueChange={setType} required>
                  <SelectTrigger>
                    <SelectValue placeholder="选择考试类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="英语一">英语一</SelectItem>
                    <SelectItem value="英语二">英语二</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="taskType">作文类型 *</Label>
              <Select
                value={taskType}
                onValueChange={setTaskType as any}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择作文类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="small_composition">小作文</SelectItem>
                  <SelectItem value="large_composition">大作文</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-gray-500">
                {taskType === "small_composition"
                  ? "小作文通常是应用文，如书信、通知、申请等"
                  : "大作文通常是议论文，需要论述特定的话题或现象"}
              </p>
            </div>

            {taskType === "large_composition" && (
              <ImageUploader onImageUploaded={handleImageUploaded} />
            )}

            <div className="space-y-2">
              <Label htmlFor="content">题目内容 *</Label>
              <Textarea
                id="content"
                value={content}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                  setContent(e.target.value)
                }
                placeholder="请输入题目内容..."
                rows={10}
                required
              />
              <p className="text-sm text-gray-500">
                请输入完整的题目内容，包括所有要求和提示信息。提交后系统会自动生成AI参考范文。
              </p>
            </div>

            <div className="flex justify-end">
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    创建中...
                  </>
                ) : (
                  "创建任务"
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
