"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import ReactMarkdown from "react-markdown";
import type { Components } from "react-markdown";
import { ShareButton } from "@/app/components/ui/share-button";
import { X, BookOpen, Info, ChevronDown, ChevronRight } from "lucide-react";

interface Paper {
  id: string;
  title: string;
  year: number;
  type: string;
  sectionType: string;
  createdAt: string;
}

interface Sentence {
  id: string;
  content: string;
  paragraph_num: number;
  sequence: number;
  explain_md?: string;
}

// 代码块组件
const CodeBlockComponent = ({
  className,
  children,
}: {
  className?: string;
  children: React.ReactNode;
}) => {
  // 处理语言标识
  const language = className ? className.replace("language-", "") : "";
  const codeContent = children?.toString() || "";

  return (
    <div className="relative group">
      <div className="absolute right-0 top-0 px-2 py-1 rounded-bl text-xs text-slate-400 bg-slate-800 font-mono">
        {language}
      </div>
      <pre className="bg-slate-900 text-white p-4 pt-8 rounded-lg overflow-x-auto my-4 text-sm leading-relaxed">
        <code className={className}>{codeContent}</code>
      </pre>
    </div>
  );
};

export default function PaperDetailPage() {
  const params = useParams();
  const router = useRouter();
  const paperId = params?.id as string;

  const [paper, setPaper] = useState<Paper | null>(null);
  const [sentences, setSentences] = useState<Sentence[]>([]);
  const [activeTab, setActiveTab] = useState<string>("all");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedSentence, setSelectedSentence] = useState<string | null>(null);

  // 获取真题详情
  useEffect(() => {
    const fetchPaperDetail = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/papers/${paperId}`);

        if (!response.ok) {
          throw new Error("获取真题详情失败");
        }

        const data = await response.json();

        if (data.success) {
          setPaper(data.data);

          // 处理句子数据
          const sentencesData = data.data.sentences || [];
          setSentences(
            sentencesData.map((s: any) => ({
              id: s.id,
              content: s.originalContent || s.content,
              paragraph_num: s.paragraphNum || s.paragraph_num || 1,
              sequence: s.indexNum || s.sequence || 1,
              // 预处理Markdown内容：移除markdown语言标记
              explain_md: s.explain_md
                ? s.explain_md
                    .replace(/```markdown/g, "```")
                    .replace(/```\s*$/g, "```")
                : s.explain_md,
            }))
          );
        } else {
          throw new Error(data.error || "获取数据失败");
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "未知错误");
        console.error("获取真题详情错误:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchPaperDetail();
  }, [paperId]);

  // 过滤当前活动标签的句子
  const filteredSentences =
    activeTab === "all"
      ? sentences
      : sentences.filter((s) => s.paragraph_num === parseInt(activeTab));

  // 获取段落编号列表 - 修复Set循环访问的类型错误
  const paragraphNumberSet = new Set<number>(
    sentences.map((s) => s.paragraph_num)
  );
  const paragraphNumbers = Array.from(paragraphNumberSet).sort((a, b) => a - b);

  // 按段落分组句子
  const sentencesByParagraph = filteredSentences.reduce(
    (acc: Record<number, Sentence[]>, sentence) => {
      const paragraphNum = sentence.paragraph_num;
      if (!acc[paragraphNum]) {
        acc[paragraphNum] = [];
      }
      acc[paragraphNum].push(sentence);
      return acc;
    },
    {}
  );

  // 获取题型显示名称
  const getSectionTypeDisplayName = (sectionType: string) => {
    switch (sectionType) {
      case "use_of_english":
        return "完形填空";
      case "reading_comprehension":
        return "阅读理解";
      case "translation":
        return "翻译";
      case "writing":
        return "写作";
      default:
        return sectionType;
    }
  };

  // 处理句子点击
  const handleSentenceClick = (sentenceId: string) => {
    setSelectedSentence(selectedSentence === sentenceId ? null : sentenceId);
  };

  // 预处理Markdown内容
  const processMarkdownContent = (content: string) => {
    if (!content) return "";
    // 移除markdown语言标记，已经在数据获取时处理
    return content;
  };

  // 自定义Markdown组件
  const markdownComponents: Components = {
    // 处理代码块
    code({ className, children, node, ...props }) {
      // 检查是否是代码块，而不是内联代码
      // @ts-ignore - 没有inline属性，但我们可以检查父节点
      const isCodeBlock = node?.parent?.tagName === "pre";

      if (isCodeBlock) {
        return (
          <CodeBlockComponent className={className}>
            {children}
          </CodeBlockComponent>
        );
      }

      // 内联代码
      return (
        <code
          className="bg-slate-100 text-slate-800 px-1 py-0.5 rounded text-sm"
          {...props}
        >
          {children}
        </code>
      );
    },
    // 重写h2标签，使标题更明显
    h2({ children }) {
      return (
        <h2 className="text-xl font-bold text-blue-700 my-4 border-b pb-2 border-blue-200">
          {children}
        </h2>
      );
    },
    // 重写h3标签
    h3({ children }) {
      return (
        <h3 className="text-lg font-semibold text-blue-600 mt-5 mb-3">
          {children}
        </h3>
      );
    },
    // 处理段落
    p({ children }) {
      return <p className="my-3 leading-relaxed">{children}</p>;
    },
    // 处理列表项
    li({ children }) {
      return <li className="mb-2">{children}</li>;
    },
    // 处理引用块
    blockquote({ children }) {
      return (
        <blockquote className="border-l-4 border-blue-300 bg-blue-50 p-3 my-4 rounded-r text-gray-700 italic">
          {children}
        </blockquote>
      );
    },
    // 处理加粗文本
    strong({ children }) {
      return <strong className="font-bold text-gray-900">{children}</strong>;
    },
    // 处理pre标签
    pre({ children }) {
      // 直接返回子元素，让code组件处理样式
      return <>{children}</>;
    },
  };

  // 修改句子解析部分，调整内边距并添加分享按钮
  const sentenceExplanation = (sentence: Sentence, onClose: () => void) => (
    <div
      key={`explanation-${sentence.id}`}
      className="mt-6 bg-gray-50 px-6 pb-6 pt-2 rounded-lg border-l-4 border-blue-500 animate-fadeIn relative"
    >
      <div className="flex justify-between items-center">
        <h4 className="text-blue-800 font-medium py-2">句子解析</h4>
        <div className="flex items-center gap-2">
          <ShareButton
            title={`考研英语句子解析 - ${paper?.year}年${paper?.type}`}
            content={sentence.content}
            explanation={sentence.explain_md}
            type="sentence"
            sourceText={`${paper?.year}年${paper?.type}真题`}
            buttonClassName="text-gray-500 hover:text-blue-600 p-1 rounded-full hover:bg-blue-50"
            iconOnly={true}
          />
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
      </div>
      <div className="markdown-body">
        <ReactMarkdown components={markdownComponents}>
          {processMarkdownContent(sentence.explain_md || "")}
        </ReactMarkdown>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
          <p className="text-blue-700 font-medium">正在加载真题...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="bg-white rounded-xl shadow-md p-8 max-w-md w-full text-center">
          <div className="flex justify-center mb-4">
            <div className="bg-red-100 p-3 rounded-full">
              <svg
                className="h-8 w-8 text-red-500"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
          </div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">加载失败</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <div className="flex justify-center">
            <Link
              href="/"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              返回首页
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!paper) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="bg-white rounded-xl shadow-md p-8 max-w-md w-full text-center">
          <div className="flex justify-center mb-4">
            <div className="bg-yellow-100 p-3 rounded-full">
              <svg
                className="h-8 w-8 text-yellow-500"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
          </div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">未找到真题</h2>
          <p className="text-gray-600 mb-6">
            未能找到指定的真题，可能已被删除或不存在。
          </p>
          <div className="flex justify-center">
            <Link
              href="/"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              返回首页
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pb-12">
      {/* 顶部导航栏 */}
      <div className="bg-white shadow-sm sticky top-0 z-10">
        <div className="container mx-auto px-4 py-4 flex items-center">
          <Link
            href="/"
            className="text-blue-600 hover:text-blue-800 flex items-center"
          >
            <svg
              className="h-5 w-5 mr-2"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
            返回首页
          </Link>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* 真题标题和基本信息 */}
        <div className="bg-white rounded-xl shadow-md p-6 mb-8">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-800 mb-4">
            {paper.title}
          </h1>

          <div className="flex flex-wrap gap-4">
            <div className="bg-blue-50 px-4 py-2 rounded-lg flex items-center">
              <span className="text-blue-800 font-medium">{paper.year}年</span>
            </div>

            <div className="bg-blue-50 px-4 py-2 rounded-lg flex items-center">
              <span className="text-blue-800 font-medium">{paper.type}</span>
            </div>

            <div className="bg-blue-50 px-4 py-2 rounded-lg flex items-center">
              <span className="text-blue-800 font-medium">
                {getSectionTypeDisplayName(paper.sectionType)}
              </span>
            </div>
          </div>
        </div>

        {/* 段落导航 */}
        {paragraphNumbers.length > 1 && (
          <div className="bg-white rounded-xl shadow-md p-4 mb-8 overflow-x-auto">
            <div className="flex space-x-2">
              <button
                onClick={() => setActiveTab("all")}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === "all"
                    ? "bg-blue-600 text-white"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
              >
                全部段落
              </button>

              {paragraphNumbers.map((num) => (
                <button
                  key={num}
                  onClick={() => setActiveTab(num.toString())}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    activeTab === num.toString()
                      ? "bg-blue-600 text-white"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  }`}
                >
                  段落 {num}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* 阅读正文 */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          {filteredSentences.length === 0 ? (
            <div className="p-8 text-center">
              <p className="text-gray-500">暂无句子数据</p>
            </div>
          ) : (
            <div className="p-8">
              {/* 提示信息 */}
              <div className="mb-6 bg-blue-50 p-4 rounded-lg text-blue-700">
                <div className="flex items-center">
                  <svg
                    className="h-5 w-5 mr-2"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <span className="font-medium">
                    点击带下划线的句子可以查看详细解析
                  </span>
                </div>
              </div>

              {/* 按段落组织句子 */}
              {Object.entries(sentencesByParagraph).map(
                ([paragraphNum, sentences]) => (
                  <div key={paragraphNum} className="mb-8">
                    <h2 className="text-lg font-bold text-gray-700 mb-4">
                      段落 {paragraphNum}
                    </h2>
                    <div className="text-gray-800 leading-relaxed text-lg">
                      {sentences
                        .sort((a, b) => a.sequence - b.sequence)
                        .map((sentence, index) => (
                          <span key={sentence.id}>
                            <span
                              onClick={() =>
                                sentence.explain_md &&
                                handleSentenceClick(sentence.id)
                              }
                              className={`${
                                sentence.explain_md
                                  ? "cursor-pointer border-b-2 border-dotted border-blue-400 hover:text-blue-600 hover:border-blue-600"
                                  : ""
                              } ${
                                selectedSentence === sentence.id
                                  ? "text-blue-600 border-blue-600"
                                  : "text-gray-800"
                              }`}
                            >
                              {sentence.content}
                            </span>
                            {index < sentences.length - 1 && " "}
                          </span>
                        ))}
                    </div>

                    {/* 句子解析 */}
                    {sentences.map(
                      (sentence) =>
                        selectedSentence === sentence.id &&
                        sentence.explain_md &&
                        sentenceExplanation(sentence, () =>
                          setSelectedSentence(null)
                        )
                    )}
                  </div>
                )
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
