"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { createClient } from "@/utils/supabase/client";

// 产品特性接口
interface ProductFeature {
  id: string;
  text: string;
}

// 产品类型定义
interface Product {
  id: string;
  name: string;
  title: string;
  description: string;
  price: string;
  priceLabel: string;
  isSubscription: boolean;
  subscriptionPeriod?: string;
  features: ProductFeature[];
}

export default function Pricing() {
  const [loading, setLoading] = useState<boolean>(false);
  const [products, setProducts] = useState<Record<string, Product>>({});
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [userId, setUserId] = useState<string | null>(null);
  const router = useRouter();
  const supabase = createClient();

  // 检查用户登录状态
  useEffect(() => {
    const checkUser = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      setUserId(session?.user?.id || null);
    };

    checkUser();
  }, [supabase]);

  // 获取产品信息
  useEffect(() => {
    const fetchProducts = async () => {
      console.log("获取产品信息");
      try {
        const response = await fetch("/api/products");
        const data = await response.json();
        setProducts(data.products);
      } catch (error) {
        console.error("获取产品信息失败:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // 处理支付请求
  const handlePayment = async (productId: string) => {
    // 更严格检查用户是否登录
    if (!userId) {
      console.log("用户未登录，跳转到登录页面");
      // 保存当前页面URL，以便登录后返回
      router.push("/sign-in?redirect=/");
      return;
    }

    // 再次验证用户登录状态
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      console.log("会话已过期，需要重新登录");
      router.push("/sign-in?redirect=/");
      return;
    }

    // 设置加载状态
    setLoading(true);

    try {
      // 获取产品信息
      const product = products[productId];
      if (!product) {
        throw new Error("产品不存在");
      }

      // 构建支付请求数据
      const paymentData: {
        productId: string;
        productName: string;
        amount: string;
        isSubscription: boolean;
        paymentMethod: string;
        subscriptionPeriod?: "monthly" | "yearly";
      } = {
        productId: product.id,
        productName: product.name,
        amount: product.price,
        isSubscription: product.isSubscription,
        paymentMethod: "wxpay", // 如果是支付宝，修改成为alipay
      };

      // 如果是订阅产品，添加订阅周期
      if (product.isSubscription && product.subscriptionPeriod) {
        paymentData.subscriptionPeriod =
          product.subscriptionPeriod === "yearly" ? "yearly" : "monthly";
      }

      // 发送请求获取支付链接
      const response = await fetch("/api/checkout/providers/zpay/url", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(paymentData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "获取支付链接失败");
      }

      // 获取支付链接
      const { url } = await response.json();

      // 跳转到支付页面
      window.location.href = url;
    } catch (error) {
      console.error("支付处理失败:", error);
      alert(
        `支付处理失败: ${error instanceof Error ? error.message : "未知错误"}`
      );
    } finally {
      setLoading(false);
    }
  };

  // 显示加载状态
  if (isLoading) {
    return (
      <section className="relative border-t border-gray-100">
        <div className="max-w-6xl mx-auto px-4 sm:px-6">
          <div className="py-12 md:py-20 text-center">
            <p>加载产品信息中...</p>
          </div>
        </div>
      </section>
    );
  }

  // 获取产品数据
  const membership = products["lifetime-membership"];

  if (!membership) {
    return (
      <section className="relative border-t border-gray-100">
        <div className="max-w-6xl mx-auto px-4 sm:px-6">
          <div className="py-12 md:py-20 text-center">
            <p>暂无产品信息，请稍后再试</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="relative border-t border-gray-100">
      {/* 背景渐变 */}
      <div
        className="absolute top-0 left-0 right-0 bg-gradient-to-b from-blue-50 to-white h-1/2 pointer-events-none -z-10"
        aria-hidden="true"
      />
      <div className="max-w-6xl mx-auto px-4 sm:px-6">
        <div className="py-12 md:py-20">
          {/* 页面标题 */}
          <div className="max-w-3xl mx-auto text-center pb-12 md:pb-16">
            <h2 className="h2 font-cabinet-grotesk">大学生备考资源终身会员</h2>
            <p className="text-xl text-gray-600 mt-4">
              一次付费，终身使用，囊括大学生备考所需的全部学习资源合集及汇总
            </p>
          </div>

          {/* 产品卡片 */}
          <div className="max-w-lg mx-auto">
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-blue-100 transform transition-all hover:shadow-xl">
              {/* 卡片头部 */}
              <div className="bg-blue-600 py-6 px-8 text-center">
                <div className="inline-block bg-white/20 py-1 px-4 rounded-full text-white text-sm font-semibold mb-3">
                  终身会员特权
                </div>
                <h3 className="text-2xl text-white font-bold">
                  {membership.title}
                </h3>
                <div className="flex items-center justify-center mt-2">
                  <span className="text-blue-100 text-2xl">¥</span>
                  <span className="text-white text-5xl font-bold mx-1">
                    {membership.price}
                  </span>
                  <span className="text-blue-100">{membership.priceLabel}</span>
                </div>
              </div>

              {/* 产品描述 */}
              <div className="px-8 py-6 text-center">
                <p className="text-gray-600">{membership.description}</p>
              </div>

              {/* 购买按钮 */}
              <div className="px-8 pb-6">
                <button
                  className="text-white bg-blue-600 hover:bg-blue-700 w-full shadow-sm py-3 px-4 rounded-lg transition-colors duration-150 ease-in-out font-medium text-lg"
                  onClick={() => handlePayment("lifetime-membership")}
                  disabled={loading}
                >
                  {loading ? "处理中..." : "立即开通会员"}
                </button>
              </div>

              {/* 产品特性列表 */}
              <div className="px-8 pb-8 pt-2">
                <h4 className="font-semibold text-gray-800 mb-4 text-center">
                  会员特权包含：
                </h4>
                <ul className="space-y-4">
                  {membership.features.map((feature) => (
                    <li key={feature.id} className="flex items-start">
                      <svg
                        className="w-5 h-5 fill-current text-green-500 mr-3 shrink-0 mt-0.5"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <span className="text-gray-700">{feature.text}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* 满意度保证 */}
            <div className="mt-8 text-center">
              <p className="text-sm text-gray-500 flex items-center justify-center">
                <svg
                  className="w-4 h-4 fill-current text-green-500 mr-2"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                内容持续更新，助您一次考试成功
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
