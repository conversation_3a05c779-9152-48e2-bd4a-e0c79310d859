"use client";

import { useState, useEffect } from "react";
import {
  ArrowLeft,
  Check,
  ChevronDown,
  ChevronUp,
  Languages,
  Lightbulb,
  Loader2,
  X,
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import ReactMarkdown from "react-markdown";
import type { Components } from "react-markdown";

interface Sentence {
  id: number;
  paragraphNum: number;
  indexNum: number;
  originalContent: string;
  explain_md?: string;
  is_marked?: boolean;
  createdAt: string;
}

interface Paper {
  id: number;
  year: string;
  type: string;
  sectionType: string;
  title: string;
  content?: string;
  reference_translation?: string;
  translation_analysis?: string;
  createdAt: string;
  updatedAt: string;
  sentences: Sentence[];
}

interface TranslationResult {
  reference_translation: string;
  difficulty_analysis: string;
  translation_skills: string;
  user_evaluation?: string;
  user_score?: number;
}

// 代码块组件
const CodeBlockComponent = ({
  className,
  children,
}: {
  className?: string;
  children: React.ReactNode;
}) => {
  // 处理语言标识
  const language = className ? className.replace("language-", "") : "";
  const codeContent = children?.toString() || "";

  return (
    <div className="relative group">
      <div className="absolute right-0 top-0 px-2 py-1 rounded-bl text-xs text-slate-400 bg-slate-800 font-mono">
        {language}
      </div>
      <pre className="bg-slate-900 text-white p-4 pt-8 rounded-lg overflow-x-auto my-4 text-sm leading-relaxed">
        <code className={className}>{codeContent}</code>
      </pre>
    </div>
  );
};

// 每个句子的翻译状态
interface SentenceTranslation {
  userInput: string;
  result: TranslationResult | null;
  isSubmitting: boolean;
}

export const English1TranslationPage = ({ paperId }: { paperId: string }) => {
  const router = useRouter();

  // 状态管理
  const [paper, setPaper] = useState<Paper | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedSentenceId, setSelectedSentenceId] = useState<number | null>(
    null
  );

  // 为每个句子单独管理状态
  const [translationStates, setTranslationStates] = useState<{
    [key: number]: SentenceTranslation;
  }>({});

  // 获取试卷数据
  useEffect(() => {
    const fetchPaper = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/papers/${paperId}`);

        if (!response.ok) {
          throw new Error("获取试卷数据失败");
        }

        const data = await response.json();

        if (data.success) {
          // 对句子按段落和序号排序
          const sortedSentences = [...data.data.sentences].sort((a, b) => {
            if (a.paragraphNum !== b.paragraphNum) {
              return a.paragraphNum - b.paragraphNum;
            }
            return a.indexNum - b.indexNum;
          });

          setPaper({
            ...data.data,
            sentences: sortedSentences,
          });

          // 初始化每个划线句子的翻译状态
          const initialStates: { [key: number]: SentenceTranslation } = {};
          sortedSentences.forEach((sentence) => {
            if (sentence.is_marked) {
              initialStates[sentence.id] = {
                userInput: "",
                result: null,
                isSubmitting: false,
              };
            }
          });
          setTranslationStates(initialStates);
        } else {
          throw new Error(data.error || "获取数据失败");
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "未知错误");
        console.error("获取试卷数据失败:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchPaper();
  }, [paperId]);

  // 找出所有的划线句子
  const markedSentences =
    paper?.sentences.filter((sentence) => sentence.is_marked) || [];

  // 更新特定句子的翻译输入
  const handleTranslationChange = (sentenceId: number, value: string) => {
    setTranslationStates((prev) => ({
      ...prev,
      [sentenceId]: {
        ...prev[sentenceId],
        userInput: value,
      },
    }));
  };

  // 提交用户的翻译
  const handleSubmitTranslation = async (sentenceId: number) => {
    try {
      // 更新提交状态
      setTranslationStates((prev) => ({
        ...prev,
        [sentenceId]: {
          ...prev[sentenceId],
          isSubmitting: true,
        },
      }));

      // 找到当前句子的内容
      const sentence = paper?.sentences.find((s) => s.id === sentenceId);
      if (!sentence) {
        throw new Error("找不到句子数据");
      }

      const response = await fetch("/api/english1-translation", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: sentence.originalContent,
          user_translation: translationStates[sentenceId].userInput,
          sentence_id: sentenceId,
        }),
      });

      if (!response.ok) {
        throw new Error("提交翻译失败");
      }

      const result = await response.json();

      // 更新结果状态
      setTranslationStates((prev) => ({
        ...prev,
        [sentenceId]: {
          ...prev[sentenceId],
          result: result.data,
          isSubmitting: false,
        },
      }));
    } catch (err) {
      setError(err instanceof Error ? err.message : "未知错误");
      console.error("提交翻译失败:", err);

      // 重置提交状态
      setTranslationStates((prev) => ({
        ...prev,
        [sentenceId]: {
          ...prev[sentenceId],
          isSubmitting: false,
        },
      }));
    }
  };

  // 切换显示句子解析
  const handleSentenceClick = (sentenceId: number) => {
    setSelectedSentenceId(
      selectedSentenceId === sentenceId ? null : sentenceId
    );
  };

  // 自定义Markdown组件
  const markdownComponents: Components = {
    // 处理代码块
    code({ className, children, node, ...props }) {
      // @ts-ignore - 检查是否是代码块
      const isCodeBlock = node?.parent?.tagName === "pre";

      if (isCodeBlock) {
        return (
          <CodeBlockComponent className={className}>
            {children}
          </CodeBlockComponent>
        );
      }

      // 内联代码
      return (
        <code
          className="bg-slate-100 text-slate-800 px-1 py-0.5 rounded text-sm"
          {...props}
        >
          {children}
        </code>
      );
    },
    // 重写h2标签
    h2({ children }) {
      return (
        <h2 className="text-xl font-bold text-blue-700 my-4 border-b pb-2 border-blue-200">
          {children}
        </h2>
      );
    },
    // 重写h3标签
    h3({ children }) {
      return (
        <h3 className="text-lg font-semibold text-blue-600 mt-5 mb-3">
          {children}
        </h3>
      );
    },
    // 处理段落
    p({ children }) {
      return <p className="my-3 leading-relaxed">{children}</p>;
    },
    // 处理列表项
    li({ children }) {
      return <li className="mb-2">{children}</li>;
    },
    // 处理引用块
    blockquote({ children }) {
      return (
        <blockquote className="border-l-4 border-blue-300 bg-blue-50 p-3 my-4 rounded-r text-gray-700 italic">
          {children}
        </blockquote>
      );
    },
    // 处理加粗文本
    strong({ children }) {
      return <strong className="font-bold text-gray-900">{children}</strong>;
    },
  };

  // 句子解析组件
  const sentenceExplanation = (sentence: Sentence, onClose: () => void) => (
    <div
      key={`explanation-${sentence.id}`}
      className="mt-6 bg-gray-50 px-6 pb-6 pt-2 rounded-lg border-l-4 border-blue-500 animate-fadeIn relative"
    >
      <button
        onClick={onClose}
        className="absolute top-2 right-3 text-gray-500 hover:text-gray-700"
      >
        <X className="h-5 w-5" />
      </button>
      <div className="markdown-body">
        <ReactMarkdown components={markdownComponents}>
          {sentence.explain_md || ""}
        </ReactMarkdown>
      </div>
    </div>
  );

  // 按段落分组句子
  const renderParagraphs = () => {
    if (!paper?.sentences || paper.sentences.length === 0) {
      return <p className="text-gray-500">暂无句子数据</p>;
    }

    // 按段落分组
    const paragraphs: { [key: number]: Sentence[] } = {};
    paper.sentences.forEach((sentence) => {
      if (!paragraphs[sentence.paragraphNum]) {
        paragraphs[sentence.paragraphNum] = [];
      }
      paragraphs[sentence.paragraphNum].push(sentence);
    });

    return Object.entries(paragraphs).map(([paragraphNum, sentences]) => {
      const paragraphNumber = parseInt(paragraphNum);
      return (
        <div key={paragraphNum} className="mb-8">
          <h2 className="text-lg font-bold text-gray-700 mb-4">
            段落 {paragraphNumber}
          </h2>
          <div className="text-gray-800 leading-relaxed text-lg">
            {sentences
              .sort((a, b) => a.indexNum - b.indexNum)
              .map((sentence, index) => (
                <span key={sentence.id}>
                  <span
                    onClick={() => handleSentenceClick(sentence.id)}
                    className={`${
                      sentence.is_marked
                        ? "bg-yellow-100 px-1 py-0.5 border-b border-yellow-400 cursor-pointer"
                        : "border-b border-gray-300 hover:border-gray-500 cursor-pointer"
                    } ${
                      selectedSentenceId === sentence.id
                        ? "text-blue-600 border-blue-600"
                        : ""
                    }`}
                  >
                    {sentence.originalContent}
                  </span>
                  {index < sentences.length - 1 && " "}
                </span>
              ))}
          </div>

          {/* 句子解析 */}
          {sentences.map(
            (sentence) =>
              selectedSentenceId === sentence.id &&
              sentence.explain_md &&
              sentenceExplanation(sentence, () => setSelectedSentenceId(null))
          )}
        </div>
      );
    });
  };

  // 主渲染
  if (loading) {
    return (
      <div className="flex justify-center items-center py-20">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl shadow p-8 text-center">
        <p className="text-red-500 mb-4">{error}</p>
        <button
          onClick={() => router.back()}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          返回
        </button>
      </div>
    );
  }

  if (!paper) {
    return (
      <div className="bg-white rounded-xl shadow p-8 text-center">
        <p className="text-gray-500">试卷数据不存在</p>
        <button
          onClick={() => router.back()}
          className="px-4 py-2 mt-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          返回
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* 头部返回按钮和标题 */}
      <div>
        <Link
          href="/exam-library"
          className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors mb-4"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          返回真题库
        </Link>
        <h1 className="text-2xl md:text-3xl font-bold text-gray-800">
          {paper.year}年 {paper.type} 翻译真题
        </h1>
        <div className="flex items-center mt-2 text-gray-500 text-sm">
          <Languages className="w-4 h-4 mr-1" />
          <span>划线句子翻译</span>
        </div>
      </div>

      {/* 原文内容 */}
      <div className="bg-white rounded-xl shadow overflow-hidden">
        <div className="p-6 border-b border-gray-100">
          <h2 className="text-xl font-bold text-gray-800">原文</h2>
          <p className="text-sm text-gray-500 mt-1">
            请阅读以下文章，黄色高亮的是划线句子。点击任何句子可查看详细解析。
          </p>
        </div>

        <div className="p-6">
          {/* 提示信息 */}
          <div className="mb-6 bg-blue-50 p-4 rounded-lg text-blue-700">
            <div className="flex items-center">
              <Lightbulb className="h-5 w-5 mr-2" />
              <span className="font-medium">
                点击任意句子可以查看详细解析，黄色高亮的是划线句子
              </span>
            </div>
          </div>

          {renderParagraphs()}
        </div>
      </div>

      {/* 划线句子翻译区域 */}
      {markedSentences.length > 0 && (
        <div className="bg-white rounded-xl shadow overflow-hidden">
          <div className="p-6 border-b border-gray-100">
            <h2 className="text-xl font-bold text-gray-800">划线句子翻译</h2>
            <p className="text-sm text-gray-500 mt-1">
              请将划线句子翻译成中文，完成后点击"提交翻译"获取解析和评价
            </p>
          </div>

          <div className="p-6 space-y-10">
            {markedSentences.map((sentence) => {
              // 获取当前句子的状态数据
              const sentenceState = translationStates[sentence.id] || {
                userInput: "",
                result: null,
                isSubmitting: false,
              };

              return (
                <div
                  key={sentence.id}
                  className="space-y-4 border-b pb-8 last:border-b-0 last:pb-0"
                >
                  <div className="bg-yellow-50 p-4 rounded-md border-l-4 border-yellow-400">
                    <p className="text-base">{sentence.originalContent}</p>
                  </div>

                  <div>
                    <label
                      htmlFor={`translation-${sentence.id}`}
                      className="block text-sm font-medium text-gray-700 mb-2"
                    >
                      你的翻译
                    </label>
                    <textarea
                      id={`translation-${sentence.id}`}
                      value={sentenceState.userInput}
                      onChange={(e) =>
                        handleTranslationChange(sentence.id, e.target.value)
                      }
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="请输入你的翻译..."
                    />
                  </div>

                  <div>
                    <button
                      onClick={() => handleSubmitTranslation(sentence.id)}
                      disabled={
                        sentenceState.isSubmitting ||
                        !sentenceState.userInput.trim()
                      }
                      className={`px-4 py-2 rounded-md ${
                        sentenceState.isSubmitting ||
                        !sentenceState.userInput.trim()
                          ? "bg-gray-300 cursor-not-allowed"
                          : "bg-blue-600 text-white hover:bg-blue-700"
                      } transition-colors inline-flex items-center`}
                    >
                      {sentenceState.isSubmitting ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          分析中...
                        </>
                      ) : (
                        <>
                          <Check className="w-4 h-4 mr-2" />
                          提交翻译
                        </>
                      )}
                    </button>
                  </div>

                  {/* 翻译结果和分析 */}
                  {sentenceState.result && (
                    <div className="bg-blue-50 p-4 rounded-md space-y-4">
                      <div>
                        <h3 className="font-medium text-gray-800 mb-2">
                          参考翻译
                        </h3>
                        <p className="text-gray-700">
                          {sentenceState.result.reference_translation}
                        </p>
                      </div>

                      <div>
                        <h3 className="font-medium text-gray-800 mb-2">
                          翻译难点
                        </h3>
                        <div className="prose prose-sm max-w-none">
                          <ReactMarkdown components={markdownComponents}>
                            {sentenceState.result.difficulty_analysis}
                          </ReactMarkdown>
                        </div>
                      </div>

                      <div>
                        <h3 className="font-medium text-gray-800 mb-2">
                          翻译技巧
                        </h3>
                        <div className="prose prose-sm max-w-none">
                          <ReactMarkdown components={markdownComponents}>
                            {sentenceState.result.translation_skills}
                          </ReactMarkdown>
                        </div>
                      </div>

                      {sentenceState.result.user_evaluation && (
                        <div>
                          <h3 className="font-medium text-gray-800 mb-2 flex items-center justify-between">
                            <span>评价反馈</span>
                            {"user_score" in sentenceState.result && (
                              <span className="text-sm bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-semibold">
                                得分: {sentenceState.result.user_score}/100
                              </span>
                            )}
                          </h3>
                          <div className="prose prose-sm max-w-none">
                            <ReactMarkdown components={markdownComponents}>
                              {sentenceState.result.user_evaluation}
                            </ReactMarkdown>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};
