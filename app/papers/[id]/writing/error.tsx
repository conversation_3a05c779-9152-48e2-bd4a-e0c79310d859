"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON>oot<PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { AlertTriangle, Home, RotateCcw } from "lucide-react";

export default function ErrorPage({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <div className="container mx-auto py-20 px-4 sm:px-6 max-w-lg">
      <Card className="border-0 shadow-lg overflow-hidden">
        <CardHeader className="bg-red-50 border-b border-red-100">
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0 bg-white p-2 rounded-full shadow-sm">
              <AlertTriangle className="h-6 w-6 text-red-500" />
            </div>
            <div>
              <CardTitle className="text-xl text-red-700">
                加载写作真题时出错
              </CardTitle>
              <CardDescription className="text-red-600 mt-1">
                很抱歉，我们无法加载所请求的内容
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <p className="text-gray-700">
              在加载写作真题数据时发生了错误。这可能是由于以下原因：
            </p>
            <ul className="list-disc list-inside text-gray-600 space-y-1">
              <li>临时的网络连接问题</li>
              <li>服务器暂时不可用</li>
              <li>请求的资源不存在或已被移除</li>
            </ul>
            <div className="mt-4 bg-amber-50 border border-amber-100 rounded-lg p-4">
              <p className="text-gray-700 text-sm font-medium mb-1">
                错误详情:
              </p>
              <p className="text-amber-800 text-sm">
                {error.message || "未知错误"}
              </p>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between border-t pt-6 bg-gray-50">
          <Button
            variant="outline"
            onClick={() => (window.location.href = "/papers")}
            className="flex items-center gap-2"
          >
            <Home className="h-4 w-4" />
            返回真题列表
          </Button>
          <Button
            onClick={reset}
            className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
          >
            <RotateCcw className="h-4 w-4" />
            重试
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
