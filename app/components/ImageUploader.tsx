"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Image as ImageIcon,
  Loader2,
  UploadCloud,
  Trash2,
  Sparkles,
} from "lucide-react";

interface ImageUploaderProps {
  onImageUploaded: (imageUrl: string, imageDescription: string) => void;
  initialImageUrl?: string;
  initialImageDescription?: string;
}

const ImageUploader = ({
  onImageUploaded,
  initialImageUrl = "",
  initialImageDescription = "",
}: ImageUploaderProps) => {
  const [isUploading, setIsUploading] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | null>(
    initialImageUrl || null
  );
  const [imageDescription, setImageDescription] = useState(
    initialImageDescription || ""
  );

  // 初始化数据
  useEffect(() => {
    if (initialImageUrl) {
      setImageUrl(initialImageUrl);
    }
    if (initialImageDescription) {
      setImageDescription(initialImageDescription);
    }
  }, [initialImageUrl, initialImageDescription]);

  // 上传图片
  const handleUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];

    // 验证文件类型
    if (!file.type.startsWith("image/")) {
      alert("请上传图片文件");
      return;
    }

    // 验证文件大小（5MB限制）
    if (file.size > 5 * 1024 * 1024) {
      alert("图片大小不能超过5MB");
      return;
    }

    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch("/api/upload-image", {
        method: "POST",
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "上传失败");
      }

      setImageUrl(data.imageUrl);
      onImageUploaded(data.imageUrl, imageDescription);
    } catch (error) {
      console.error("上传图片失败:", error);
      alert("上传图片失败，请重试");
    } finally {
      setIsUploading(false);
    }
  };

  // 分析图片
  const handleAnalyze = async () => {
    if (!imageUrl) return;

    setIsAnalyzing(true);
    try {
      const response = await fetch("/api/analyze-image", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ imageUrl }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "分析失败");
      }

      setImageDescription(data.imageDescription);
      onImageUploaded(imageUrl, data.imageDescription);
    } catch (error) {
      console.error("分析图片失败:", error);
      alert("分析图片失败，请重试");
    } finally {
      setIsAnalyzing(false);
    }
  };

  // 删除图片
  const handleDelete = () => {
    setImageUrl(null);
    setImageDescription("");
    onImageUploaded("", "");
  };

  // 更新图片描述
  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    setImageDescription(e.target.value);
    onImageUploaded(imageUrl || "", e.target.value);
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg flex items-center text-blue-700">
            <ImageIcon className="mr-2 h-5 w-5" />
            大作文图片上传
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!imageUrl ? (
            <div className="flex items-center justify-center h-48 border-2 border-dashed rounded-md border-gray-300 bg-gray-50 hover:bg-gray-100 transition-colors cursor-pointer">
              <label className="w-full h-full flex flex-col items-center justify-center cursor-pointer">
                <UploadCloud className="h-10 w-10 text-gray-400 mb-2" />
                <span className="text-sm text-gray-500 mb-1">
                  点击或拖拽上传图片
                </span>
                <span className="text-xs text-gray-400">
                  支持 PNG, JPG, GIF (最大5MB)
                </span>
                <input
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleUpload}
                  disabled={isUploading}
                />
                {isUploading && (
                  <Loader2 className="h-5 w-5 animate-spin text-blue-500 mt-2" />
                )}
              </label>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="relative">
                <div className="relative h-60 w-full bg-gray-100 rounded-md overflow-hidden">
                  <Image
                    src={imageUrl}
                    alt="上传的图片"
                    fill
                    style={{ objectFit: "contain" }}
                    unoptimized
                  />
                </div>
                <Button
                  variant="destructive"
                  size="icon"
                  className="absolute top-2 right-2 h-8 w-8 rounded-full opacity-80 hover:opacity-100"
                  onClick={handleDelete}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex justify-end">
                <Button
                  onClick={handleAnalyze}
                  disabled={isAnalyzing}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {isAnalyzing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      分析中...
                    </>
                  ) : (
                    <>
                      <Sparkles className="mr-2 h-4 w-4" />
                      AI分析图片
                    </>
                  )}
                </Button>
              </div>

              <div className="space-y-2">
                <Label htmlFor="imageDescription">图片描述（AI分析结果）</Label>
                <Textarea
                  id="imageDescription"
                  value={imageDescription}
                  onChange={handleDescriptionChange}
                  placeholder={
                    isAnalyzing
                      ? "AI正在分析图片..."
                      : '点击"AI分析图片"按钮，或手动输入图片描述'
                  }
                  rows={8}
                  className="resize-none"
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ImageUploader;
