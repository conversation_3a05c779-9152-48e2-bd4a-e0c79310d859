import { createClient } from "@supabase/supabase-js";
import { NextResponse } from "next/server";
import {
  RESOURCE_PATHS,
  TYPE_CATEGORY_MAP,
  Resource,
  getFileTypeFromName,
  getResourceTypeConfig,
  TEACHER_RESOURCE_CONFIGS,
  NESTED_TEACHER_RESOURCE_PATHS,
  getNestedPath,
  NestedSectionConfig,
} from "@/app/lib/learning-resources-config";

// 创建Supabase客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || "";
const supabase = createClient(supabaseUrl, supabaseKey);

// 获取资源函数
async function getResources(path: string, category: string, section: string) {
  const { data, error } = await supabase.storage.from("mbdata").list(path, {
    sortBy: { column: "name", order: "desc" },
  });

  if (error) {
    console.error(`获取${path}资源失败:`, error);
    return [];
  }

  if (!data || data.length === 0) {
    return [];
  }

  return await Promise.all(
    data.map(async (file) => {
      const { data: urlData } = supabase.storage
        .from("mbdata")
        .getPublicUrl(`${path}/${file.name}`);

      return {
        id: file.id,
        name: file.name,
        size: file.metadata.size,
        url: urlData.publicUrl,
        created_at: file.created_at,
        type: file.metadata.mimetype || getFileTypeFromName(file.name),
        category: category,
        section: section,
      };
    })
  );
}

// 获取教资资源类型配置的辅助函数
function getTeacherResourceConfig(id: string) {
  return TEACHER_RESOURCE_CONFIGS.find((config) => config.id === id);
}

// 解析嵌套路径参数
function parseNestedPath(pathParam: string): string[] {
  if (!pathParam) return [];
  // 支持用 | 分隔的多级路径，例如: "科目三|初中|数学|真题"
  return pathParam.split("|").map(decodeURIComponent);
}

// GET请求处理函数 - 获取所有学习资源
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const resourceType = searchParams.get("type") || "kaoyan1"; // 默认使用考研英语一作为默认类型
    const section = searchParams.get("section"); // 获取section参数
    const nestedPath = searchParams.get("nestedPath"); // 获取嵌套路径参数

    let resources: Resource[] = [];

    // 首先检查是否是教资资源类型
    const teacherConfig = getTeacherResourceConfig(resourceType);
    if (teacherConfig) {
      // 处理教资资源
      if (!teacherConfig.needsApi) {
        return NextResponse.json({ resources: [] });
      }

      const categoryName = TYPE_CATEGORY_MAP[resourceType];
      if (!categoryName) {
        return NextResponse.json(
          { error: "不支持的教资资源类型" },
          { status: 400 }
        );
      }

      // 获取嵌套配置
      const nestedConfig = teacherConfig.nestedSections;
      if (!nestedConfig) {
        return NextResponse.json(
          { error: "该教资类型没有配置嵌套结构" },
          { status: 400 }
        );
      }

      // 如果提供了嵌套路径，使用嵌套路径
      if (nestedPath) {
        const pathArray = parseNestedPath(nestedPath);
        const storagePath = getNestedPath(nestedConfig, pathArray);

        if (storagePath) {
          try {
            const sectionName = pathArray.join(" > ");
            const sectionResources = await getResources(
              storagePath,
              categoryName,
              sectionName
            );
            resources = [...resources, ...sectionResources];
          } catch (error) {
            console.error(
              `获取${resourceType}的嵌套路径${nestedPath}资源失败:`,
              error
            );
          }
        }
      } else if (section) {
        // 兼容旧的section模式，尝试在第一级查找
        const firstLevelKeys = Object.keys(nestedConfig);
        if (firstLevelKeys.includes(section)) {
          const sectionConfig = nestedConfig[section];

          if (typeof sectionConfig === "string") {
            // 直接是路径
            try {
              const sectionResources = await getResources(
                sectionConfig,
                categoryName,
                section
              );
              resources = [...resources, ...sectionResources];
            } catch (error) {
              console.error(`获取${resourceType}的${section}资源失败:`, error);
            }
          } else if (typeof sectionConfig === "object") {
            // 是嵌套对象，获取所有子路径的资源
            const getAllNestedPaths = (
              config: NestedSectionConfig,
              parentPath: string[] = []
            ): Array<{ path: string; fullPath: string[] }> => {
              const paths: Array<{ path: string; fullPath: string[] }> = [];

              for (const [key, value] of Object.entries(config)) {
                const currentPath = [...parentPath, key];

                if (typeof value === "string") {
                  paths.push({ path: value, fullPath: currentPath });
                } else if (typeof value === "object") {
                  paths.push(...getAllNestedPaths(value, currentPath));
                }
              }

              return paths;
            };

            const allPaths = getAllNestedPaths(sectionConfig, [section]);

            for (const { path, fullPath } of allPaths) {
              try {
                const sectionName = fullPath.join(" > ");
                const sectionResources = await getResources(
                  path,
                  categoryName,
                  sectionName
                );
                resources = [...resources, ...sectionResources];
              } catch (error) {
                console.error(`获取${resourceType}的${path}资源失败:`, error);
              }
            }
          }
        }
      } else {
        // 如果没有指定section或nestedPath，返回第一级的所有可用选项信息
        // 这里可以返回空资源，让前端显示选择界面
        return NextResponse.json({
          resources: [],
          availableOptions: Object.keys(nestedConfig),
        });
      }

      // 添加缓存控制头，防止客户端过度缓存
      return NextResponse.json(
        { resources },
        {
          headers: {
            "Cache-Control": "no-cache, no-store, must-revalidate",
            Pragma: "no-cache",
            Expires: "0",
          },
        }
      );
    }

    // 处理常规资源类型
    const resourceConfig = getResourceTypeConfig(resourceType);
    if (!resourceConfig) {
      return NextResponse.json({ error: "不支持的资源类型" }, { status: 400 });
    }

    // 如果不需要API调用，直接返回空资源
    if (!resourceConfig.needsApi) {
      return NextResponse.json({ resources: [] });
    }

    // 获取对应类型的配置
    const typeConfig = RESOURCE_PATHS[resourceType];
    const categoryName = TYPE_CATEGORY_MAP[resourceType];

    if (!typeConfig || !categoryName) {
      return NextResponse.json({ error: "不支持的资源类型" }, { status: 400 });
    }

    // 遍历该类型下的所有section
    for (const [sectionName, path] of Object.entries(typeConfig)) {
      // 如果指定了section，只获取指定section的资源
      if (section && section !== sectionName) {
        continue;
      }

      try {
        const sectionResources = await getResources(
          path,
          categoryName,
          sectionName
        );
        resources = [...resources, ...sectionResources];
      } catch (error) {
        console.error(`获取${resourceType}的${sectionName}资源失败:`, error);
        // 继续处理其他section，不因为单个section失败而中断
      }
    }

    // 添加缓存控制头，防止客户端过度缓存
    return NextResponse.json(
      { resources },
      {
        headers: {
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
      }
    );
  } catch (error) {
    console.error("获取学习资源出错:", error);
    return NextResponse.json({ error: "获取资源出错" }, { status: 500 });
  }
}
